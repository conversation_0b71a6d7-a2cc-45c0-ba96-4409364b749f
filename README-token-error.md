# Token过期错误处理功能

## 功能说明

当用户访问简化阅读器页面时，如果token无效或过期，系统会自动跳转到一个友好的错误提示页面，提示用户关闭当前网页。

## 实现方案

### 1. 错误处理逻辑

在 `src/views/reader/simplified.vue` 中：

```javascript
async function getBookByToken(redisToken) {
  try {
    const response = await getBookByRedisToken(redisToken);
    if (response.code !== 200) {
      throw new Error(response.msg || "链接无效");
    }
    return response.data; // { bookId, chapterId }
  } catch (error) {
    // 发送错误消息给父窗口
    sendErrorMessage(error.message || "链接无效");
    
    // 直接跳转到错误页面
    proxy.$router.push('/error/token-expired');
    return null;
  }
}
```

### 2. 错误页面

创建了 `src/views/error/TokenExpired.vue` 页面，包含：

- 警告图标和友好的错误提示
- "尝试关闭页面" 按钮
- "重新加载" 按钮
- 响应式设计，适配移动端

### 3. 路由配置

在 `src/router/index.js` 中添加了路由：

```javascript
{
  path: '/error/token-expired',
  component: () => import('@/views/error/TokenExpired'),
  hidden: true,
}
```

## 测试方法

### 方法1：直接访问错误页面
```
http://localhost:3000/error/token-expired
```

### 方法2：使用无效token访问简化阅读器
```
http://localhost:3000/reader/simplified?token=invalid_token&operationFlag=true
```

### 方法3：使用测试页面
打开 `test-token-error.html` 文件，点击测试按钮。

## 用户体验

1. **简洁明了**：直接显示错误信息，不会有复杂的弹窗
2. **操作明确**：提供明确的操作按钮
3. **友好提示**：使用易懂的文字和图标
4. **响应式**：在不同设备上都有良好的显示效果

## 技术特点

- **简化处理**：移除了复杂的多层错误处理逻辑
- **直接跳转**：token无效时直接跳转到错误页面
- **Vue组件**：使用Vue组件实现，便于维护和扩展
- **路由管理**：通过Vue Router管理页面跳转

## 浏览器兼容性

- 支持所有现代浏览器
- 移动端友好
- 无需特殊权限或API

## 部署说明

1. 确保错误页面组件已正确创建
2. 确保路由配置已添加
3. 测试不同场景下的错误处理
4. 验证页面样式在不同设备上的显示效果
