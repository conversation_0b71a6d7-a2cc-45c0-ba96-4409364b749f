<template>
  <router-view />
</template>

<script setup>
import useSettingsStore from '@/store/modules/settings';
import { handleThemeStyle } from '@/utils/theme';
import useSiteStore from '@/store/modules/site';
import useSiteConfigStore from '@/store/modules/siteConfig';
onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme);
    // 获取站点信息
    useSiteStore().getSiteInfo();
    // 获取菜单
    useSiteStore().getMenuInfo();
    // 获取菜单
    useSiteStore().doGetPublicKey();
    // 获取专业层次分类拉平数据
    useSiteStore().getSubjectList();
    // 获取网站配置信息
    useSiteConfigStore().getSiteConfigInfo();
  });
});
</script>

<style lang="scss"></style>
