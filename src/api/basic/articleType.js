import request from '@/utils/request'

// 查询文章类型列表
export function listArticleType(query) {
  return request({
    url: '/basic/articleType/listEducation',
    method: 'get',
    params: query
  })
}

// 查询文章类型详细
export function getArticleType(typeId) {
  return request({
    url: '/basic/articleType/' + typeId,
    method: 'get'
  })
}

// 新增文章类型
export function addArticleType(data) {
  return request({
    url: '/basic/articleType',
    method: 'post',
    data: data
  })
}

// 修改文章类型
export function updateArticleType(data) {
  return request({
    url: '/basic/articleType',
    method: 'put',
    data: data
  })
}

// 删除文章类型
export function delArticleType(typeId) {
  return request({
    url: '/basic/articleType/' + typeId,
    method: 'delete'
  })
}
