import request from '@/utils/request'

// 查询友情链接列表
export function bookAreaList(query){
    return request({
        url:'/basic/bookArea/listEducation',
        method:'get',
        params:query
    })
}

// 查询友情链接详情
export function getBookArea(areaId){
    return request({
        url:'/basic/bookArea/getInfo/' + areaId,
        method:'get',
    })
}
// 导入友情链接
export function importBookArea(data) {
    return request({
        url: '/basic/bookArea/import',
        method: 'post',
        data: data
    })
}

// 新增友情链接
export function addBookArea(data){
    return request({
        url:'/basic/bookArea',
        method:'post',
        data:data
    })
}

// 修改友情链接
export function editBookArea(data){
    return request({
        url:'/basic/bookArea',
        method:'put',
        data:data
    })
}

//删除友情链接
export function deleteBookArea(areaId){
    return request({
        url:'/basic/bookArea/' + areaId,
        method:'delete',
    })
}