/* 友情链接 */
import request from '@/utils/request.js';

export function getFriends() {
  return request({
     url: '/basic/openApi/friends',
      method: 'get'
  });
}

export function getBanners(query) {
  return request({
     url: '/basic/openApi/banners',
      method: 'get',
      params: query
  });
}

export function getPartners(limit) {
  return request({
     url: '/basic/openApi/partners/' + limit,
      method: 'get'
  });
}

export function getSiteInfoById(siteId) {
  return request({
    url: '/cms/openApi/getSiteInfoById/' + siteId,
    method: 'get',
  });
}

export function getMenus() {
  return request({
     url: '/cms/openApi/menus',
      method: 'get'
  });
}