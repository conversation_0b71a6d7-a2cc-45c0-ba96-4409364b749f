import request from '@/utils/request'

// 查询数字教材简介详细
export function getBookAttribute(bookId) {
  return request({
    url: '/book/bookAttribute/' + bookId,
    method: 'get'
  })
}

// 新增数字教材简介
export function addBookAttribute(data) {
  return request({
    url: '/book/bookAttribute',
    method: 'post',
    data: data
  })
}

// 修改数字教材简介
export function updateBookAttribute(data) {
  return request({
    url: '/book/bookAttribute',
    method: 'put',
    data: data
  })
}
// 获取数字教材简介
export function getRecommendedTextbooks(data) {
  return request({
    url: '/book/bookAttribute/getRecommendedTextbooks',
    method: 'post',
    data: data
  })
}
