import request from '@/utils/request'

// 查询数字教材章节目录列表
export function listChapter(query) {
  return request({
    url: '/book/chapter/list',
    method: 'get',
    params: query
  })
}

// 查询数字教材章节目录列表
export function listForSort(query) {
  return request({
    url: '/book/chapter/listForSort',
    method: 'get',
    params: query
  })
}

// 查询数字教材章节目录列表 为了下拉
export function listForSelect(query) {
  return request({
    url: '/book/chapter/listForSelect',
    method: 'get',
    params: query
  })
}

// 查询数字教材章节目录详细
export function getChapter(chapterId) {
  return request({
    url: '/book/chapter/' + chapterId,
    method: 'get'
  })
}

// 查询数字教材章节回收站列表
export function listForRecycle(query) {
  return request({
    url: '/book/chapter/listForRecycle',
    method: 'get',
    params: query
  })
}


// 新增数字教材章节目录
export function addChapter(data) {
  return request({
    url: '/book/chapter',
    method: 'post',
    data: data
  })
}

// 修改数字教材章节目录
export function updateChapter(data) {
  return request({
    url: '/book/chapter',
    method: 'put',
    data: data
  })
}

// 修改数字教材章节目录
export function updateChapterInfo(data) {
  return request({
    url: '/book/chapter/info',
    method: 'put',
    data: data
  })
}

// 恢复数字教材章节目录
export function recycleChapter(data) {
  return request({
    url: '/book/chapter/recycleChapter',
    method: 'put',
    data: data
  })
}

// 更新数字教材章节目录顺序
export function updateChapterSort(data) {
  return request({
    url: '/book/chapter/updateChapterSort',
    method: 'put',
    data: data
  })
}


// 删除数字教材章节目录
export function delChapter(chapterId) {
  return request({
    url: '/book/chapter/' + chapterId,
    method: 'delete'
  })
}

// 查询数字教材章节回收站列表
export function queryBookChapterList(query) {
  return request({
    url: '/book/chapter/queryBookChapterList',
    method: 'get',
    params: query
  })
}


// 查询数字教材章节回收站列表
export function queryBookChapterListByBookDetail(query) {
  return request({
    url: '/book/chapter/queryBookChapterListByBookDetail',
    method: 'get',
    params: query
  })
}