import request from '@/utils/request'

// 查询DUTP-DTB_015收藏教材列表
export function listCollect(query) {
  return request({
    url: '/book/collect/list',
    method: 'get',
    params: query
  })
}

// 查询DUTP-DTB_015收藏教材详细
export function getCollect(userId) {
  return request({
    url: '/book/collect/' + userId,
    method: 'get'
  })
}

// 新增DUTP-DTB_015收藏教材
export function addCollect(data) {
  return request({
    url: '/book/collect',
    method: 'post',
    data: data
  })
}

// 修改DUTP-DTB_015收藏教材
export function updateCollect(data) {
  return request({
    url: '/book/collect',
    method: 'put',
    data: data
  })
}

// 删除DUTP-DTB_015收藏教材
export function delCollect(userId) {
  return request({
    url: '/book/collect/' + userId,
    method: 'delete'
  })
}
// 新增DUTP-DTB_015收藏教材
export function addDtbBookCollect(bookId) {
  return request({
    url: '/book/collect/addDtbUserBookCollect',
    method: 'get',
    params: { bookId: bookId }
  })
}

// 删除DUTP-DTB_015收藏教材
export function delDtbBookCollect(bookId) {
  return request({
    url: '/book/collect/delDtbUserBookCollect',
    method: 'get',
    params: { bookId: bookId }
  })
}


