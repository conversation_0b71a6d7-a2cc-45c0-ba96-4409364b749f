import request from '@/utils/request'

// 查询DUTP-DTB_018足迹列表
export function listPrint(query) {
  return request({
    url: '/book/print/list',
    method: 'get',
    params: query
  })
}

// 查询DUTP-DTB_018足迹详细
export function getPrint(footPrintId) {
  return request({
    url: '/book/print/' + footPrintId,
    method: 'get'
  })
}

// 新增DUTP-DTB_018足迹
export function addPrint(data) {
  return request({
    url: '/book/print',
    method: 'post',
    data: data
  })
}

// 修改DUTP-DTB_018足迹
export function updatePrint(data) {
  return request({
    url: '/book/print',
    method: 'put',
    data: data
  })
}

// 删除DUTP-DTB_018足迹
export function delPrint(footPrintId) {
  return request({
    url: '/book/print/' + footPrintId,
    method: 'delete'
  })
}
