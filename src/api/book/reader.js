import request from '@/utils/request'

// 查询电子教材版本列表
export function startRead(bookId) {
  return request({
    url: '/book/readerApi/startRead',
    method: 'get',
    params: {
      id: bookId
    }
  })
}
// 获取编辑器配置的模版信息
export function getInfoByChapterId(chapterId) {
  return request({
    url: `/book/template/getInfoByChapterId/${chapterId}`,
    method: 'get',
  })
}

/**
 * 获取关联教材*/
export function getBooks(bookId) {
  return request({
    url: '/book/readerApi/getBooks/' + bookId,
    method: 'get',
  })
}

export function getContent(query) {
  return request({
    url: '/book/readerApi/getChapterContent',
    method: 'get',
    params: query
  })
}

export function getContentSimple(query) {
  return request({
    url: '/book/readerApi/getChapterContentSimple',
    method: 'get',
    params: query
  })
}

export function saveBookConfig(data) {
  return request({
    url: '/book/readerApi/saveBookConfig',
    method: 'post',
    data
  })
}

export function getChapters(bookId) {
  return request({
    url: '/book/readerApi/getChapter/' + bookId,
    method: 'get'
  })
}

/**
 * 简版阅读器使用
 * @param bookId
 * @returns {*}
 */
export function getChaptersSimple(bookId,fromType) {
  if (!fromType){
    fromType = 1
  }
  return request({
    url: '/book/readerApi/getChaptersSimple/' + bookId+ '/' + fromType,
    method: 'get'
  })
}


/**
 * 保存用户阅读信息
*/
export function saveBookRead(data) {
  return request({
    url: '/book/readerApi/saveBookRead/',
    method: 'post',
    data
  })
}

/**添加书签*/
export function saveBookMark(data) {
  return request({
    url: '/book/readerApi/saveBookMark/',
    method: 'post',
    data
  })
}
/**查询书签列表*/
export function getBookMark(bookId) {
  return request({
    url: '/book/readerApi/getBookMark/' + bookId,
    method: 'get',
  })
}

/**删除书签*/
export function removeBookMark(markId) {
  return request({
    url: '/book/readerApi/deleteBookMark/' + markId,
    method: 'post',
  })
}

/**添加划线*/
export function saveBookLine(data) {
  return request({
    url: '/book/readerApi/saveBookLine/',
    method: 'post',
    data
  })
}

/**获取划线列表*/
export function getBookLine(params) {
  return request({
    url: '/book/readerApi/getBookLine/',
    method: 'get',
    params
  })
}

/**
 * 删除划线
*/
export function deleteBookLine(params) {
  return request({
    url: '/book/readerApi/deleteBookLine/',
    method: 'get',
    params
  })
}

/**
 * 获取资源列表
 * bookId=1&chapterId=1851170583426600961&resourceType=0 resourceType 1==实训  0===资源
*/
export function getBookResource(params) {
  return request({
    url: '/book/readerApi/getBookResourceByChapter',
    method: 'get',
    params
  })
}
/**
 * 复制文字
*/
export function bookCopyText(params) {
  return request({
    url: '/book/readerApi/bookCopyText/',
    method: 'get',
    params
  })
}

/**
 * 查询笔记列表
*/
export function getBookNote(params) {
  return request({
    url: '/book/readerApi/getBookNote/',
    method: 'get',
    params
  })
}

/**
 * 保存笔记
*/
export function saveBookNote(data) {
  return request({
    url: '/book/readerApi/saveBookNote/',
    method: 'post',
    data
  })
}
/**
 * 修改笔记
*/
export function updateBookNote(data) {
  return request({
    url: '/book/readerApi/updateBookNote/',
    method: 'post',
    data
  })
}

/**
 * 删除笔记
*/
export function deleteBookNote(noteId) {
  return request({
    url: '/book/readerApi/deleteBookNote/' + noteId,
    method: 'delete',
  })
}

/**
 * 保存用户纠错
*/
export function saveBookFault(data) {
  return request({
    url: '/book/readerApi/saveBookFault/',
    method: 'post',
    data
  })
}

export function updateReadTime(readId, readTime) {
  return request({
    url: '/book/readerApi/updateReadTime/' + readId + '/' + readTime,
    method: 'get'
  })
}

export function addQuestionAnswer(data) {
  return request({
    url: '/book/questionAnswer',
    method: 'post',
    data: data
  })
}


export function editQuestionAnswer(data) {
  return request({
    url: '/book/questionAnswer',
    method: 'put',
    data: data
  })
}

// 获取试卷的题目组和题目信息
export function getTestPaperQuestions(paperId) {
  return request({
    url: '/book/paper/questions/' + paperId,
    method: 'get'
  })
}

//提交试卷-考试答案
export function addPapersAnswer(data) {
  return request({
    url: '/book/bookPaper',
    method: 'post',
    data: data
  })
}

//获取题的题干
export function getQuestionStem(data) {
  console.log(data)
  return request({
    url: '/book/userQuestion/getUserAnswerInfo/' ,
    method: 'get',
    params: data
  })
}

//获取试卷和作业上次提交的答案
export function getPaperAnswer(data) {
  return request({
    url: '/book/bookPaper/getPaperAnswer',
    method: 'get',
    params: data
  })
}
//获取试卷和作业上列表
export function getPaperList(data) {
  return request({
    url: '/book/bookPaper/getPaperList',
    method: 'get',
    params: data
  })
}


// 搜索
export function getQueryChapterContent(data) {
  return request({
    url: '/book/readerApi/getQueryChapterContent',
    method: 'get',
    params: data
  })
}


// 导出标记
export function exportBookMark(data) {
  return request({
    url: '/book/readerApi/exportBookLine?bookId='+ data,
    method: 'post',
    responseType: 'blob',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  })
}

export function getReadingWithExtendKnowledge(keyWord) {
  return request({
    url: '/book/chatAi/chatAi',
    method: 'post',
    data: {
      ability: 26,
      question: keyWord,
      userType: 0
    }
  })
}

export function exportNotes({ bookId, chapterId, sort }) {
  return request({
    url: '/book/readerApi/exportBookNote',
    method: 'post',
    responseType: 'blob',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    params: {
      bookId,
      chapterId
    }
  })
}

// 更新视频时长
export function updateVideoTime(data) {
  return request({
    url: '/book/readerApi/saveBookResourceLog',
    method: 'post',
    data: data
  })
}

// 预览心理测试量表详情
export function getPhychology(scaleId) {
  return request({
    url: '/book/moocPsychologyHealth/getPsychology/' + scaleId,
    method: 'get'
  })
}

// 教材分享部分

/*
 * 检查是否设置了分享码
 */
export function isShareCode(shareId) {
  return request({
    url: '/book/readerApi/getIsShareCode/' + shareId,
    method: 'get'
  })
}

export function checkShareCode(data) {
  return request({
    url: '/book/readerApi/checkShareCode',
    method: 'post',
    data
  })
}

/**
 * 教材分享阅读器
 * @param shareId
 * @returns {*}
 */
export function getChaptersSimpleBook(shareId) {
  return request({
    url: '/book/readerApi/getChaptersSimpleBook/' + shareId,
    method: 'get'
  })
}

/**
 * 查询互评列表
 */
export function getShareComment(params) {
  return request({
    url: '/book/readerApi/getShareComment/',
    method: 'get',
    params
  })
}

/**
 * 保存互评
 */
export function saveShareComment(data) {
  return request({
    url: '/book/readerApi/saveShareComment/',
    method: 'post',
    data
  })
}
/**
 * 修改互评
 */
export function updateShareComment(data) {
  return request({
    url: '/book/readerApi/updateShareComment/',
    method: 'post',
    data
  })
}

/**
 * 删除互评
 */
export function deleteShareComment(noteId) {
  return request({
    url: '/book/readerApi/deleteShareComment/' + noteId,
    method: 'delete',
  })
}

export function getBookByRedisToken(redisToken) {
  return request({
    url: '/book/readerApi/getBookByRedisToken/' + redisToken,
    method: 'get',
  })
}


