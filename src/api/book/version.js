import request from '@/utils/request'

// 查询电子教材版本列表
export function listVersion(query) {
  return request({
    url: '/book/version/list',
    method: 'get',
    params: query
  })
}

// 查询电子教材版本详细
export function getVersion(versionId) {
  return request({
    url: '/book/version/getInfoEducation/' + versionId,
    method: 'get'
  })
}

// 新增电子教材版本
export function addVersion(data) {
  return request({
    url: '/book/version',
    method: 'post',
    data: data
  })
}

// 修改电子教材版本
export function updateVersion(data) {
  return request({
    url: '/book/version',
    method: 'put',
    data: data
  })
}

// 删除电子教材版本
export function delVersion(versionId) {
  return request({
    url: '/book/version/' + versionId,
    method: 'delete'
  })
}
