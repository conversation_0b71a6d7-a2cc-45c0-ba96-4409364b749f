import request from '@/utils/request'

// 查询教师认证列表
export function listAuthentication(query) {
  return request({
    url: '/edu/authentication/listEducation',
    method: 'get',
    params: query
  })
}

// 查询教师认证详细
export function getAuthentication(authId) {
  return request({
    url: '/edu/authentication/' + authId,
    method: 'get'
  })
}

// 新增教师认证
export function addAuthentication(data) {
  return request({
    url: '/edu/authentication/addEducation',
    method: 'post',
    data: data
  })
}

// 修改教师认证
export function updateAuthentication(data) {
  return request({
    url: '/edu/authentication',
    method: 'put',
    data: data
  })
}

// 删除教师认证
export function delAuthentication(authId) {
  return request({
    url: '/edu/authentication/' + authId,
    method: 'delete'
  })
}
