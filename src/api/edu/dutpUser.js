import request from '@/utils/request'

// 查询DUTP-BASE-001用户【教师，学生，无身份者】列表
export function listUser(query) {
  return request({
    url: '/edu/userInfo/list',
    method: 'get',
    params: query
  })
}

// 查询DUTP-BASE-001用户【教师，学生，无身份者】详细
export function getUser() {
  return request({
    url: '/edu/userInfo/getInfoEducation',
    method: 'get'
  })
}

// 新增DUTP-BASE-001用户【教师，学生，无身份者】
export function addUser(data) {
  return request({
    url: '/edu/userInfo',
    method: 'post',
    data: data
  })
}

// 修改DUTP-BASE-001用户【教师，学生，无身份者】
export function updateUser(data) {
  return request({
    url: '/edu/userInfo/editUserEducation',
    method: 'put',
    data: data
  })
}

// 删除DUTP-BASE-001用户【教师，学生，无身份者】
export function cancelAccount(data) {
  return request({
    url: '/edu/userInfo/removeUser',
    method: 'post',
    data: data
  })
}
