import request from "@/utils/request";

let prefix = "/file";

// 获取sts临时凭证
export const getStsToken = () =>
  request({
    url: prefix + "/getStsToken",
    method: "get",
  });

// 上传文件
export const uploadFile = (formData) =>
  request({
    url: prefix + "/upload",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
      repeatSubmit: false,
    },
  });
