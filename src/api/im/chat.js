import request from '@/utils/request'
// 发送消息
export function sendMes(data) {
    return request({
        url: '/im/detail/send',
        method: 'post',
        data: data
    })
}

// 获取聊天记录
export function getDetailList(data) {
    return request({
        url: '/im/detail/getMessage',
        method: 'post',
        data: data
    })
}

// 初始化聊天
export function initChatDetail(data) {
    return request({
        url: '/im/detail/checkUserChat',
        method: 'post',
        data: data
    })
}

// 修改已读未读
export function changeMessageState(data) {
    return request({
        url: '/im/detail/changeReadStatus',
        method: 'post',
        data: data
    })
}

