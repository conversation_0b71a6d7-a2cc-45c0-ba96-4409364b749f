import request from '@/utils/request'

// 登录方法
export function login(data) {
  return request({
    url: '/auth/loginEducation',
    headers: {
      isToken: false,
      repeatSubmit: false,
      Language: localStorage.getItem('Language')
    },
    method: 'post',
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/auth/registerEducation',
    headers: {
      isToken: false,
      Language: localStorage.getItem('Language')
    },
    method: 'post',
    data: data
  })
}

// 刷新方法
export function refreshToken() {
  return request({
    url: '/auth/refresh',
    method: 'post'
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/system/dutpUser/getInfo',
    headers: {
      Language: localStorage.getItem('Language')
    },
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/auth/logoutEducation',
    headers: {
      Language: localStorage.getItem('Language')
    },
    method: 'delete'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/code',
    headers: {
      isToken: false,
      Language: localStorage.getItem('Language')
    },
    method: 'get',
    timeout: 20000
  })
}

// 发送验证码
export function sendAliyun(data) {
  return request({
    url: '/system/dutpUser/sendAliyun',
    headers: {
      isToken: false,
      Language: localStorage.getItem('Language')
    },
    method: 'post',
    data: data
  })
}
// 验证码登录方法
export function codeLogin(data) {
  return request({
      url: '/auth/codeLoginEducation',
      headers: {
          isToken: false,
          repeatSubmit: false,
          Language: localStorage.getItem('Language')
      },
      method: 'post',
      data: data
  })
}
// 注册方法
export function forgotPasswordEducation(data) {
  return request({
    url: '/auth/forgotPasswordEducation',
    headers: {
      isToken: false,
      Language: localStorage.getItem('Language')
    },
    method: 'post',
    data: data
  })
}
// 二维码创建
export function createQrCode(qrLogUrl) {
  return request({
    url: '/auth/createQrCode?qrLogUrl=' + qrLogUrl,
    headers: {
      isToken: false,
      Language: localStorage.getItem('Language')
    },
    method: 'get'
  })
}
// 二维码查询返回结果
export function queryIsScannedOrVerified(img) {
  return request({
    url: '/auth/queryIsScannedOrVerified',
    headers: {
      isToken: false,
      Language: localStorage.getItem('Language')
    },
    data:{ img: img },
    method: 'post',
  })
}