import request from '@/utils/request'

// 查询DUTP-DTB_016读者反馈/纠错列表
export function listFeedback(query) {
  return request({
    url: '/message/feedback/list',
    method: 'get',
    params: query
  })
}

// 查询DUTP-DTB_016读者反馈/纠错详细
export function getFeedback(feedBackId) {
  return request({
    url: '/message/feedback/' + feedBackId,
    method: 'get'
  })
}

// 新增DUTP-DTB_016读者反馈/纠错
export function addFeedback(data) {
  return request({
    url: '/message/feedback',
    method: 'post',
    data: data
  })
}

// 修改DUTP-DTB_016读者反馈/纠错
export function updateFeedback(data) {
  return request({
    url: '/message/feedback',
    method: 'put',
    data: data
  })
}

// 删除DUTP-DTB_016读者反馈/纠错
export function delFeedback(feedBackId) {
  return request({
    url: '/message/feedback/' + feedBackId,
    method: 'delete'
  })
}
