import request from '@/utils/request'

// 学生教师端查询消息
export function getMessage(query) {
  return request({
    url: '/message/message/getMessage',
    method: 'get',
    params: query
  })
}
// 学生教师端修改消息已读状态
export function editBatch(data) {
  return request({
    url: '/message/message/editBatch',
    method: 'put',
    data: data
  })
}
// 学生教师端删除用户消息
export function removeBatch(data) {
  return request({
    url: '/message/message/removeBatch',
    method: 'post',
    data: data
  })
}
// 下拉框TITLE查询
export function getSelTitle() {
  return request({
    url: '/message/message/getSelTitle',
    method: 'get'
  })
}