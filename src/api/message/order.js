import request from '@/utils/request'

// 查询DUTP-BASE-018反馈工单列表
export function listOrder(query) {
  return request({
    url: '/message/order/list',
    method: 'get',
    params: query
  })
}

// 查询DUTP-BASE-018反馈工单详细
export function getOrder(ticketId) {
  return request({
    url: '/message/order/' + ticketId,
    method: 'get'
  })
}

// 新增DUTP-BASE-018反馈工单
export function addOrder(data) {
  return request({
    url: '/message/order',
    method: 'post',
    data: data
  })
}

// 修改DUTP-BASE-018反馈工单
export function updateOrder(data) {
  return request({
    url: '/message/order',
    method: 'put',
    data: data
  })
}

// 删除DUTP-BASE-018反馈工单
export function delOrder(ticketId) {
  return request({
    url: '/message/order/' + ticketId,
    method: 'delete'
  })
}
