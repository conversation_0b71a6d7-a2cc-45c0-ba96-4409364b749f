import request from '@/utils/request'
import { getToken } from '@/utils/auth';

// 查询DUTP-DTB_015收藏教材列表
export function miSearchEducation(query) {
  return request({
    url: '/book/openApi/miSearchEducation',
    method: 'get',
    params: query
  })
}

// 查询DUTP-DTB_015收藏教材列表
export function listBookTypeEducation(query) {
  return request({
    url: '/book/openApi/listBookTypeEducation',
    method: 'get',
    params: query
  })
}
// 查询投稿邮息列表
export function listEmail(query) {
  return request({
    url: '/message/openApi/listEmail',
    method: 'get',
    params: query
  })
}
// 查询投稿邮息列表
export function listArticleTypeEducation(query) {
  return request({
    url: '/basic/openApi/listArticleTypeEducation',
    method: 'get',
    params: query
  })
}
// 新增系统访问日志
export function addVisitLog(data) {
  return request({
    url: '/system/openApi/addVisitLog',
    method: 'post',
    data
  })
}

// 查询教材
export function getDtbBookInfo(params) {
  const token = getToken();
  return request({
    url: '/book/openApi/getDtbBookInfo',
    method: 'get',
    params: params,
    headers: {
      Authorization: `Bearer ${token}` // 将 token 添加到请求头
    }
  });
}
// 查询数字教材章节回收站列表
export function queryBookChapterListByBookDetail(query) {
  return request({
    url: '/book/openApi/queryBookChapterListByBookDetail',
    method: 'get',
    params: query
  })
}
// 查询数字教材章节回收站列表
export function homepageChapterSearch(query) {
  return request({
    url: '/book/openApi/homepageChapterSearch',
    method: 'get',
    params: query
  })
}
// 修改数字教材简介
export function getRecommendedTextbooks(data) {
  return request({
    url: '/book/openApi/getRecommendedTextbooks',
    method: 'post',
    data: data
  })
}


//  使用AI执行代码块
export function chartAi(data) {
  return request({
    url: '/book/chatAi/chatAi',
    method: 'post',
    data: data
  })
}

export function getLanguageList() {
  return request({
    url: '/book/aiTranslation/language/list',
    method: 'get',
  })
}


// 查询AI助教使用次数
export function getAiAssistantUseCount(data) {
  return request({
    url: '/book/chatAi/getAiExperiment',
    method: 'get',
    params: data
  })
}

