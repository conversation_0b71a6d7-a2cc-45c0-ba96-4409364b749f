import request from '@/utils/request';

// 学生教师端查询发票列表
export function getInfoEducation(data) {
  return request({
    url: '/shop/apply/getInfoEducation',
    method: 'post',
    data: data
  })
}
// 学生教师端修改发票列表
export function editEducation(data) {
  return request({
    url: '/shop/apply/editEducation',
    method: 'put',
    data: data
  })
}
// 学生教师端新增发票
export function addInvoiceApplyEducation(data) {
  return request({
    url: '/shop/apply/addInvoiceApplyEducation',
    method: 'post',
    data: data
  })
}
