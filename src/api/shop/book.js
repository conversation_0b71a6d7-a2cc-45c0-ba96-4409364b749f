import request from '@/utils/request';

// 查询DUTP-DTB_002数字教材列表
export function listByQuery(query) {
  return request({
    url: '/shop/openApi/listByQuery',
    method: 'get',
    params: query,
  });
}
// 查询购买教材

export function getShopBook(query) {
  return request({
    url: '/shop/openApi/getShopBook',
    method: 'get',
    params: query,
  });
}
export function getInfoByBookId(bookId) {
  return request({
    url: '/shop/book/getInfoByBookId/' + bookId,
    method: 'get'
  });
}