import request from '@/utils/request'

// 查询订单下的购书码列表
export function listCode(query) {
  return request({
    url: '/shop/code/list',
    method: 'get',
    params: query
  })
}

// 查询订单下的购书码详细
export function getCode(orderCodeId) {
  return request({
    url: '/shop/code/' + orderCodeId,
    method: 'get'
  })
}

// 新增订单下的购书码
export function addCode(data) {
  return request({
    url: '/shop/code',
    method: 'post',
    data: data
  })
}

// 修改订单下的购书码
export function updateCode(data) {
  return request({
    url: '/shop/code',
    method: 'put',
    data: data
  })
}

// 删除订单下的购书码
export function delCode(orderCodeId) {
  return request({
    url: '/shop/code/' + orderCodeId,
    method: 'delete'
  })
}
