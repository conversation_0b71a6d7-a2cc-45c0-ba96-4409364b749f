import request from '@/utils/request'

// 学生教师端查询订单列表
export function getOrderInfo(query) {
  return request({
    url: '/shop/order/getOrderInfo',
    method: 'get',
    params: query
  })
}
// 学生教师端超时取消订单
export function cancelDtbBookOrder(data) {
  return request({
    url: '/shop/order/cancelDtbBookOrder',
    method: 'put',
    data: data
  })
}
// 学生教师端修改订单
export function updateOrder(data) {
  return request({
    url: '/shop/order/updateOrder',
    method: 'put',
    data: data
  })
}