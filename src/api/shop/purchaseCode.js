import request from '@/utils/request'

// 查询购书码发行管理列表
export function listPurchaseCode(query) {
  return request({
    url: '/shop/purchaseCode/listEducation',
    method: 'get',
    params: query
  })
}

// 查询购书码发行管理详细
export function getPurchaseCode(query) {
  return request({
    url: '/shop/purchaseCode/getInfoEducation',
    method: 'get',
    params: query
  })
}

// 新增购书码发行管理
export function addPurchaseCode(data) {
  return request({
    url: '/shop/purchaseCode',
    method: 'post',
    data: data
  })
}

// 修改购书码发行管理
export function updatePurchaseCode(data) {
  return request({
    url: '/shop/purchaseCode',
    method: 'put',
    data: data
  })
}

// 删除购书码发行管理
export function delPurchaseCode(codeId) {
  return request({
    url: '/shop/purchaseCode/' + codeId,
    method: 'delete'
  })
}

// 学生教师端购书码兑换
export function bookCodeExchange(data) {
  return request({
    url: '/shop/purchaseCode/bookCodeExchange',
    method: 'put',
    data: data
  })
}
