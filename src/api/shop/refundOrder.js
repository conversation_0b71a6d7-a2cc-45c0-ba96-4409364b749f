import request from '@/utils/request'

// 查询售后退款订单列表
export function listRefundOrder(query) {
  return request({
    url: '/shop/refundOrder/list',
    method: 'get',
    params: query
  })
}

// 查询售后退款订单详细
export function getRefundOrder(refundOrderId) {
  return request({
    url: '/shop/refundOrder/' + refundOrderId,
    method: 'get'
  })
}

// 新增售后退款订单
export function addRefundOrder(data) {
  return request({
    url: '/shop/refundOrder',
    method: 'post',
    data: data
  })
}

// 修改售后退款订单
export function updateRefundOrder(data) {
  return request({
    url: '/shop/refundOrder',
    method: 'put',
    data: data
  })
}

// 删除售后退款订单
export function delRefundOrder(refundOrderId) {
  return request({
    url: '/shop/refundOrder/' + refundOrderId,
    method: 'delete'
  })
}
