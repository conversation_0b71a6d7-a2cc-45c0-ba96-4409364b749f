import request from '@/utils/request';

// 学生教师端查询发票抬头列表
export function getInvoiceTitleEducation(data) {
  return request({
    url: '/shop/title/getInvoiceTitleEducation',
    method: 'post',
    data: data,
  });
}

// 学生教师端新增发票抬头列表
export function addEducation(data) {
  return request({
    url: '/shop/title/addEducation',
    method: 'post',
    data: data,
  });
}

// 学生教师端修改发票抬头列表
export function editEducation(data) {
  return request({
    url: '/shop/title/editEducation',
    method: 'put',
    data: data
  })
}

// 学生教师端删除发票抬头
export function removeEducation(titleIds) {
  return request({
    url: '/shop/title/removeEducation/' + titleIds,
    method: 'delete'
  })
}