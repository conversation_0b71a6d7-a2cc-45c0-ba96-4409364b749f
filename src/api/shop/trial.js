import request from '@/utils/request'

// 查询DUTP-DTB_025教师试用申请列表
export function listApply(query) {
  return request({
    url: '/shop/trial/listEducation',
    method: 'get',
    params: query
  })
}

// 查询DUTP-DTB_025教师试用申请详细
export function getApply(trialId) {
  return request({
    url: '/shop/trial/' + trialId,
    method: 'get'
  })
}

// 新增DUTP-DTB_025教师试用申请
export function addApply(data) {
  return request({
    url: '/shop/trial',
    method: 'post',
    data: data
  })
}

// 修改DUTP-DTB_025教师试用申请
export function updateApply(data) {
  return request({
    url: '/shop/trial/editEducation',
    method: 'put',
    data: data
  })
}

// 删除DUTP-DTB_025教师试用申请
export function delApply(trialId) {
  return request({
    url: '/shop/trial/' + trialId,
    method: 'delete'
  })
}

// 查询DUTP-DTB_025教师试用申请列表(试用中心)
export function TrialShopList(query) {
  return request({
    url: '/shop/trial/TrialShopList',
    method: 'get',
    params: query
  })
}

// 查询DUTP-DTB_025教师试用申请列表(试用中心)
export function submitTrialApplicationEducation(query) {
  return request({
    url: '/shop/trial/submitTrialApplicationEducation',
    method: 'get',
    params: query
  })
}