import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listFile(query) {
  return request({
    url: '/system/file/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getFile(fileId) {
  return request({
    url: '/system/file/' + fileId,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addFile(data) {
  return request({
    url: '/system/file/addDutpUserCommonFile',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateFile(data) {
  return request({
    url: '/system/file',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delFile(fileId) {
  return request({
    url: '/system/file/' + fileId,
    method: 'delete'
  })
}

// 查询【请填写功能名称】详细
export function getListByIds(fileIds) {
  return request({
    url: '/system/file/getListByIds/' + fileIds,
    method: 'get'
  })
}
// 删除文件
export function deleteFile(data) {
  return request({
    url: '/system/file/deleteDutpUserCommonFile',
    method: 'post',
    data: data
  })
}
