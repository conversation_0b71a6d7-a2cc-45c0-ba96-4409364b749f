import request from '@/utils/request'

// 查询用户黑名单列表
export function listList(query) {
  return request({
    url: '/system/list/list',
    method: 'get',
    params: query
  })
}

// 查询用户黑名单详细
export function getList(blackListId) {
  return request({
    url: '/system/list/' + blackListId,
    method: 'get'
  })
}

// 新增用户黑名单
export function addList(data) {
  return request({
    url: '/system/list',
    method: 'post',
    data: data
  })
}

// 修改用户黑名单
export function updateList(data) {
  return request({
    url: '/system/list',
    method: 'put',
    data: data
  })
}

// 删除用户黑名单
export function delList(blackListId) {
  return request({
    url: '/system/list/' + blackListId,
    method: 'delete'
  })
}

// 获取禁止兑换黑名单详细信息
export function getProhibitionOfExchange(userId) {
  return request({
    url: '/system/list/getProhibitionOfExchange/' + userId,
    method: 'get'
  })
}
// 新增禁止兑换黑名单详细信息
export function addProhibitionOfExchange(data) {
  return request({
    url: '/system/list/addProhibitionOfExchange',
    method: 'post',
    data: data
  })
}
