import request from '@/utils/request'

// 查询购书码兑换记录列表
export function listLog(query) {
  return request({
    url: '/system/log/listEducation',
    method: 'get',
    params: query
  })
}

// 查询购书码兑换记录详细
export function getLog(logId) {
  return request({
    url: '/system/log/' + logId,
    method: 'get'
  })
}

// 新增购书码兑换记录
export function addLog(data) {
  return request({
    url: '/system/log',
    method: 'post',
    data: data
  })
}

// 修改购书码兑换记录
export function updateLog(data) {
  return request({
    url: '/system/log',
    method: 'put',
    data: data
  })
}

// 删除购书码兑换记录
export function delLog(logId) {
  return request({
    url: '/system/log/' + logId,
    method: 'delete'
  })
}
