.login-page-con {
  width: 100vw;
  height: 100vh;
  background-color: rgb(235, 244, 254);
  display: flex;
  display: -webkit-flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  display: -webkit-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.box_2 {
  width: 100vw;
  height: 100vh;
  position: relative;
  display: flex;
  display: -webkit-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.block_1 {
  width: 1920px;
  height: 325px;
  margin-top: 672px;
}

.block_2 {
  position: absolute;
  left: 97px;
  top: -73px;
  width: 2px;
  height: 332px;
  background-size: 100% 100%;
}

.block_3 {
  position: absolute;
  left: 1795px;
  top: -106px;
  width: 2px;
  height: 332px;
  background-size: 100% 100%;
}

.block_5 {
  width: 100vw;
  height: 100vh;
  background: url('../../assets/images/background.png') no-repeat;
  background-size: cover;
  display: flex;
  display: -webkit-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.block_5_1 {
  width: 100vw;
  height: 100vh;
  background: url('../../assets/images/background.png') no-repeat;
  background-size: cover;
  display: flex;
  display: -webkit-flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.group_20 {
  width: 100vw;
  display: flex;
  display: -webkit-flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  border: 1px solid black;
}
.group_20_1 {
  width: 100vw;
  display: flex;
  display: -webkit-flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  padding-top: 50px;
  margin-bottom: 60px;
}

.label_1 {
  width: 230px;
  height: 51px;
}

.text_1 {
  width: 168px;
  height: 28px;
  overflow-wrap: break-word;
  color: rgba(16, 87, 163, 1);
  font-size: 28px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: justify;
  white-space: nowrap;
  line-height: 28px;
  margin: 11px 0 0 13px;
}

.group_1 {
  width: 2px;
  height: 135px;
  background-size: 100% 100%;
  margin: 32px 0 0 71px;
}

.text_2 {
  width: 28px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin: 18px 0 0 81px;
}

.text_3 {
  width: 56px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin: 18px 0 0 40px;
}

.text_4 {
  width: 56px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin: 18px 0 0 40px;
}

.text_5 {
  width: 42px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin: 18px 0 0 40px;
}

.text_6 {
  width: 56px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin: 18px 0 0 40px;
}

.group_2 {
  width: 2px;
  height: 135px;
  background-size: 100% 100%;
  margin: 100px 0 0 10px;
}

.text_7 {
  width: 56px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin: 18px 0 0 28px;
}

.text_8 {
  width: 63px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin: 18px 0 0 40px;
}
.text_8_1 {
  width: 63px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin: 18px 0 0 0px;
  font-size: 20px;
  font-weight: bold;
  color: rgb(0 139 255);
  cursor: pointer;
}

.image-wrapper_6 {
  width: 27px;
  height: 32px;
  margin: 229px 0 140px 1086px;
}

.label_2 {
  width: 27px;
  height: 32px;
}

.box_4 {
  height: 467px;
  background-size: 100% 100%;
  width: 366px;
  position: absolute;
  left: 1588px;
  top: -29px;
}

.image-wrapper_2 {
  height: 423px;
  background-size: 100% 100%;
  margin-top: 29px;
  width: 332px;
}

.image_2 {
  width: 307px;
  height: 382px;
  margin-left: 25px;
}

.box_5 {
  height: 420px;
  background-size: 100% 100%;
  width: 402px;
  position: absolute;
  left: -1px;
  top: 0;
}

.image-wrapper_3 {
  height: 391px;
  background-size: 100% 100%;
  margin-left: 1px;
  width: 318px;
}

.image_3 {
  width: 258px;
  height: 310px;
}

.box_8 {
  position: absolute;
  left: 223px;
  top: 307px;
  width: 2px;
  height: 332px;
  background-size: 100% 100%;
}

.box_9 {
  position: absolute;
  left: 356px;
  top: 456px;
  width: 2px;
  height: 332px;
  background-size: 100% 100%;
}

.box_10 {
  position: absolute;
  left: 1624px;
  top: 404px;
  width: 2px;
  height: 361px;
  background-size: 100% 100%;
}

.box_6 {
  box-shadow: 0px 0px 18px 2px rgba(102, 133, 161, 0.26);
  border-radius: 26px;
  width: 987px;
  height: 617px;
  background-color: #edf6fe;
}

.group_3 {
  position: relative;
  width: 500px;
  height: 617px;
}

.box_7 {
  width: 361px;
  height: 79px;
  background-size: 373px 93px;
  margin: 320px 0 0 70px;
}

.text-group_4 {
  width: 459px;
  height: 74px;
  margin: 54px 0 0 21px;
}

.text_9 {
  width: 144px;
  height: 36px;
  overflow-wrap: break-word;
  color: rgba(0, 102, 177, 1);
  font-size: 36px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: justify;
  white-space: nowrap;
  line-height: 36px;
  margin-left: 157px;
}

.text_10 {
  width: 459px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(62, 120, 162, 1);
  font-size: 16px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 16px;
  margin-top: 22px;
}

.group_21 {
  width: 115px;
  height: 9px;
  margin: 41px 0 40px 193px;
}

.group_13 {
  background-color: rgba(0, 113, 186, 1);
  border-radius: 4px;
  width: 21px;
  height: 9px;
}

.group_14 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 21px;
  height: 9px;
  margin-left: 26px;
}

.group_15 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 21px;
  height: 9px;
  margin-left: 26px;
}

.image-wrapper_4 {
  height: 251px;
  background-size: 100% 100%;
  width: 354px;
  position: absolute;
  left: 74px;
  top: 144px;
}

.label_3 {
  width: 31px;
  height: 23px;
  margin: 19px 0 0 62px;
}

.group_22 {
  width: 388px;
  height: 557px;
  margin: 17px 15px 0 84px;
}

.group_23 {
  width: 179px;
  height: 85px;
  margin-left: 209px;
}

.group_10 {
  width: 97px;
  height: 22px;
  background-size: 98px 22px;
  margin-top: 9px;
}

.thumbnail_3 {
  width: 16px;
  height: 16px;
  margin: 3px 0 0 8px;
}

.text_24 {
  width: 56px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(102, 102, 102, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin: 4px 12px 0 5px;
}

.image_5 {
  width: 179px;
  height: 85px;
  margin-left: -3px;
}

.text_11 {
  width: 144px;
  height: 28px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: justify;
  white-space: nowrap;
  line-height: 28px;
}

.text-wrapper_5 {
  width: 204px;
  height: 18px;
  margin-top: 30px;
}

.text_12 {
  width: 90px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 18px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: justify;
  white-space: nowrap;
  line-height: 18px;
}

.text_13 {
  width: 64px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 16px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 16px;
  margin-top: 1px;
}

.group_5 {
  background-color: rgba(9, 102, 180, 1);
  border-radius: 2px;
  width: 42px;
  height: 4px;
  margin: 8px 0 0 24px;
}

.group_24 {
  width: 111px;
  height: 16px;
  margin: 31px 0 0 12px;
}

.thumbnail_1 {
  width: 11px;
  height: 16px;
}

.text_14 {
  width: 84px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(203, 203, 203, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin-top: 1px;
}

.group_25 {
  width: 296px;
  height: 15px;
  margin: 46px 0 0 12px;
}

.thumbnail_2 {
  width: 13px;
  height: 15px;
}

.text_15 {
  width: 116px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(203, 203, 203, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin-left: 14px;
}

.text_16 {
  width: 70px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin-left: 83px;
}

.text-wrapper_2 {
  width: 108px;
  height: 14px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 14px;
  margin: 115px 0 0 106px;
}

.text_17 {
  width: 108px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 12px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
}

.text_18 {
  width: 108px;
  height: 14px;
  overflow-wrap: break-word;
  color: rgba(7, 113, 188, 1);
  font-size: 12px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 14px;
}
.text-wrapper_3 {
  height: 23px;
  background-size: 100% 100%;
  width: 120px;
  margin: 105px 0 0 26px;
}

.image-text_3 {
  width: 257px;
  height: 12px;
  margin: 7px 0 0 34px;
}

.section_1 {
  border-radius: 2px;
  width: 12px;
  height: 12px;
  border: 1px solid rgba(198, 198, 198, 1);
}

.text-group_2 {
  width: 240px;
  height: 12px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 12px;
}

.text_20 {
  width: 240px;
  height: 12px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 12px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 12px;
}

.text_21 {
  width: 240px;
  height: 12px;
  overflow-wrap: break-word;
  color: rgba(7, 113, 188, 1);
  font-size: 12px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 12px;
}

.text_22 {
  width: 240px;
  height: 12px;
  overflow-wrap: break-word;
  color: rgba(58, 58, 58, 1);
  font-size: 12px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 12px;
}

.text_23 {
  width: 240px;
  height: 12px;
  overflow-wrap: break-word;
  color: rgba(7, 113, 188, 1);
  font-size: 12px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 12px;
}

.group_8 {
  border-radius: 8px;
  position: absolute;
  left: 584px;
  top: 206px;
  width: 320px;
  height: 46px;
  border: 0.6px solid rgba(229, 230, 231, 1);
}

.group_9 {
  border-radius: 8px;
  position: absolute;
  left: 584px;
  top: 267px;
  width: 320px;
  height: 46px;
  border: 0.6px solid rgba(229, 230, 231, 1);
}

.slide-verify-block_2 {
  width: 374px;
  height: 382px;
  margin: 90px 0px 0px -25px;
}
.slide-verify-image_5 {
  width: 21px;
  height: 18px;
}

.slide-verify-box_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 17px;
  width: 374px;
  height: 335px;
  border: 3.349253731343284px solid rgba(229, 230, 231, 1);
  margin-top: 29px;
}

.slide-verify-text_11 {
  width: 180px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: justify;
  white-space: nowrap;
  line-height: 18px;
  margin: 25px 0 0 20px;
}

.slide-verify-box_5 {
  border-radius: 8px;
  width: 329px;
  height: 179px;
  margin: 16px 0 0 22px;
}

.slide-verify-box_6 {
  background-color: rgba(229, 230, 231, 1);
  border-radius: 4px;
  width: 329px;
  height: 38px;
  margin: 12px 0 0 22px;
}

.slide-verify-image-text_1 {
  width: 278px;
  height: 30px;
  margin: 4px 0 0 4px;
}

.slide-verify-image-wrapper_5 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 2px;
  height: 30px;
  width: 45px;
}

.slide-verify-image_6 {
  width: 25px;
  height: 20px;
  margin: 5px 0 0 10px;
}

.slide-verify-text-group_2 {
  width: 224px;
  height: 16px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 16px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 16px;
  margin-top: 7px;
}

.slide-verify-image-text_2 {
  width: 46px;
  height: 18px;
  margin: 50px 0 17px 22px;
}

.slide-verify-thumbnail_1 {
  width: 13px;
  height: 13px;
  margin-top: 3px;
}

.slide-verify-text-group_3 {
  width: 28px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 18px;
}

.retrieve-group_22 {
  width: 388px;
  height: 557px;
  margin: 15px 15px 0 -40px;
}
