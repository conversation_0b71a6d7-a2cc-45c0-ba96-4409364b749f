html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 26.587rem;
  overflow: hidden;
}

.box_2 {
  height: 26.587rem;
  background-size: 100% 100%;
  width: 51.2rem;
  position: relative;
}

.block_1 {
  position: relative;
  width: 51.2rem;
  height: 8.667rem;
  margin-top: 17.92rem;
}

.block_2 {
  position: absolute;
  left: 2.587rem;
  top: -1.946rem;
  width: 0.054rem;
  height: 8.854rem;
  background-size: 100% 100%;
}

.block_3 {
  position: absolute;
  left: 47.867rem;
  top: -2.826rem;
  width: 0.054rem;
  height: 8.854rem;
  background-size: 100% 100%;
}

.block_5 {
  position: absolute;
  left: 0;
  top: 0;
  width: 51.2rem;
  height: 17.947rem;
}

.group_20 {
  width: 26.16rem;
  height: 6.267rem;
  margin: 0.987rem 0 0 12.427rem;
}

.label_1 {
  width: 1.307rem;
  height: 1.307rem;
}

.text_1 {
  width: 4.48rem;
  height: 0.747rem;
  overflow-wrap: break-word;
  color: rgba(16, 87, 163, 1);
  font-size: 0.746rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.747rem;
  margin: 0.294rem 0 0 0.347rem;
}

.group_1 {
  width: 0.054rem;
  height: 3.6rem;
  background-size: 100% 100%;
  margin: 0.854rem 0 0 1.894rem;
}

.text_2 {
  width: 0.747rem;
  height: 0.374rem;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.373rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.374rem;
  margin: 0.48rem 0 0 2.16rem;
}

.text_3 {
  width: 1.494rem;
  height: 0.374rem;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.373rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.374rem;
  margin: 0.48rem 0 0 1.067rem;
}

.text_4 {
  width: 1.494rem;
  height: 0.374rem;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.373rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.374rem;
  margin: 0.48rem 0 0 1.067rem;
}

.text_5 {
  width: 1.12rem;
  height: 0.374rem;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.373rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.374rem;
  margin: 0.48rem 0 0 1.067rem;
}

.text_6 {
  width: 1.494rem;
  height: 0.374rem;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.373rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.374rem;
  margin: 0.48rem 0 0 1.067rem;
}

.group_2 {
  width: 0.054rem;
  height: 3.6rem;
  background-size: 100% 100%;
  margin: 2.667rem 0 0 0.267rem;
}

.text_7 {
  width: 1.494rem;
  height: 0.374rem;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.373rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.374rem;
  margin: 0.48rem 0 0 0.747rem;
}

.text_8 {
  width: 1.68rem;
  height: 0.374rem;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 0.373rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.374rem;
  margin: 0.48rem 0 0 1.067rem;
}

.image-wrapper_6 {
  width: 0.72rem;
  height: 0.854rem;
  margin: 6.107rem 0 3.734rem 28.96rem;
}

.label_2 {
  width: 0.72rem;
  height: 0.854rem;
}

.box_4 {
  height: 12.454rem;
  background-size: 100% 100%;
  width: 9.76rem;
  position: absolute;
  left: 42.347rem;
  top: -0.773rem;
}

.image-wrapper_2 {
  height: 11.28rem;
  background-size: 100% 100%;
  margin-top: 0.774rem;
  width: 8.854rem;
}

.image_2 {
  width: 8.187rem;
  height: 10.187rem;
  margin-left: 0.667rem;
}

.box_5 {
  height: 11.2rem;
  background-size: 100% 100%;
  width: 10.72rem;
  position: absolute;
  left: -0.026rem;
  top: 0;
}

.image-wrapper_3 {
  height: 10.427rem;
  background-size: 100% 100%;
  margin-left: 0.027rem;
  width: 8.48rem;
}

.image_3 {
  width: 6.88rem;
  height: 8.267rem;
}

.box_8 {
  position: absolute;
  left: 5.947rem;
  top: 8.187rem;
  width: 0.054rem;
  height: 8.854rem;
  background-size: 100% 100%;
}

.box_9 {
  position: absolute;
  left: 9.494rem;
  top: 12.16rem;
  width: 0.054rem;
  height: 8.854rem;
  background-size: 100% 100%;
}

.box_10 {
  position: absolute;
  left: 43.307rem;
  top: 10.774rem;
  width: 0.054rem;
  height: 9.627rem;
  background-size: 100% 100%;
}

.box_6 {
  box-shadow: 0px 0px 18px 2px rgba(102, 133, 161, 0.26);
  border-radius: 26px;
  position: absolute;
  left: 12.427rem;
  top: 6.16rem;
  width: 26.32rem;
  height: 16.454rem;
}

.group_3 {
  position: relative;
  width: 13.334rem;
  height: 16.454rem;
}

.box_7 {
  width: 9.627rem;
  height: 2.107rem;
  background-size: 9.947rem 2.48rem;
  margin: 8.534rem 0 0 1.867rem;
}

.text-group_4 {
  width: 12.24rem;
  height: 1.974rem;
  margin: 1.44rem 0 0 0.56rem;
}

.text_9 {
  width: 3.84rem;
  height: 0.96rem;
  overflow-wrap: break-word;
  color: rgba(0, 102, 177, 1);
  font-size: 0.96rem;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.96rem;
  margin-left: 4.187rem;
}

.text_10 {
  width: 12.24rem;
  height: 0.427rem;
  overflow-wrap: break-word;
  color: rgba(62, 120, 162, 1);
  font-size: 0.426rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.427rem;
  margin-top: 0.587rem;
}

.group_21 {
  width: 3.067rem;
  height: 0.24rem;
  margin: 1.094rem 0 1.067rem 5.147rem;
}

.group_13 {
  background-color: rgba(0, 113, 186, 1);
  border-radius: 4px;
  width: 0.56rem;
  height: 0.24rem;
}

.group_14 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 0.56rem;
  height: 0.24rem;
  margin-left: 0.694rem;
}

.group_15 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 0.56rem;
  height: 0.24rem;
  margin-left: 0.694rem;
}

.image-wrapper_4 {
  height: 6.694rem;
  background-size: 100% 100%;
  width: 9.44rem;
  position: absolute;
  left: 1.974rem;
  top: 3.84rem;
}

.label_3 {
  width: 0.827rem;
  height: 0.614rem;
  margin: 0.507rem 0 0 1.654rem;
}

.group_22 {
  width: 10.347rem;
  height: 14.854rem;
  margin: 0.454rem 0.4rem 0 2.24rem;
}

.group_23 {
  width: 4.774rem;
  height: 2.267rem;
  margin-left: 5.574rem;
}

.group_10 {
  width: 2.587rem;
  height: 0.587rem;
  background-size: 2.614rem 0.587rem;
  margin-top: 0.24rem;
}

.thumbnail_3 {
  width: 0.427rem;
  height: 0.427rem;
  margin: 0.08rem 0 0 0.214rem;
}

.text_24 {
  width: 1.494rem;
  height: 0.374rem;
  overflow-wrap: break-word;
  color: rgba(102, 102, 102, 1);
  font-size: 0.373rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.374rem;
  margin: 0.107rem 0.32rem 0 0.134rem;
}

.image_5 {
  width: 2.267rem;
  height: 2.267rem;
  margin-left: -0.08rem;
}

.text_11 {
  width: 3.84rem;
  height: 0.747rem;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.747rem;
}

.text-wrapper_5 {
  width: 5.44rem;
  height: 0.48rem;
  margin-top: 0.8rem;
}

.text_12 {
  width: 2.4rem;
  height: 0.48rem;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 0.48rem;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.48rem;
}

.text_13 {
  width: 1.707rem;
  height: 0.427rem;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.426rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.427rem;
  margin-top: 0.027rem;
}

.group_5 {
  background-color: rgba(9, 102, 180, 1);
  border-radius: 2px;
  width: 1.12rem;
  height: 0.107rem;
  margin: 0.214rem 0 0 0.64rem;
}

.group_24 {
  width: 2.96rem;
  height: 0.427rem;
  margin: 0.827rem 0 0 0.32rem;
}

.thumbnail_1 {
  width: 0.294rem;
  height: 0.427rem;
}

.text_14 {
  width: 2.24rem;
  height: 0.374rem;
  overflow-wrap: break-word;
  color: rgba(203, 203, 203, 1);
  font-size: 0.373rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.374rem;
  margin-top: 0.027rem;
}

.group_25 {
  width: 7.894rem;
  height: 0.4rem;
  margin: 1.227rem 0 0 0.32rem;
}

.thumbnail_2 {
  width: 0.347rem;
  height: 0.4rem;
}

.text_15 {
  width: 3.094rem;
  height: 0.374rem;
  overflow-wrap: break-word;
  color: rgba(203, 203, 203, 1);
  font-size: 0.373rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.374rem;
  margin-left: 0.374rem;
}

.text_16 {
  width: 1.867rem;
  height: 0.374rem;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 0.373rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.374rem;
  margin-left: 2.214rem;
}

.text-wrapper_2 {
  width: 2.88rem;
  height: 0.374rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.374rem;
  margin: 3.067rem 0 0 2.827rem;
}

.text_17 {
  width: 2.88rem;
  height: 0.374rem;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.32rem;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 0.374rem;
}

.text_18 {
  width: 2.88rem;
  height: 0.374rem;
  overflow-wrap: break-word;
  color: rgba(7, 113, 188, 1);
  font-size: 0.32rem;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 0.374rem;
}

.text-wrapper_3 {
  height: 0.614rem;
  background-size: 100% 100%;
  width: 3.2rem;
  margin: 2.8rem 0 0 0.694rem;
}

.image-text_3 {
  width: 6.854rem;
  height: 0.32rem;
  margin: 0.187rem 0 0 0.907rem;
}

.section_1 {
  border-radius: 2px;
  width: 0.32rem;
  height: 0.32rem;
  border: 1px solid rgba(198, 198, 198, 1);
}

.text-group_2 {
  width: 6.4rem;
  height: 0.32rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.32rem;
}

.text_20 {
  width: 6.4rem;
  height: 0.32rem;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.32rem;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 0.32rem;
}

.text_21 {
  width: 6.4rem;
  height: 0.32rem;
  overflow-wrap: break-word;
  color: rgba(7, 113, 188, 1);
  font-size: 0.32rem;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 0.32rem;
}

.text_22 {
  width: 6.4rem;
  height: 0.32rem;
  overflow-wrap: break-word;
  color: rgba(58, 58, 58, 1);
  font-size: 0.32rem;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 0.32rem;
}

.text_23 {
  width: 6.4rem;
  height: 0.32rem;
  overflow-wrap: break-word;
  color: rgba(7, 113, 188, 1);
  font-size: 0.32rem;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 0.32rem;
}

.group_8 {
  border-radius: 8px;
  position: absolute;
  left: 15.574rem;
  top: 5.494rem;
  width: 8.534rem;
  height: 1.227rem;
  border: 0.6px solid rgba(229, 230, 231, 1);
}

.group_9 {
  border-radius: 8px;
  position: absolute;
  left: 15.574rem;
  top: 7.12rem;
  width: 8.534rem;
  height: 1.227rem;
  border: 0.6px solid rgba(229, 230, 231, 1);
}

.slide-verify-block_2 {
  width: 9.974rem;
  height: 10.187rem;
  margin: 1.787rem 1.494rem 0 1.52rem;
}

.slide-verify-image_5 {
  width: 0.56rem;
  height: 0.48rem;
}

.slide-verify-box_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 17px;
  width: 9.974rem;
  height: 8.934rem;
  border: 3.349253731343284px solid rgba(229, 230, 231, 1);
  margin-top: 0.774rem;
}

.slide-verify-text_11 {
  width: 4.8rem;
  height: 0.48rem;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.48rem;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.48rem;
  margin: 0.667rem 0 0 0.534rem;
}

.slide-verify-box_5 {
  border-radius: 8px;
  width: 8.774rem;
  height: 4.774rem;
  margin: 0.427rem 0 0 0.587rem;
}

.slide-verify-box_6 {
  background-color: rgba(229, 230, 231, 1);
  border-radius: 4px;
  width: 8.774rem;
  height: 1.014rem;
  margin: 0.32rem 0 0 0.587rem;
}

.slide-verify-image-text_1 {
  width: 7.414rem;
  height: 0.8rem;
  margin: 0.107rem 0 0 0.107rem;
}

.slide-verify-image-wrapper_5 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 2px;
  height: 0.8rem;
  width: 1.2rem;
}

.slide-verify-image_6 {
  width: 0.667rem;
  height: 0.534rem;
  margin: 0.134rem 0 0 0.267rem;
}

.slide-verify-text-group_2 {
  width: 5.974rem;
  height: 0.427rem;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 0.426rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.427rem;
  margin-top: 0.187rem;
}

.slide-verify-image-text_2 {
  width: 1.227rem;
  height: 0.48rem;
  margin: 0.32rem 0 0.454rem 0.587rem;
}

.slide-verify-thumbnail_1 {
  width: 0.347rem;
  height: 0.347rem;
  margin-top: 0.08rem;
}

.slide-verify-text-group_3 {
  width: 0.747rem;
  height: 0.48rem;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 0.373rem;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.48rem;
}