.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 51.93vw;
  overflow: hidden;
}

.box_2 {
  height: 51.93vw;
  background-size: 100% 100%;
  width: 100vw;
  position: relative;
}

.block_1 {
  position: relative;
  width: 100vw;
  height: 16.93vw;
  margin-top: 35vw;
}

.block_2 {
  position: absolute;
  left: 5.06vw;
  top: -3.8vw;
  width: 0.11vw;
  height: 17.3vw;
  background-size: 100% 100%;
}

.block_3 {
  position: absolute;
  left: 93.49vw;
  top: -5.52vw;
  width: 0.11vw;
  height: 17.3vw;
  background-size: 100% 100%;
}

.block_5 {
  position: absolute;
  left: 0;
  top: 0;
  width: 100vw;
  height: 35.06vw;
}

.group_20 {
  width: 51.1vw;
  height: 12.24vw;
  margin: 1.92vw 0 0 24.27vw;
}

.label_1 {
  width: 2.56vw;
  height: 2.56vw;
}

.text_1 {
  width: 8.75vw;
  height: 1.46vw;
  overflow-wrap: break-word;
  color: rgba(16, 87, 163, 1);
  font-size: 1.45vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.46vw;
  margin: 0.57vw 0 0 0.67vw;
}

.group_1 {
  width: 0.11vw;
  height: 7.04vw;
  background-size: 100% 100%;
  margin: 1.66vw 0 0 3.69vw;
}

.text_2 {
  width: 1.46vw;
  height: 0.73vw;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.72vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.73vw;
  margin: 0.93vw 0 0 4.21vw;
}

.text_3 {
  width: 2.92vw;
  height: 0.73vw;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.72vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.73vw;
  margin: 0.93vw 0 0 2.08vw;
}

.text_4 {
  width: 2.92vw;
  height: 0.73vw;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.72vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.73vw;
  margin: 0.93vw 0 0 2.08vw;
}

.text_5 {
  width: 2.19vw;
  height: 0.73vw;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.72vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.73vw;
  margin: 0.93vw 0 0 2.08vw;
}

.text_6 {
  width: 2.92vw;
  height: 0.73vw;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.72vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.73vw;
  margin: 0.93vw 0 0 2.08vw;
}

.group_2 {
  width: 0.11vw;
  height: 7.04vw;
  background-size: 100% 100%;
  margin: 5.2vw 0 0 0.52vw;
}

.text_7 {
  width: 2.92vw;
  height: 0.73vw;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.72vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.73vw;
  margin: 0.93vw 0 0 1.45vw;
}

.text_8 {
  width: 3.29vw;
  height: 0.73vw;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 0.72vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.73vw;
  margin: 0.93vw 0 0 2.08vw;
}

.image-wrapper_6 {
  width: 1.41vw;
  height: 1.67vw;
  margin: 11.92vw 0 7.29vw 56.56vw;
}

.label_2 {
  width: 1.41vw;
  height: 1.67vw;
}

.box_4 {
  height: 24.33vw;
  background-size: 100% 100%;
  width: 19.07vw;
  position: absolute;
  left: 82.71vw;
  top: -1.51vw;
}

.image-wrapper_2 {
  height: 22.04vw;
  background-size: 100% 100%;
  margin-top: 1.52vw;
  width: 17.3vw;
}

.image_2 {
  width: 15.99vw;
  height: 19.9vw;
  margin-left: 1.31vw;
}

.box_5 {
  height: 21.88vw;
  background-size: 100% 100%;
  width: 20.94vw;
  position: absolute;
  left: -0.05vw;
  top: 0;
}

.image-wrapper_3 {
  height: 20.37vw;
  background-size: 100% 100%;
  margin-left: 0.06vw;
  width: 16.57vw;
}

.image_3 {
  width: 13.44vw;
  height: 16.15vw;
}

.box_8 {
  position: absolute;
  left: 11.62vw;
  top: 15.99vw;
  width: 0.11vw;
  height: 17.3vw;
  background-size: 100% 100%;
}

.box_9 {
  position: absolute;
  left: 18.55vw;
  top: 23.75vw;
  width: 0.11vw;
  height: 17.3vw;
  background-size: 100% 100%;
}

.box_10 {
  position: absolute;
  left: 84.59vw;
  top: 21.05vw;
  width: 0.11vw;
  height: 18.81vw;
  background-size: 100% 100%;
}

.box_6 {
  box-shadow: 0px 0px 18px 2px rgba(102, 133, 161, 0.26);
  border-radius: 26px;
  position: absolute;
  left: 24.28vw;
  top: 12.04vw;
  width: 51.41vw;
  height: 32.14vw;
}

.group_3 {
  position: relative;
  width: 26.05vw;
  height: 32.14vw;
}

.box_7 {
  width: 18.81vw;
  height: 4.12vw;
  background-size: 19.42vw 4.84vw;
  margin: 16.66vw 0 0 3.64vw;
}

.text-group_4 {
  width: 23.91vw;
  height: 3.86vw;
  margin: 2.81vw 0 0 1.09vw;
}

.text_9 {
  width: 7.5vw;
  height: 1.88vw;
  overflow-wrap: break-word;
  color: rgba(0, 102, 177, 1);
  font-size: 1.87vw;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.88vw;
  margin-left: 8.18vw;
}

.text_10 {
  width: 23.91vw;
  height: 0.84vw;
  overflow-wrap: break-word;
  color: rgba(62, 120, 162, 1);
  font-size: 0.83vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.84vw;
  margin-top: 1.15vw;
}

.group_21 {
  width: 5.99vw;
  height: 0.47vw;
  margin: 2.13vw 0 2.08vw 10.05vw;
}

.group_13 {
  background-color: rgba(0, 113, 186, 1);
  border-radius: 4px;
  width: 1.1vw;
  height: 0.47vw;
}

.group_14 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 1.1vw;
  height: 0.47vw;
  margin-left: 1.36vw;
}

.group_15 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 1.1vw;
  height: 0.47vw;
  margin-left: 1.36vw;
}

.image-wrapper_4 {
  height: 13.08vw;
  background-size: 100% 100%;
  width: 18.44vw;
  position: absolute;
  left: 3.86vw;
  top: 7.5vw;
}

.label_3 {
  width: 1.62vw;
  height: 1.2vw;
  margin: 0.98vw 0 0 3.22vw;
}

.group_22 {
  width: 20.21vw;
  height: 29.02vw;
  margin: 0.88vw 0.78vw 0 4.37vw;
}

.group_23 {
  width: 9.33vw;
  height: 4.43vw;
  margin-left: 10.89vw;
}

.group_10 {
  width: 5.06vw;
  height: 1.15vw;
  background-size: 5.1vw 1.14vw;
  margin-top: 0.47vw;
}

.thumbnail_3 {
  width: 0.84vw;
  height: 0.84vw;
  margin: 0.15vw 0 0 0.41vw;
}

.text_24 {
  width: 2.92vw;
  height: 0.73vw;
  overflow-wrap: break-word;
  color: rgba(102, 102, 102, 1);
  font-size: 0.72vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.73vw;
  margin: 0.2vw 0.62vw 0 0.26vw;
}

.image_5 {
  width: 4.43vw;
  height: 4.43vw;
  margin-left: -0.15vw;
}

.text_11 {
  width: 7.5vw;
  height: 1.46vw;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 1.25vw;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.46vw;
}

.text-wrapper_5 {
  width: 10.63vw;
  height: 0.94vw;
  margin-top: 1.57vw;
}

.text_12 {
  width: 4.69vw;
  height: 0.94vw;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 0.93vw;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.94vw;
}

.text_13 {
  width: 3.34vw;
  height: 0.84vw;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.83vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.84vw;
  margin-top: 0.06vw;
}

.group_5 {
  background-color: rgba(9, 102, 180, 1);
  border-radius: 2px;
  width: 2.19vw;
  height: 0.21vw;
  margin: 0.41vw 0 0 1.25vw;
}

.group_24 {
  width: 5.79vw;
  height: 0.84vw;
  margin: 1.61vw 0 0 0.62vw;
}

.thumbnail_1 {
  width: 0.58vw;
  height: 0.84vw;
}

.text_14 {
  width: 4.38vw;
  height: 0.73vw;
  overflow-wrap: break-word;
  color: rgba(203, 203, 203, 1);
  font-size: 0.72vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.73vw;
  margin-top: 0.06vw;
}

.group_25 {
  width: 15.42vw;
  height: 0.79vw;
  margin: 2.39vw 0 0 0.62vw;
}

.thumbnail_2 {
  width: 0.68vw;
  height: 0.79vw;
}

.text_15 {
  width: 6.05vw;
  height: 0.73vw;
  overflow-wrap: break-word;
  color: rgba(203, 203, 203, 1);
  font-size: 0.72vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.73vw;
  margin-left: 0.73vw;
}

.text_16 {
  width: 3.65vw;
  height: 0.73vw;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 0.72vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.73vw;
  margin-left: 4.33vw;
}

.text-wrapper_2 {
  width: 5.63vw;
  height: 0.73vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.73vw;
  margin: 5.98vw 0 0 5.52vw;
}

.text_17 {
  width: 5.63vw;
  height: 0.73vw;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.62vw;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 0.73vw;
}

.text_18 {
  width: 5.63vw;
  height: 0.73vw;
  overflow-wrap: break-word;
  color: rgba(7, 113, 188, 1);
  font-size: 0.62vw;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 0.73vw;
}

.text-wrapper_3 {
  height: 1.2vw;
  background-size: 100% 100%;
  width: 6.25vw;
  margin: 5.46vw 0 0 1.35vw;
}

.image-text_3 {
  width: 13.39vw;
  height: 0.63vw;
  margin: 0.36vw 0 0 1.77vw;
}

.section_1 {
  border-radius: 2px;
  width: 0.63vw;
  height: 0.63vw;
  border: 1px solid rgba(198, 198, 198, 1);
}

.text-group_2 {
  width: 12.5vw;
  height: 0.63vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.63vw;
}

.text_20 {
  width: 12.5vw;
  height: 0.63vw;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.62vw;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 0.63vw;
}

.text_21 {
  width: 12.5vw;
  height: 0.63vw;
  overflow-wrap: break-word;
  color: rgba(7, 113, 188, 1);
  font-size: 0.62vw;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 0.63vw;
}

.text_22 {
  width: 12.5vw;
  height: 0.63vw;
  overflow-wrap: break-word;
  color: rgba(58, 58, 58, 1);
  font-size: 0.62vw;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 0.63vw;
}

.text_23 {
  width: 12.5vw;
  height: 0.63vw;
  overflow-wrap: break-word;
  color: rgba(7, 113, 188, 1);
  font-size: 0.62vw;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 0.63vw;
}

.group_8 {
  border-radius: 8px;
  position: absolute;
  left: 30.42vw;
  top: 10.73vw;
  width: 16.67vw;
  height: 2.4vw;
  border: 0.6px solid rgba(229, 230, 231, 1);
}

.group_9 {
  border-radius: 8px;
  position: absolute;
  left: 30.42vw;
  top: 13.91vw;
  width: 16.67vw;
  height: 2.4vw;
  border: 0.6px solid rgba(229, 230, 231, 1);
}

.slide-verify-block_2 {
  width: 19.48vw;
  height: 19.9vw;
  margin: 3.48vw 2.91vw 0 2.96vw;
}

.slide-verify-image_5 {
  width: 1.1vw;
  height: 0.94vw;
}

.slide-verify-box_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 17px;
  width: 19.48vw;
  height: 17.45vw;
  border: 3.349253731343284px solid rgba(229, 230, 231, 1);
  margin-top: 1.52vw;
}

.slide-verify-text_11 {
  width: 9.38vw;
  height: 0.94vw;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.93vw;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.94vw;
  margin: 1.3vw 0 0 1.04vw;
}

.slide-verify-box_5 {
  border-radius: 8px;
  width: 17.14vw;
  height: 9.33vw;
  margin: 0.83vw 0 0 1.14vw;
}

.slide-verify-box_6 {
  background-color: rgba(229, 230, 231, 1);
  border-radius: 4px;
  width: 17.14vw;
  height: 1.98vw;
  margin: 0.62vw 0 0 1.14vw;
}

.slide-verify-image-text_1 {
  width: 14.48vw;
  height: 1.57vw;
  margin: 0.2vw 0 0 0.2vw;
}

.slide-verify-image-wrapper_5 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 2px;
  height: 1.57vw;
  width: 2.35vw;
}

.slide-verify-image_6 {
  width: 1.31vw;
  height: 1.05vw;
  margin: 0.26vw 0 0 0.52vw;
}

.slide-verify-text-group_2 {
  width: 11.67vw;
  height: 0.84vw;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 0.83vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.84vw;
  margin-top: 0.37vw;
}

.slide-verify-image-text_2 {
  width: 2.4vw;
  height: 0.94vw;
  margin: 0.62vw 0 0.88vw 1.14vw;
}

.slide-verify-thumbnail_1 {
  width: 0.68vw;
  height: 0.68vw;
  margin-top: 0.16vw;
}

.slide-verify-text-group_3 {
  width: 1.46vw;
  height: 0.94vw;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 0.72vw;
  font-weight: normal;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.94vw;
}