.highlighted {
  // position: relative;
  // color: #0966b4;
  // // animation: explode 1s ease-out;
  // // -webkit-box-reflect: below 10px linear-gradient(transparent, #0005);
  // animation: blink 1s linear infinite;
  // /* 其它浏览器兼容性前缀 */
  // -webkit-animation: blink 1s linear infinite;
  // -moz-animation: blink 1s linear infinite;
  // -ms-animation: blink 1s linear infinite;
  // -o-animation: blink 1s linear infinite;
}
@keyframes explode {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.5;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes blink {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}
/* 添加兼容性前缀 */
@-webkit-keyframes blink {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@-moz-keyframes blink {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@-ms-keyframes blink {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@-o-keyframes blink {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
