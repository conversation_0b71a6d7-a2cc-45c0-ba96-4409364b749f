// global transition css

/* fade */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.28s;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}

/* fade-transform */
.fade-transform--move,
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* breadcrumb transition */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all 0.5s;
}

.breadcrumb-leave-active {
  position: absolute;
}

.__select-item__ {
  background-color: var(--el-dropdown-menuItem-hover-fill);
  color: var(--el-dropdown-menuItem-hover-color);
}

.__flex-center__ {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.__hidden-scroll__ {
  scrollbar-width: none; /* 针对 Firefox */
  -ms-overflow-style: none; /* 针对 Internet Explorer 和 Edge */
}

//超出省略号
.__ellipsis__ {
  overflow: hidden; /* 确保超出容器的文本被裁剪 */
  white-space: nowrap; /* 确保文本在一行内显示 */
  text-overflow: ellipsis; /* 使用省略号表示文本超出 */
}

.__ellipsis__2_line {
  display: -webkit-box; /* 使用WebKit的盒子模型 */
  -webkit-box-orient: vertical; /* 设置文本垂直排列 */
  -webkit-line-clamp: 2; /* 限制显示的行数 */
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
  line-height: 1.5; /* 设置行高，确保两行的高度正确 */
  max-height: 3em; /* 设置最大高度（行高 × 行数） */
}

//阅读器下拉框黑夜模式的样式
.__darkSelect__ {
  background-color: #333 !important;
  color: #f7f7f7;
  .el-select-dropdown__item.is-selected {
    color: #75c0ff;
    background-color: #2b3c4a;
  }
  .is-hovering {
    background-color: #333;
    color: #f7f7f7;
  }
  & li:hover {
    color: #75c0ff;
    background-color: #2b3c4a;
  }
}
