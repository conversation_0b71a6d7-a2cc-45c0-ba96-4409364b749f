<template>
  <el-dialog
    v-model="dialogVisible"
    title="发票开具声明"
    width="600px"
    :close-on-click-modal="false"
    class="custom-dialog"
  >
    <div class="content">
      <p>根据国家税法及发票管理相关规定，任何单位和个人不得要求服务或商品提供方开具与实际经营业务情况不符的专用发票。包括但不限于下列"虚开专用发票"的行为：</p>
      
      <ol class="disclaimer-list">
        <li>在没有货物采购或者没有接受应税劳务的情况下要求为自己或他人开具专用发票；</li>
        <li>虽有货物采购或者接受应税劳务，但要求为自己或他人开具数量或金额与实际情况不符的专用发票；</li>
      </ol>

      <p class="confirm-text">我已充分了解上述各项税法和发票管理规定，并确认仅就我或我单位实际购买商品或服务索取发票。如我未按国家相关规定申请开具或使用专用发票，由我自行承担相应法律后果。</p>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleConfirm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleConfirm = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
/* 标题居中 */
.custom-dialog :deep(.el-dialog__header) {
  text-align: center;
}

/* 按钮居中 */
.dialog-footer {
  display: flex;
  justify-content: center; /* 修改为居中 */
}

.content {
  line-height: 1.6;
  color: #606266;
}

.disclaimer-list {
  margin: 12px 0;
  padding-left: 20px;
}

.disclaimer-list li {
  margin-bottom: 8px;
}

.confirm-text {
  margin-top: 16px;
  font-weight: 500;
  color: #303133;
}
</style>