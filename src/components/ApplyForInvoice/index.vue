<template>
  <div>
    <el-dialog
      :title="
        businessFlg === 0
          ? '申请发票'
          : businessFlg === 1
            ? '添加发票抬头'
            : '修改发票抬头'
      "
      :modelValue="modelValue"
      width="25%"
      :before-close="handleClose"
      center
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="invoiceForm"
        label-width="100px"
      >
        <el-row v-if="businessFlg === 0">
          <el-col :span="14">
            <el-form-item>
              <template #label>
                <span class="common-style">订单编号:</span>
              </template>
              <span class="common-style">{{ form.orderNo }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="开票金额:">
              <span style="font-size: 18px; color: #2354a1"
                >￥{{ form.invoiceAmount }}</span
              >
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="发票类型:" prop="invoiceType">
              <el-radio-group
                v-model="form.invoiceType"
                :disabled="businessFlg === 0"
              >
                <el-radio :label="1" size="large">数电普票</el-radio>
                <el-radio :label="2" size="large">数电专票</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="抬头类型:" prop="titleType">
              <el-radio-group
                v-model="form.titleType"
                :disabled="businessFlg === 0"
              >
                <el-radio :label="1" size="large">个人</el-radio>
                <el-radio :label="2" size="large">企业</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="businessFlg === 0">
          <el-col :span="24">
            <el-form-item label="发票内容:" prop="applyType">
              <el-radio-group v-model="form.applyType">
                <el-radio :label="1" size="large">商品明细</el-radio>
                <el-radio :label="2" size="large">商品类别</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="抬头名称:" prop="titleName">
              <el-select
                v-if="businessFlg === 0"
                v-model="form.titleName"
                placeholder="请选择发票抬头名称"
                size="large"
                style="width: 300px"
                @change="handleTitleChange"
              >
                <el-option
                  v-for="item in titleOptions"
                  :key="item.titleId"
                  :label="item.titleName"
                  :value="item.titleId"
                />
              </el-select>
              <el-input
                v-else
                v-model="form.titleName"
                style="width: 300px"
                placeholder="请输入发票抬头名称"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="form.titleType === 2 && businessFlg !== 0">
          <el-row>
            <el-col :span="24">
              <el-form-item label="单位税号:" prop="taxNo">
                <el-input
                  v-model="form.taxNo"
                  style="width: 300px"
                  :disabled="businessFlg === 0"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="注册地址:" prop="registAddress">
                <el-input
                  v-model="form.registAddress"
                  style="width: 300px"
                  :disabled="businessFlg === 0"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="注册电话:" prop="registTel">
                <el-input
                  v-model="form.registTel"
                  style="width: 300px"
                  :disabled="businessFlg === 0"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="开户银行:" prop="accountBank">
                <el-input
                  v-model="form.accountBank"
                  style="width: 300px"
                  :disabled="businessFlg === 0"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="银行账号:" prop="accountNo">
                <el-input
                  v-model="form.accountNo"
                  style="width: 300px"
                  :disabled="businessFlg === 0"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="businessFlg === 0">
            <el-col :span="24">
              <el-form-item label="收票人邮箱:" prop="userEmail">
                <el-input v-model="form.userEmail" style="width: 300px" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div v-else-if="businessFlg === 0">
          <el-row>
            <el-col :span="24">
              <el-form-item label="单位税号:" prop="taxNo">
                <el-input
                  v-model="form.taxNo"
                  style="width: 300px"
                  :disabled="businessFlg === 0"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="注册地址:" prop="registAddress">
                <el-input
                  v-model="form.registAddress"
                  style="width: 300px"
                  :disabled="businessFlg === 0"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="注册电话:" prop="registTel">
                <el-input
                  v-model="form.registTel"
                  style="width: 300px"
                  :disabled="businessFlg === 0"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="开户银行:" prop="accountBank">
                <el-input
                  v-model="form.accountBank"
                  style="width: 300px"
                  :disabled="businessFlg === 0"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="银行账号:" prop="accountNo">
                <el-input
                  v-model="form.accountNo"
                  style="width: 300px"
                  :disabled="businessFlg === 0"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="businessFlg === 0">
            <el-col :span="24">
              <el-form-item label="收票人邮箱:" prop="userEmail">
                <el-input v-model="form.userEmail" style="width: 300px" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div  v-if="form.invoiceType === 2" style="margin-left: 25px !important">
        <el-tooltip
          class="box-item"
          effect="dark"
          content="请先同意《专用发票抬头确认书》"
          placement="top-end"
          v-model:visible="showAgreementPopover"
        >
          <el-checkbox
            v-model="agreeTerms"
            @click="visible = !showAgreementPopover"
          >
            <el-button
              type="primary"
              link
              @click="invoiceDisclaimerDialog = true"
              >我已阅读并同意《专用发票抬头确认书》</el-button
            >
          </el-checkbox>
        </el-tooltip>
        <InvoiceDisclaimerDialog v-model="invoiceDisclaimerDialog" />
      </div>
      <template #footer>
        <el-button class="close-btn" @click="closeDialog">取消</el-button>
        <el-button class="submit-btn" @click="submitForm(form)">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
const props = defineProps({
  form: Object,
  modelValue: Boolean,
  businessFlg: Number,
});
import { onMounted, ref } from "vue";
import {
  getInvoiceTitleEducation,
  addEducation,
  editEducation,
} from "@/api/shop/title";
import { addInvoiceApplyEducation } from "@/api/shop/apply";
import InvoiceDisclaimerDialog from "@/components/ApplyForInvoice/InvoiceDisclaimerDialog.vue";
import { ElNotification } from "element-plus";

const showAgreementPopover = ref(false);
const invoiceDisclaimerDialog = ref(false); // 确保初始值为 false
const agreeTerms = ref(false);

const titleOptions = ref([]);
const emit = defineEmits(["update", "update-completed"]);
const handleClose = (done) => {
  resetForm();
  emit("update:modelValue", false);
  done();
};

const closeDialog = () => {
  resetForm();
  emit("update:modelValue", false);
};

const invoiceForm = ref(null);
const rules = computed(() => ({
  titleName: [{ required: true, message: "请选择抬头名称", trigger: "change" }],
  taxNo: [{ required: true, message: "请输入单位税号", trigger: "blur" }],
  userEmail: [
    { required: true, message: "请输入收票人邮箱", trigger: "blur" },
    {
      pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: "请输入有效的邮箱地址",
      trigger: "blur",
    },
  ],
  applyType: [{ required: true, message: "请选择发票内容", trigger: "blur" }],
}));

const submitForm = (form) => {
  if (form.invoiceType === 2 && !agreeTerms.value) {
    showAgreementPopover.value = true;
    return;
  }
  invoiceForm.value.validate((valid) => {
    if (valid) {
      if (props.businessFlg === 0) {
        addInvoiceApplyEducation(form).then((res) => {
          if (res.code === 200) {
            ElNotification({
              title: "开票申请已提交",
              message: "发票预计将在48小时内开出，请注意查收",
              type: "success",
            });
            closeDialog();
          } else {
            ElNotification({
              title: "操作提示",
              message: res.msg,
              type: "warning",
            });
          }
        });
      } else if (props.businessFlg === 1) {
        let param = form;
        form.titleId = null;
        if (form.titleType === 1) {
          param.taxNo = "";
          param.registAddress = "";
          param.registTel = "";
          param.accountBank = "";
          param.accountNo = "";
        }
        addEducation(param).then((res) => {
          if (res.code === 200) {
            ElNotification({
              title: "操作提示",
              message: "添加成功",
              type: "success",
            });
            closeDialog();
            emit("update-completed");
          } else {
            ElNotification({
              title: "操作提示",
              message: "添加失败",
              type: "error",
            });
          }
        });
      } else {
        let param = form;
        if (form.titleType === 1) {
          param.taxNo = "";
          param.registAddress = "";
          param.registTel = "";
          param.accountBank = "";
          param.accountNo = "";
        }
        editEducation(param).then((res) => {
          if (res.code === 200) {
            ElNotification({
              title: "操作提示",
              message: "修改成功",
              type: "success",
            });
            closeDialog();
            emit("update-completed");
          } else {
            ElNotification({
              title: "操作提示",
              message: "修改失败",
              type: "error",
            });
          }
        });
      }
      // 提交成功后重置表单
      resetForm();
      emit("update:modelValue", false);
    } else {
      return false;
    }
  });
};
function handleTitleChange(titleId, title) {
  const selectedTitle = titleOptions.value.find(
    (option) => option.titleId === titleId
  );
  if (selectedTitle) {
    // 更新表单中的其他字段
    props.form.invoiceType = selectedTitle.invoiceType;
    props.form.registAddress = selectedTitle.registAddress;
    props.form.registTel = selectedTitle.registTel;
    props.form.accountBank = selectedTitle.accountBank;
    props.form.accountNo = selectedTitle.accountNo;
    props.form.titleType = selectedTitle.titleType;
    props.form.taxNo = selectedTitle.taxNo;
    props.form.titleId = selectedTitle.titleId;
  }
}
function resetForm() {
  if (invoiceForm.value) {
    // 清除表单验证状态
    invoiceForm.value.clearValidate();
    invoiceForm.value.resetFields();
  }
}
onMounted(() => {
  if (props.businessFlg === 0) {
    getInvoiceTitleEducation({}).then((res) => {
      titleOptions.value = res.data;
      if (res && res.rows) {
      }
    });
  } else {
    props.form.invoiceType = 1;
    props.form.titleType = 1;
  }
});
</script>

<style scoped>
.dialog-footer {
  text-align: center;
}

.group {
  background-color: rgba(255, 255, 255, 1);
  height: 156px;
  position: relative;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.text-wrapper {
  height: 50px;
  margin: 39px 0 0 131px;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.text_isbn {
  width: 300px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  line-height: 70px;
}

.text-wrapper_1 {
  position: absolute;
  left: 131px;
  top: 14px;
  width: 991px;
  height: 50px;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.text_bookName {
  width: 240px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 50px;
}

.section {
  border-radius: 2px;
  height: 109px;
  width: 81px;
  position: absolute;
  left: 32px;
  top: 22px;
}

.text_priceCounter {
  line-height: 165px;
  margin-left: -300px;
  color: rgba(51, 51, 51, 1);
  font-size: 14px;
  font-weight: normal;
}

.section_3 {
  width: 1087px;
  height: 50px;
  margin: 3px 0 14px 35px;
}

:deep(.el-dialog__footer) {
  box-sizing: border-box;
  padding-top: var(--el-dialog-padding-primary);
  text-align: center;
}

.submit-btn {
  width: 120px;
  height: 35px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 600;
  font-size: 16px;
  background-color: #2354a1;
  color: #ffffff;
  text-align: center;
  line-height: 42px;
  cursor: pointer;
}

.close-btn {
  width: 120px;
  height: 35px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 600;
  font-size: 16px;
  text-align: center;
  line-height: 42px;
  background-color: #eaeeef;
  color: #3665a3;
  cursor: pointer;
}

.refund-icon {
  float: left;
}

.refund-text {
  float: left;
  margin: 2px 0px 0px 10px;
}

.label {
  align-items: flex-start;
  box-sizing: border-box;
  color: var(--el-text-color-regular);
  display: inline-flex;
  flex: 0 0 auto;
  font-size: var(--el-form-label-font-size);
  height: 32px;
  justify-content: flex-end;
  line-height: 32px;
  padding: 0 12px 0 0;
}

.refund-pay-amount {
  font-size: 18px;
  color: #2354a1;

  float: right;
  margin: 14px 0px 0px 10px;
}

.common-style {
  color: rgba(153, 153, 153, 1);
}
</style>
