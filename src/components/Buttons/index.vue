<!-- 模块说明 -->
<style lang="scss" scoped>
.btn:nth-of-type(1) {
  margin-left: 0;
}
</style>
<template>
  <div :style="`margin-left: ${left};`">
    <span v-for="(ele, i) in _options" :key="i">
      <el-button
        v-bind="ele.attrs"
        v-if="!ele.attrs?.circle && ele.text"
        class="btn"
        :style="`margin-left: ${itemLeft};`"
        :type="ele.type"
        @click="click(ele.click)"
        :size="ele?.size"
        :disabled="typeof ele.disabled == 'function' ? ele.disabled(prop) : ele.disabled || disabled"
        :icon="ele.icon || ''">
        {{ typeof ele.text == 'string' ? ele.text : ele.text && ele.text() }}
      </el-button>
      <el-button
        v-bind="ele.attrs"
        v-else
        class="btn"
        :style="`margin-left: ${itemLeft};`"
        :type="ele.type"
        @click="click(ele.click)"
        :size="ele?.size"
        :disabled="typeof ele.disabled == 'function' ? ele.disabled(prop) : ele.disabled || disabled"
        :icon="ele.icon || ''" />
    </span>
  </div>
</template>

<script setup>
import { computed } from 'vue'
const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
  itemLeft: {
    type: String,
    default: '20px',
  },
  left: String,
  prop: {
    type: Object,
    default: () => ({}),
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})
const click = (_fun) => {
  if (_fun) _fun(props.prop)
}
const _options = computed(() => {
  return props.options.filter((ele) => {
    if (ele.isHide) {
      return ele.isHide()
    } else {
      return true
    }
  })
})
// const Options = [
//   {
//     text: '保存',
//     type: '1',
//     click: () => {
//     }
//   },
//   {
//     text: '同意',
//     click() {
//     }
//   }
// ]
</script>
