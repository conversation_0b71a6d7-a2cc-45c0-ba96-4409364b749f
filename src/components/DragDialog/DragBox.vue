<!-- 自动阅读容器 -->
<template>
  <div
    class="automaticReadingBox"
    :style="{ top: drag.y + 'px', left: drag.x + 'px' }"
    @mousedown="startDrag"
  >
    <div>
      <slot></slot>
    </div>
  </div>
  <div v-if="drag.status" class="automaticReadingBox-maskLayer"></div>
</template>
<script setup>
import { defineProps, onBeforeUnmount, onMounted, reactive } from 'vue'
const props = defineProps({
  startOption: {
    type: Object,
    default: () => ({ x: 0, y: 0 }),
  },
  localName: String,
  margins:{
    type:Number,
    default:50
  }
})
const drag = reactive({
  status: false,
  x: 0,
  y: 0,
  startX: 0,
  startY: 0,
})

const startDrag = (e) => {
  drag.status = true
  drag.startX = e.clientX - drag.x
  drag.startY = e.clientY - drag.y
}
const doDrag = (e) => {
  if (drag.status) {
    drag.x = e.clientX - drag.startX
    drag.y = e.clientY - drag.startY
  }
}
const stopDrag = () => {
  drag.status = false
  // if (drag.y < 0) drag.y = 0
  // if (window.innerHeight - drag.y < 60) drag.y = 120
  // if (drag.x < 0) drag.x = 0
  // if (window.innerWidth - drag.x < 60) drag.x = 100
  if (
    drag.y < 0 ||
    window.innerHeight - drag.y < props.margins ||
    drag.x < 0 ||
    window.innerWidth - drag.x < props.margins
  ) {
    initPosition()
  }

  if (props.localName)
    localStorage.setItem(
      props.localName,
      JSON.stringify({ x: drag.x, y: drag.y }),
    )
}
const initPosition = () => {
  try {
    const { x, y } = JSON.parse(localStorage.getItem(props.localName))
    drag.x = x
    drag.y = y
  } catch (error) {
    const { x, y } = props.startOption
    drag.x = x
    drag.y = y
  }
}
onMounted(() => {
  document.addEventListener('mousemove', doDrag)
  document.addEventListener('mouseup', stopDrag)
  initPosition()
})

onBeforeUnmount(() => {
  document.removeEventListener('mousemove', doDrag)
  document.removeEventListener('mouseup', stopDrag)
})
</script>

<style lang="less" scoped>
.automaticReadingBox {
  position: fixed;
  z-index: 99;
  cursor: move;
}
.automaticReadingBox-maskLayer {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 98;
}
</style>
