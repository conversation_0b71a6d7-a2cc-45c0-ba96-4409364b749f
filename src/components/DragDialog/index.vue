<style lang="scss" scoped>
.DragDialog {
  background: #fff;
  position: fixed;
  z-index: 999;
  border: 1px solid #000;
  .header {
    width: 100%;
    height: 30px;
    background-color: #cfa;
    display: flex;
    justify-content: space-between;
    cursor: move;
    .title {
      font-weight: bold;
      font-size: 16px;
      line-height: 30px;
      margin-left: 10px;
    }
    .close {
      cursor: pointer;
      padding: 5px;
      color: #999;
      transition: 0.3s;
      &:hover {
        color: #409eff;
      }
    }
  }
  .main {
    min-width: 50px;
    width: 100%;
    height: calc(100% - 30px);
    overflow-y: auto;
    resize: both;
  }
}
.DragDialog-maskLayer {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 998;
}
</style>
<template>
  <div class="DragDialog" :style="{ top: drag.y + 'px', left: drag.x + 'px' }" >
    <div class="header" @mousedown="startDrag">
      <div class="title">平行阅读</div>
      <div class="close">
        <el-icon><CloseBold /></el-icon>
      </div>
    </div>
    <div class="main">
      <p>1234567890</p>
      <p>1234567890</p>
      <p>1234567890</p>
      <p>1234567890</p>
      <p>1234567890</p>
      <p>1234567890</p>
    </div>
  </div>
  <div class="DragDialog-maskLayer" v-if="drag.status"></div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, h } from 'vue'
const drag = reactive({
  status: false,
  x: 0,
  y: 0,
  startX: 0,
  startY: 0,
})

const resize = reactive({
  status: false,
  w: 500,
  h: 380,
})
const startDrag = (e) => {
  drag.status = true
  drag.startX = e.clientX - drag.x
  drag.startY = e.clientY - drag.y
}
const doDrag = (e) => {
  if (drag.status) {
    drag.x = e.clientX - drag.startX
    drag.y = e.clientY - drag.startY
  }
}
const stopDrag = () => {
  drag.status = false
  if (drag.y < 0) drag.y = 0
}

const startResize = (e) => {
  // resize.status = true
  // this.startX = event.clientX
  // this.startY = event.clientY
  // this.resizeFrom = position
}

const doResize = (e) => {}

const endResize = (e) => {}

onMounted(() => {
  document.addEventListener('mousemove', doDrag)
  document.addEventListener('mouseup', stopDrag)
})

onBeforeUnmount(() => {
  document.removeEventListener('mousemove', doDrag)
  document.removeEventListener('mouseup', stopDrag)
})
</script>
