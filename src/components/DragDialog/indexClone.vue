<template>
  <div>
    <div class="modal" ref="modal" :style="{ top: top + 'px', left: left + 'px', width: width + 'px', height: height + 'px' }" @mousedown="initDrag">
      <header @mousedown.stop.prevent="startResize('top-left')" />
      <footer @mousedown.stop.prevent="startResize('bottom-right')" />
      <div class="modal-content">
        <!-- Your content here -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      drag: false,
      resize: false,
      startX: 0,
      startY: 0,
      top: 100,
      left: 100,
      width: 300,
      height: 200,
    }
  },
  methods: {
    initDrag(event) {
      this.drag = true
      this.startX = event.clientX - this.left
      this.startY = event.clientY - this.top
    },
    doDrag(event) {
      if (this.drag) {
        this.top = event.clientY - this.startY
        this.left = event.clientX - this.startX
      }
    },
    stopDrag() {
      this.drag = false
    },
    
    startResize(position) {
      this.resize = true
      this.startX = event.clientX
      this.startY = event.clientY
      this.resizeFrom = position
    },
    doResize(event) {
      if (this.resize) {
        const x = event.clientX
        const y = event.clientY
        const el = this.$refs.modal

        switch (this.resizeFrom) {
          case 'top-left':
            this.top = y - this.startY
            this.left = x - this.startX
            this.width = this.width - (x - this.startX)
            this.height = this.height - (y - this.startY)
            break
          case 'bottom-right':
            this.width = x - this.left
            this.height = y - this.top
            break
          // Add more cases for other corners if needed
          default:
            break
        }
      }
    },
    stopResize() {
      this.resize = false
    },
  },
  mounted() {
    document.addEventListener('mousemove', this.doDrag)
    document.addEventListener('mouseup', this.stopDrag)
    document.addEventListener('mousemove', this.doResize)
    document.addEventListener('mouseup', this.stopResize)
  },
  beforeUnmount() {
    document.removeEventListener('mousemove', this.doDrag)
    document.removeEventListener('mouseup', this.stopDrag)
    document.removeEventListener('mousemove', this.doResize)
    document.removeEventListener('mouseup', this.stopResize)
  },
}
</script>

<style scoped>
.modal {
  position: absolute;
  background-color: white;
  border: 1px solid #ccc;
  resize: both;
  overflow: auto;
  z-index: 999;
}

.modal-content {
  padding: 10px;
}

header,
footer {
  cursor: nwse-resize;
  background: #ccc;
  width: 20px;
  height: 20px;
  position: absolute;
  top: 0;
  right: 0;
}

header {
  top: 0;
  right: 0;
}

footer {
  bottom: 0;
}
</style>
