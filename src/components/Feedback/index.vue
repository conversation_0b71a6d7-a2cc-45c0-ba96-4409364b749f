<template>
  <el-dialog :modelValue="modelValue" width="400px" :before-close="handleClose">
    <el-row style="margin-bottom: 20px">
      <el-col :span="24">
        <!-- 内容输入 -->
        <el-input v-model="title" :rows="4" placeholder="反馈标题" />
      </el-col>
    </el-row>
    <el-row style="margin-bottom: 20px">
      <el-col :span="24">
        <el-input
          v-model="description"
          type="textarea"
          :rows="4"
          placeholder="请输入10个字以上的内容，以使我们为您提供更好的服务"
          show-word-limit
        />
      </el-col>
    </el-row>
    <el-row style="margin-bottom: 20px">
      <el-col :span="24">
        <!-- 提交按钮 -->
        <el-button type="primary" class="submit-btn" @click="handleSubmit">
          确认提交
        </el-button>
      </el-col>
    </el-row>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from "vue";
import { ElDialog, ElInput, ElButton, ElMessage } from "element-plus";
import { addOrder } from "@/api/message/order.js";
import { getToken } from "@/utils/auth";

const props = defineProps({
  modelValue: Boolean,
});
const emit = defineEmits(["update:modelValue"]);

const handleClose = (done) => {
  emit("update:modelValue", false);
  done();
};

const title = ref("");
const description = ref("");

const isValid = computed(() => {
  return title.value.trim() !== "" && description.value.trim().length >= 10;
});

const handleSubmit = () => {
  if (!isValid.value) {
    ElMessage.warning("请填写反馈标题和至少10个字的内容");
    return;
  }
  if (getToken()) {
    const parm = {
      title: title.value,
      description: description.value,
      status: 'pending',
    };
    // 提交逻辑
    addOrder(parm)
      .then((res) => {
        if (res.code === 200) {
          ElMessage.success("提交成功");
          emit("update:modelValue", false);
          title.value = "";
          description.value = "";
        }
      })
      .catch((error) => {
        ElMessage.error("提交失败，请重试");
        console.error(error);
      });
  } else {
    // 跳转登录页
    router.push({ path: "/login" });
  }
};
</script>

<style scoped>
.remaining {
  color: #666;
  font-size: 12px;
  margin: 8px 0;
}

.upload-demo {
  margin: 15px 0;
}

.contact-input {
  margin: 15px 0;
}

.submit-btn {
  width: 100%;
}
</style>
