<template>
  <el-dialog title="发票详情" :modelValue="modelValue" width="25%" :before-close="handleClose" center>
    <div class="invoice-container">
      <!-- 步骤条 -->
      <el-steps :active="3" align-center>
        <el-step title="申请提交" :icon="CircleCheck" />
        <el-step title="财务处理" :icon="CircleCheck" />
        <el-step title="开票完成" :icon="CircleCheck" />
      </el-steps>

      <!-- 发票状态卡片 -->
      <el-card class="status-card">
        <div class="status-header">
          <span class="status-tag">已开票</span>
          <span class="amount">已开发票金额：<span style="font-size: 18px; color: #2354a1;">￥{{ form.invoiceAmount }}</span></span>
        </div>

        <el-descriptions :column="1" border>
          <el-descriptions-item label="发票类型">{{ form.invoiceType === 1 ? '数电普票' : '数电专票' }}</el-descriptions-item>
          <el-descriptions-item label="发票内容">{{ form.applyType === 1 ? '商品明细' : '商品类别' }}</el-descriptions-item>
          <el-descriptions-item label="抬头类型">{{ form.titleType === 1 ? '个人' : '企业' }}</el-descriptions-item>
          <el-descriptions-item label="抬头名称">{{ form.titleName }}</el-descriptions-item>
          <el-descriptions-item label="开票金额"> <span style="font-size: 18px; color: #2354a1;">￥{{ form.invoiceAmount }}</span></el-descriptions-item>
          <el-descriptions-item label="开票时间">{{ formatTime(form.uploadTime) }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatTime(form.createTime) }}</el-descriptions-item>
        </el-descriptions>

        <div class="action-area">
          <el-button type="primary" @click="handleReopen(form)">申请换开</el-button>
        </div>
      </el-card>

      <div class="tips">
        <p>您申请的电子普通发票已开具。</p>
        <p>点击<span class="link" @click="viewInvoice(form.invoiceFileList)">预览</span>查看发票详情</p>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ElMessageBox, ElNotification } from 'element-plus'
import dayjs from 'dayjs'
import { CircleCheck } from '@element-plus/icons-vue'
import { editEducation } from '@/api/shop/apply';
import { Base64 } from 'js-base64';
const emit = defineEmits(['update'])
const handleClose = (done) => {
  emit('update:modelValue', false);
  done();
};
const props = defineProps({
  form: Object,
  modelValue: Boolean
});


// 时间格式化
const formatTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 查看发票
const viewInvoice = (items) => {
  items.forEach(element => {
    // 实现预览文件逻辑
    const url = element.fileUrl;
    const encodedUrl = Base64.encode(url);
    const previewUrl = import.meta.env.VITE_ONLINE_PREVIEW + encodeURIComponent(encodedUrl);
    window.open(previewUrl);
  });
}

// 申请换开
const handleReopen = (form) => {
  ElMessageBox.confirm('确定要申请换开发票吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const parm = {
      applyId: form.applyId,
      changeCount: 1
    }
    editEducation(parm).then(response => {
      if (response.code === 200) {
        ElNotification({
          title: '操作提示',
          message: '申请换开成功',
          type: 'success',
        })
        handleClose();
      } else {
        ElNotification({
          title: '操作提示',
          message: response.msg,
          type: 'warning',
        })
      }
    })
  })
}
</script>

<style scoped>
.invoice-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
}

.status-card {
  margin-top: 30px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.status-tag {
  color: #67C23A;
  font-weight: bold;
}

.amount {
  color: #606266;
}

.action-area {
  margin-top: 20px;
  text-align: center;
}

.tips {
  margin-top: 20px;
  color: #909399;
  text-align: center;
}

.link {
  color: #409EFF;
  cursor: pointer;
  margin: 0 5px;
}
</style>