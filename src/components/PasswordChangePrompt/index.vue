<template>
  <div v-if="isVisible" class="modal-overlay" @click="closeModal">
    <div class="modal" @click.stop>
      <h2>密码安全提示</h2>
      <p>为保障账户安全，建议立即修改密码。</p>
      <button @click="goToChangePassword">修改密码</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { newUserVerification } from "@/api/system/user";
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();
const router = useRouter();
const isVisible = ref(false);

onMounted( async () => {
  const res = await userStore.getInfo();
  if (res.user) {
    isVisible.value = await newUserVerification();
  }
  console.log('isVisible.value',isVisible.value);
});

const closeModal = () => {
  isVisible.value = false;
};

const goToChangePassword = () => {
  closeModal();
  router.push('/basic-information'); // 假设个人中心的路由是 /personal-center
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  animation: fadeIn 0.3s ease;
}

.modal {
  background: #fff;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  text-align: center;
  max-width: 450px;
  width: 90%;
  transform: scale(0.95);
  animation: modalIn 0.3s ease forwards;
}

h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

p {
  font-size: 16px;
  line-height: 1.5;
  color: #666;
  margin: 20px 0;
}

button {
  margin-top: 24px;
  padding: 12px 32px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #3a8ffe, #2962ff);
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(41, 98, 255, 0.25);
  background: linear-gradient(135deg, #2962ff, #3a8ffe);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modalIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@media (max-width: 480px) {
  .modal {
    padding: 24px;
  }
  
  h2 {
    font-size: 20px;
  }
  
  p {
    font-size: 14px;
  }
}
</style>