<template>
  <div>
    <el-dialog :title="isOrderBusiness ? '退款详情' : '申请退款'" :modelValue="modelValue" width="35%" :before-close="handleClose">
      <el-form :model="form" label-width="100px">
        <div v-if="isOrderBusiness">
          <el-row>
            <el-col :span="24">
              <img src="@/assets/icons/svg/refund-fill.svg" class="refund-icon">
              <h3 class="refund-text" style="font-weight: bold;width: 100px;">申请退款</h3>
            </el-col>
            <el-col :span="24">
              <span class="label common-style" style="margin-left: 30px;">您已提交退款申请，请等待客服处理</span>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <h3 style="font-weight: bold;width: 100px;" class="refund-icon">退款金额</h3>
              <span class="refund-pay-amount">￥{{ form.payAmount }}</span>
            </el-col>
            <el-col :span="24" style="text-align: right;">
              <span class="label common-style" style="padding: 0 0 0 0;">按实付金额计算，原路退回</span>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <h3 style="font-weight: bold;">退款流程</h3>
            </el-col>
            <el-col :span="24">
              <el-steps style="max-width: 600px;margin-left: 30px;" :active="form.refundStatus == 1 ? 2 : 3"
                finish-status="success">
                <el-step title="提交申请" :icon="VideoPlay" />
                <el-step title="审核通过" :icon="More" />
                <el-step title="退款成功" :icon="CircleCheck" />
              </el-steps>
            </el-col>
          </el-row>
        </div>
        <el-row>
          <el-col :span="24">
            <h3 style="font-weight: bold;">教材详情</h3>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
            <el-form-item label="订单编号:" style="font-weight: bold;">
              {{ form.orderNo }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="购书码:" style="font-weight: bold;">
              <template #label>
                <span class="common-style">购书码:</span>
              </template>
              <span style="color: rgba(153, 153, 153, 1);">{{ form.code }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="group flex-col">
              <div class="text-wrapper flex-row">
                <span class="text_isbn" style="font-weight: bold;">ISBN：{{ form.isbn }}</span>
                <span class="text_priceCounter" style="font-weight: bold;">定价：{{ form.priceCounter }} 元</span>
              </div>
              <div class="text-wrapper_1 flex-row justify-between">
                <span class="text_bookName" style="font-weight: bold;">{{ form.bookName }}</span>
              </div>
              <div class="section flex-col">
                <img style="width: 100%;height: 100%;" class="img" :src="form.cover" />
              </div>
            </div>
          </el-col>
          <el-divider style="margin: 0;" />
        </el-row>
        <el-row>
          <el-col :span="24">
            <h3 style="font-weight: bold;">退款信息</h3>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="退款原因:" style="font-weight: bold;">
              召回教材
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="refund-icon">
              <el-form-item label="实际支付:" style="font-weight: bold;">
                {{ form.payAmount }}元
              </el-form-item>
            </div>
            <div class="refund-pay-amount">
              <el-form-item label="退款金额:" style="font-weight: bold;">
                <span style="font-size: 18px; color: #2354a1; font-weight: bold;">￥{{ form.payAmount }}</span>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button class="close-btn" @click="closeDialog">取消</el-button>
        <el-button v-if="isOrderBusiness" class="submit-btn" @click="closeDialog">确认</el-button>
        <el-button v-else class="submit-btn" @click="submitForm(form)">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { addRefundOrder } from '@/api/shop/refundOrder';
import { ElNotification } from 'element-plus';
import { CircleCheck, More, VideoPlay } from '@element-plus/icons-vue'
export default {
  props: {
    modelValue: {
      type: Boolean,
      required: true,
    },
    form: {
      type: Object,
      required: true,
    },
    isOrderBusiness: {
      type: Boolean,
      required: false,
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const handleClose = (done) => {
      emit('update:modelValue', false);
      done();
    };

    const closeDialog = () => {
      emit('update:modelValue', false);
    };

    const submitForm = (form) => {
      addRefundOrder(form).then((res) => {
        if (res.code === 200) {
          ElNotification({
            title: '操作提示',
            message: '退货单生成成功',
            type: 'success',
          });
          closeDialog();
        } else {
          ElNotification({
            title: '操作提示',
            message: res.msg,
            type: 'error',
          });
        }
      });
    };
    return {
      handleClose,
      closeDialog,
      submitForm,
      CircleCheck,
      More,
      VideoPlay,
    };
  },
};
</script>

<style scoped>
.dialog-footer {
  text-align: center;
}

.group {
  background-color: rgba(255, 255, 255, 1);
  height: 156px;
  position: relative;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.text-wrapper {
  height: 50px;
  margin: 39px 0 0 131px;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.text_isbn {
  width: 300px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  line-height: 70px;
}

.text-wrapper_1 {
  position: absolute;
  left: 131px;
  top: 14px;
  width: 991px;
  height: 50px;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.text_bookName {
  width: 240px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 50px;
}

.section {
  border-radius: 2px;
  height: 109px;
  width: 81px;
  position: absolute;
  left: 32px;
  top: 22px;
}

.text_priceCounter {
  line-height: 165px;
  margin-left: -300px;
  color: rgba(51, 51, 51, 1);
  font-size: 14px;
  font-weight: normal;
}

.section_3 {
  width: 1087px;
  height: 50px;
  margin: 3px 0 14px 35px;
}

:deep(.el-dialog__footer) {
  box-sizing: border-box;
  padding-top: var(--el-dialog-padding-primary);
  text-align: center;
}

.submit-btn {
  width: 200px;
  height: 42px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 16px;
  background-color: #2354a1;
  color: #ffffff;
  text-align: center;
  line-height: 42px;
  cursor: pointer;
}

.close-btn {
  width: 200px;
  height: 42px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 16px;
  text-align: center;
  line-height: 42px;
  background-color: #eaeeef;
  color: #3665a3;
  cursor: pointer;
}

.refund-icon {
  float: left;
}

.refund-text {
  float: left;
  margin: 2px 0px 0px 10px;
}

.label {
  align-items: flex-start;
  box-sizing: border-box;
  color: var(--el-text-color-regular);
  display: inline-flex;
  flex: 0 0 auto;
  font-size: var(--el-form-label-font-size);
  height: 32px;
  justify-content: flex-end;
  line-height: 32px;
  padding: 0 12px 0 0;
}

.refund-pay-amount {
  font-size: 18px;
  color: #2354a1;
  font-weight: bold;
  float: right;
  margin: 14px 0px 0px 10px;
}

.common-style {
  color: rgba(153, 153, 153, 1);
  font-weight: bold;
}
</style>