<template>
  <!-- 遮罩层 -->
  <div v-if="visible" class="modal-overlay" @click.self="close">
    <el-image :src="realSrc" fit="cover" :preview-src-list="[realSrc]">
    </el-image>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const realSrc = ref('')
const visible = ref(false)
const openView = (value) => {
 
    realSrc.value = value
    visible.value = true
  
}
const close = () => {
  sessionStorage.removeItem('v')
  visible.value = false
}

window.addEventListener('beforeunload', () => {
      // 关闭标签页时清除标记（避免影响下次打开页面）
      sessionStorage.removeItem('v');
    });
  
defineExpose({ openView })
</script>

<style scoped>
/* 遮罩层样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  /* 黑色半透明 */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* 弹窗内容样式 */
.modal-content {
  position: relative;
  max-width: 80%;
  max-height: 80%;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 关闭按钮样式 */
.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  border: none;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background 0.2s;
}

.close-btn:hover {
  background: #cc0000;
}

/* 图片样式 */
.modal-image {
  max-width: 100%;
  max-height: 70vh;
  display: block;
}
</style>