<template>
  <div>
    <el-dialog title="购买教材" :modelValue="modelValue" style="margin-top: 20vh !important;"
      width="20%" :before-close="handleClose">
      <el-form :model="form" label-position="left" ref="invoiceForm" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="教材">
              <span class="common-style">{{ form.bookName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="价格">
              <span style="font-size: 18px; color: #888; text-decoration: line-through;margin-right: 30px;">￥{{ form.priceCounter }}</span>
              <span style="font-size: 18px; color: #2354a1;">￥{{ form.priceSale }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="24">
            <el-form-item label="是否开局发票">
              <el-radio-group v-model="isInvoice">
                <el-radio :label="1" size="large">是</el-radio>
                <el-radio :label="2" size="large">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="isInvoice === 1">
          <el-col :span="24">
            <el-form-item label="发票信息">
              <el-button @click="openInvoiceInformation" link>选择开票信息</el-button>
            </el-form-item>
          </el-col>
        </el-row> -->
      </el-form>
      <template #footer>
        <el-button class="close-btn" @click="closeDialog">取消</el-button>
        <el-button class="submit-btn" @click="submitForm(form)">确认购买</el-button>
      </template>
      <!-- <InvoiceHeader v-model="showInvoiceInformationDialogFlag" @update-completed="getInfoInvoiceHeader"/> -->
      <PaymentOrder v-model="showPaymentOrderDialogFlag" :form="form" />
    </el-dialog>
  </div>
</template>

<script setup>
const props = defineProps({
  form: Object,
  modelValue: Boolean
});
// import { addInvoiceApplyEducation } from '@/api/shop/apply';
// import InvoiceHeader from './invoiceHeader.vue';
import PaymentOrder from './paymentOrder.vue';
// import { ElNotification } from 'element-plus'
import { ref } from 'vue';
const isInvoice = ref(1);
const invoiceHeaderData = ref({});
const showInvoiceInformationDialogFlag = ref(false);
const showPaymentOrderDialogFlag = ref(false);
const emit = defineEmits(['update']);
const handleClose = (done) => {
  emit('update:modelValue', false);
  done();
};

const closeDialog = () => {
  emit('update:modelValue', false);
};
const openInvoiceInformation = () => {
  showInvoiceInformationDialogFlag.value = true;

};
const submitForm = (form) => {
  form.payAmount = form.priceSale
  showPaymentOrderDialogFlag.value = true;
  // addInvoiceApplyEducation(form).then((res) => {
  //         if (res.code === 200) {
  //           ElNotification({
  //             title: '开票申请已提交',
  //             message: '发票预计将在48小时内开出，请注意查收',
  //             type: 'success',
  //           });
  //           closeDialog();
  //         } else {
  //           ElNotification({
  //             title: '操作提示',
  //             message: res.msg,
  //             type: 'warning',
  //           });
  //         }
  // });
};
const getInfoInvoiceHeader = (invoiceHeader) => {
  invoiceHeaderData.value = invoiceHeader
};
onMounted(() => {
});
</script>

<style scoped>
.dialog-footer {
  text-align: center;
}
:deep(.el-dialog__footer) {
  box-sizing: border-box;
  padding-top: var(--el-dialog-padding-primary);
  text-align: center;
}

.submit-btn {
  width: 120px;
  height: 35px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 16px;
  background-color: #2354a1;
  color: #ffffff;
  text-align: center;
  line-height: 42px;
  cursor: pointer;
}

.close-btn {
  width: 80px;
  height: 35px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 16px;
  text-align: center;
  line-height: 42px;
  background-color: #eaeeef;
  color: #3665a3;
  cursor: pointer;
}

.common-style {
  color: rgba(153, 153, 153, 1);

}
</style>