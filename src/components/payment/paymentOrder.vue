<template>
  <div>
    <el-dialog title="支付订单" :modelValue="modelValue" width="20%" :before-close="handleClose">
      <el-row style="margin: 0px 20px 20px 20px;">
        <el-col :span="24">
          <strong>订单金额为<span style="font-size: 18px; color: #2354a1;">{{ form.payAmount }}</span>元，请使用{{ isPay === 'WeChat' ? '微信' : '支付宝'}}扫码支付。</strong>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" style="margin-bottom:10px ;">
          <el-radio-group v-model="isPay" size="large">
            <el-radio-button label="微信支付" value="WeChat" />
            <el-radio-button label="支付宝支付" value="Alipay" />
          </el-radio-group>
        </el-col>
        <div v-if="isPay === 'WeChat'" style="margin: 20px 0px 20px 50px;">
          <qrcode-vue :value="weChatPayCode" :size="250"/>
        </div>
        <div v-if="isPay === 'Alipay'" style="margin: 20px 0px 20px 50px;">
          <qrcode-vue :value="aliPayCode" :size="250"/>
        </div>
      </el-row>
    </el-dialog>
  </div>
</template>
<script setup>
const props = defineProps({
  form: Object,
  modelValue: Boolean
});
const isPay = ref('WeChat');
const weChatPayCode = ref('')
const aliPayCode = ref('')
import { wechatPrepay, alPrepay } from '@/api/shop/pay';
import { onMounted } from 'vue';
// 引入QRCodeVue组件
import QrcodeVue from 'qrcode.vue'

const emit = defineEmits(['update']);
const handleClose = (done) => {
  emit('update:modelValue', false);
  done();
};

const closeDialog = () => {
  emit('update:modelValue', false);
};
onMounted (() => {
    const parm = {
      totalFee: props.form.payAmount
    }
    wechatPrepay(parm).then(res => {
      weChatPayCode.value = res.data;
    })
    alPrepay(parm).then(res => {
      weChatPayCode.value = res.data;
    })
})
</script>
<style scoped lang="scss">
:deep(.el-radio-button__inner) {
  border-left: var(--el-border);
  border-radius: var(--el-border-radius-base) 0 0 var(--el-border-radius-base);
  box-shadow: none !important;
  width: 170px;
}
</style>