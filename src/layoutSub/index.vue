<template>
  <div class="layout-sub-sty">
    <div class="page-nav-container">
      <div class="nav-con">
        <headNavComp @toLogin="toLogin" :showBackground="true" />
      </div>
    </div>
    <div class="page-content-con">
      <!-- <app-main /> -->
      <router-view />
    </div>
    <!-- 底部   -->
    <div class="foot-con">
      <footComp />
    </div>
  </div>
</template>

<script setup name="SearchPage">
import footComp from '@/views/home/<USER>/footComp/index.vue';
import headNavComp from '@/views/home/<USER>/headNavComp/index.vue';
import { useRouter } from 'vue-router';
import { AppMain } from '@/layout/components/index.js';

const router = useRouter();
function toLogin() {
  router.push({ path: '/login' });
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/index.scss';
.layout-sub-sty {
  width: 100%;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
  .page-nav-container {
    width: 100%;
    .nav-con {
      width: 100%;
    }
  }
  .page-content-con {
    width: 100%;
    min-height: calc(100vh - 288px);
    /*align-self: stretch;*/
    @extend .base-flex-column;
    justify-content: flex-start;
    align-items: center;
  }
  .foot-con {
    width: 100%;
  }
}
</style>
