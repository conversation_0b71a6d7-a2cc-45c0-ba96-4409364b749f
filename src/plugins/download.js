import axios from 'axios'
import { ElLoading, ElMessage } from 'element-plus'
import { saveAs } from 'file-saver'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { blobValidate } from '@/utils/dutp'
import JSZip from 'jszip';

const baseURL = import.meta.env.VITE_APP_BASE_API
let downloadLoadingInstance;

export default {
  zip(url, name) {
    var url = url
    downloadLoadingInstance = ElLoading.service({ text: "正在下载数据，请稍候", background: "rgba(0, 0, 0, 0.7)", })
    axios({
      method: 'get',
      url: url,
      responseType: 'blob',
      headers: { 'Authorization': 'Bearer ' + getToken(), 'Language': sessionStorage.getItem('Language'),
        'kk-proxy-authorization': JSON.stringify({Referer: 'https://ebook.dutp.cn'})
       }
    }).then((res) => {
      const isBlob = blobValidate(res.data);
      if (isBlob) {
        const blob = new Blob([res.data], { type: 'application/zip' })
        this.saveAs(blob, name)
      } else {
        this.printErrMsg(res.data);
      }
      downloadLoadingInstance.close();
    }).catch((r) => {
      console.error(r)
      ElMessage.error('下载文件出现错误，请联系管理员！')
      downloadLoadingInstance.close();
    })
  },
  zipDownload(files, zipName) {
    const zip = new JSZip();
    const downloadLoadingInstance = ElLoading.service({
      text: "正在下载数据，请稍候",
      background: "rgba(0, 0, 0, 0.7)",
    });

    const token = getToken();
    const language = sessionStorage.getItem('Language');

    const requests = files.map(({ fileUrl, fileName }) => {
      return axios({
        method: 'get',
        url: fileUrl,
        responseType: 'blob',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Language': language
        }
      }).then((res) => {
        const isBlob = blobValidate(res.data);
        if (isBlob) {
          zip.file(fileName, res.data); // 将文件添加到 ZIP 中，使用指定的文件名
        } else {
          console.warn(`文件无效 ${fileUrl}`);
        }
      }).catch((err) => {
        console.error(`下载错误 ${fileUrl}:`, err);
      });
    });

    Promise.all(requests).then(() => {
      zip.generateAsync({ type: 'blob' }).then((content) => {
        saveAs(content, zipName); // 保存 ZIP 文件
        downloadLoadingInstance.close();
      });
    }).catch((err) => {
      console.error('生成ZIP时出错:', err);
      ElMessage.error('下载文件出现错误，请联系管理员！');
      downloadLoadingInstance.close();
    });
  },

  saveAs(text, name, opts) {
    saveAs(text, name, opts);
  },
  async printErrMsg(data) {
    const resText = await data.text();
    const rspObj = JSON.parse(resText);
    const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
    ElMessage.error(errMsg);
  }
}

