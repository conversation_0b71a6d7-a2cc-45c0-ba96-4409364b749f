import { createWebHistory, createRouter } from 'vue-router';
/* Layout */
import Layout from '@/layout';
import LayoutSub from '@/layoutSub';
import user from '@/views/user/home/<USER>';
import Reader from '@/views/reader/index.vue';
import SimplifiedReader from '@/views/reader/simplified.vue';
import RunCode from '@/views/runCode/index.vue';
import { DUTP_FONT_URL } from '@/utils/constant.js';
import { loadDutpFonts } from '@/utils/dutpFontUtils';
/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
 noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
 title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
 icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
 breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
 activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
 }
 */
/*async function preloadFont() {
  try {
    // 1. 创建字体对象
    const font = new FontFace(
        'FZCHYK',
        'url(http://192.168.0.40/font/test-font/FZCHYK.TTF)',
        {
          style: 'normal',
          weight: '400',
          display: 'swap'
        }
    );

    // 2. 加载字体
    const loadedFont = await font.load();
    // 3. 将字体添加到文档
    document.fonts.add(loadedFont);

    // 4. 验证字体是否可用
    await document.fonts.ready;
    // fontStatus.value = 'loaded';
  } catch (err) {
    console.error('字体加载失败:', err);
    // fontStatus.value = 'error';
  }
}*/

// 公共路由
export const constantRoutes = [
  {
    path: '/home',
    component: () => import('@/views/index'),
    name: 'Home',
    meta: { title: '首页' },
    beforeEnter: () => {
      // 预加出版社载字体
      // loadDutpFonts(['FZCHYK', 'FZCYK']);
    },
  },
  {
    path: '/page',
    component: () => import('@/views/page'),
    name: 'Page',
  },
  {
    path: '/sub-page',
    component: () => import('@/layoutSub/index'),
    name: 'SubPageLayout',
    hidden: true,
    children: [
      {
        path: '/search',
        component: () => import('@/views/search/index'),
        name: 'Search',
        meta: { title: '' },
      },
      {
        path: '/search-advanced',
        component: () => import('@/views/searchAdvanced/index'),
        name: 'SearchAdvanced',
        meta: { title: '' },
      },
      {
        path: '/search-catalog',
        component: () => import('@/views/searchCatalog/index'),
        name: 'SearchCatalog',
        meta: { title: '' },
      },
      {
        path: '/search-catalog',
        component: () => import('@/views/searchCatalog/index'),
        name: 'SearchCatalog',
        meta: { title: '' },
      },
      {
        path: '/search-result',
        component: () => import('@/views/search/searchResult/index'),
        name: 'SearchResult',
        meta: { title: '' },
      },
      {
        path: '/book-detail',
        component: () => import('@/views/book/bookDetail/index'),
        name: 'BookDetail',
        meta: { title: '' },
      },
      {
        path: '/book-natural-resources',
        component: () => import('@/views/book/bookNaturalResources/index'),
        name: 'BookNaturalResources',
        meta: { title: '' },
      },
      {
        path: '/special-subject-books-list',
        component: () => import('@/views/home/<USER>/index'),
        name: 'SpecialSubjectBooksList',
        meta: { title: '专题教材列表' },
      },
      {
        path: '/contact-submit-article',
        component: () => import('@/views/home/<USER>/index'),
        hidden: false,
        name: 'ContactSubmitArticle',
        meta: { title: '我要投稿' },
      },
      {
        path: '/contact-us',
        component: () => import('@/views/home/<USER>/index'),
        hidden: false,
        name: 'ContactUs',
        meta: { title: '联系我们' },
      },
      {
        path: '/copyright-notice',
        component: () => import('@/views/home/<USER>/index'),
        hidden: false,
        name: 'CopyrightNotice',
        meta: { title: '版权声明' },
      },
      {
        path: '/site-instructions',
        component: () => import('@/views/home/<USER>/index'),
        hidden: false,
        name: 'SiteInstructions',
        meta: { title: '站点使用说明' },
      },
      {
        path: '/home-partners-school',
        component: () => import('@/views/home/<USER>/partnersSchoolComp/index'),
        name: 'PartnersSchool',
        meta: { title: '合作院校' },
      },
      {
        path: '/partner-school-books',
        component: () => import('@/views/home/<USER>/schoolBooksList/index'),
        name: 'HomeSchoolBooks',
        meta: { title: '合作院校图书列表' },
      },
      {
        path: '/digital-platforms',
        component: () => import('@/views/home/<USER>/index'),
        name: 'TeachingPlatform',
        meta: { title: '教学平台' },
      },
    ],
  },
  {
    path: '/about-us',
    component: () => import('@/views/home/<USER>/index.vue'),
    hidden: true,
  },
  {
    path: '/book-codes',
    component: () => import('@/views/home/<USER>/bookCodes/index'),
    name: 'BookCodes',
    meta: { title: '购书码' },
  },
  {
    path: '/reader',
    component: Reader,
    hidden: false,
    name: 'Reader',
    meta: { title: '阅读器' },
  },
  {
    path: '/simplifiedreader',
    component: SimplifiedReader,
    component: () => import('@/views/reader/simplified'),
    hidden: false,
    name: 'SimplifiedReader',
    meta: { title: '预览章节内容' },
  },
  {
    path: '/bookReader',
    component: SimplifiedReader,
    component: () => import('@/views/reader/bookShareReader'),
    hidden: false,
    name: 'BookShareReader',
    meta: { title: '预览全书内容' },
  },
  {
    path: '/runCode',
    component: RunCode,
    hidden: false,
    name: 'runCode',
    meta: { title: '阅读器-运行代码' },
  },
  {
    path: '/user',
    component: user, // 使用 Layout 或其他主页面布局组件
    redirect: '/basic-information', // 默认跳转到 "基本信息" 页面
    children: [
      // 顶部菜单列表
      {
        path: '/my-message',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'MyMessage',
        meta: { title: '我的消息' },
      },
      {
        path: '/basic-information',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'BasicInformation',
        meta: { title: '基本信息' },
      },
      {
        path: '/purchase-history',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'PurchaseHistory',
        meta: { title: '我的书架' },
      },
      {
        path: '/collect',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'Collect',
        meta: { title: '我的收藏' },
      },
      {
        path: '/browsing-history',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'BrowsingHistory',
        meta: { title: '浏览历史' },
      },
      {
        path: '/book-purchase-code',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'BookPurchaseCode',
        meta: { title: '我的购书码' },
      },
      {
        path: '/my-order',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'MyOrder',
        meta: { title: '我的订单' },
      },
      {
        path: '/error-correction',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'ErrorCorrection',
        meta: { title: '我的慧点' },
      },
      {
        path: '/basic-information',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'StudyReport',
        meta: { title: '学习报告' },
      },
      {
        path: '/myCorrection',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'MyCorrection',
        meta: { title: '我的纠错' },
      },
      {
        path: '/invoice-header',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'InvoiceHeader',
        meta: { title: '发票中心' },
      },
      // 中部菜单列表
      {
        path: '/basic-information',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'MyClass',
        meta: { title: '我的班级' },
      },
      {
        path: '/identity',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'Identity',
        meta: { title: '身份认证' },
      },
      {
        path: '/application-history',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'ApplicationHistory',
        meta: { title: '申请历史' },
      },

      // 底部菜单列表
      {
        path: '/feedback',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'Feedback',
        meta: { title: '意见反馈' },
      },
    ],
  },
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue'),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true,
  },
  {
    path: '/user-agreement',
    component: () => import('@/views/userAgreement'),
    hidden: true,
  },
  {
    path: '/privacy-policy',
    component: () => import('@/views/privacyPolicy'),
    hidden: true,
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true,
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/error/404'),
    hidden: true,
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true,
  },
  {
    path: '/error/token-expired',
    component: () => import('@/views/error/TokenExpired'),
    hidden: true,
  },
  {
    path: '/agreement',
    component: () => import('@/views/user/home/<USER>/agreement.vue'),
    hidden: true,
  },
  {
    path: '',
    component: Layout,
    redirect: '/index',
    children: [
      {
        path: '/index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true },
      },
    ],
  },
];
// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [];

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  // 是否滚动行为
  scrollBehavior(to, from, savePosition) {
    if (savePosition) {
      return savePosition;
    } else {
      return { top: 0 };
    }
  },
});

export default router;
