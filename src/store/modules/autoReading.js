import { nextTick, ref, watch } from 'vue'
import { PAGE_ITEMS_CONTAINER_ID, PAGE_TURNING_ERROR_MAPPER } from '@/utils/reader'
import useReader, { READING_MODE } from '@/store/modules/reader'
import { ElMessage } from 'element-plus'

let readerStore

export const AUTO_READING_RUNNING_STATUS = {
  RUNNING: 'running',
  PAUSED: 'pause',
  STOPPED: 'stop'
}

/**
 * 自动阅读的控制器
 */
class _TaskScheduling {
  constructor() {
    this.realReadingCountingDown = ref(0)
    this.taskTimer = null //自动阅读计时器的key
    this.countingDownTimer = null
    this.speed = {} //自动阅读的速度
    this.pageTurningTime = {
      one: {
        y: {
          slow: 20,
          in: 15,
          fast: 5
        },
        // x: {
        //   slow: 10000,
        //   in: 15000,
        //   fast: 20000
        // },
        r: {
          slow: 20000,
          in: 15000,
          fast: 10000
        }
      }
      // 双栏暂时不要了
      // two: {
      //   x: {
      //     slow: 10000,
      //     in: 20000,
      //     fast: 20000
      //   },
      //   r: {
      //     slow: 20000,
      //     in: 30000,
      //     fast: 40000
      //   },
      // }
    }
    this.t = null //截留用
    this.pageFlippingMethod = 'y'
  }
  resetAutoReadingConfig(opts) {
    this.pageFlippingMethod = opts.pageFlippingMethod || this.pageFlippingMethod
  }
  /**
   * 避免闭包造成数据不同步，获取最新值
   */
  getTer(keys) {
    return this[keys]
  }

  /**
   * 初始化任务
   */
  init(speed, opts = {}) {
    this.speed = speed
    this.resetAutoReadingConfig(opts)
    this.stop(true)
    this.start(speed)
  }
  /**
   *获取翻页速度与时间
   */
  getPageTurningTime(type) {
    console.log(type)
    const flex = 'one'
    const pageFlippingMethod = this.pageFlippingMethod
    return this.pageTurningTime[flex][pageFlippingMethod][type.value]
  }

  /**
   * 单栏-上下滑动-鼠标滚轮事件
   * */
  one_y_mousewheel() {
    this.branchTask(resolve => {
      clearTimeout(this.t)
      this.t = setTimeout(() => {
        resolve()
        this.t = null
      }, 200)
    })
  }

  /**
   * 开始任务
   */
  start(speed) {
    console.log(speed)
    console.log(this.pageFlippingMethod)
    this.destruction()
    this.speed = speed
    const bookReadingStore = { flex: 'one' }
    if (bookReadingStore.flex === 'one') {
      let t = this.getPageTurningTime(speed)
      console.log(t)
      switch (this.pageFlippingMethod) {
        case 'r':
          this.taskTimer = setInterval(() => {
            readerStore
              .nextPage()
              .then(() => {
                this.realReadingCountingDown = t / 1000
                clearInterval(this.countingDownTimer)
                this.countingDownTimer = setInterval(() => {
                  this.realReadingCountingDown--
                  // console.log(this.realReadingCountingDown)
                }, 1000)
              })
              .catch(err => {
                if (err.code === PAGE_TURNING_ERROR_MAPPER.LAST_PAGE_IN_BOOK.code) {
                  // 到底最后一页的话停止自动翻页
                  useAutoReader().pause()
                  ElMessage.info('您已阅读到教材最后一页，5秒后将退出自动阅读')
                  setTimeout(() => {
                    readerStore.setReading(READING_MODE.GENERAL)
                  }, 5000)
                }
              })
          }, t)
          this.realReadingCountingDown = t / 1000
          clearInterval(this.countingDownTimer)
          this.countingDownTimer = setInterval(() => {
            this.realReadingCountingDown--
            // console.log(this.realReadingCountingDown)
          }, 1000)
          break
        case 'y':
          document.addEventListener('wheel', this.one_y_mousewheel)
          this.taskTimer = setInterval(() => {
            const BOX = document.querySelector(`#${PAGE_ITEMS_CONTAINER_ID}`)
            BOX.scrollTop += 1
          }, t)
          break
        // 水平自动阅读暂时不要了
        // case 'x':
        //   const { nextPage } = this.useReader()
        //   const element = document.querySelector('.content-pages')
        //   //判断是否有滚动条
        //   if (element.scrollHeight > element.clientHeight) {
        //     const _t = setInterval(() => {
        //       element.scrollTop += 1
        //       //竖向滚动条已经滚动到底部
        //       if (element.clientHeight + element.scrollTop === element.scrollHeight) {
        //         clearInterval(_t)
        //       }
        //     }, 10)
        //   }
        //   this.taskTimer = setInterval(() => {
        //     if (this.getTer('taskStatus')) {
        //       //重置滚动条状态
        //       if (element.scrollHeight > element.clientHeight) {
        //         element.scrollTop = 0
        //       }
        //       nextPage()
        //     } else {
        //       clearInterval(this.getTer('taskTimer'))
        //       this.taskTimer = null
        //     }
        //   }, t)
        //   break

        default:
          break
      }
    }
    // 双栏暂时不要了
    // else if (bookReadingStore.flex === 'two') {
    //   let t = this.getPageTurningTime(speed)
    //   const { nextPage } = this.useReader()

    //   switch (bookReadingStore.pageFlippingMethod) {
    //     case 'x':
    //       this.taskTimer = setInterval(() => {
    //         if (this.getTer('taskStatus')) {
    //           nextPage()
    //         } else {
    //           clearInterval(this.getTer('taskTimer'))
    //           this.taskTimer = null
    //         }
    //       }, t)
    //       break;

    //     case "r":
    //       // __pageItemContent__
    //       this.taskTimer = setInterval(() => {
    //         if (this.getTer('taskStatus')) {
    //           nextPage()
    //         } else {
    //           clearInterval(this.getTer('taskTimer'))
    //           this.taskTimer = null
    //         }
    //       }, t)
    //       break
    //     default:
    //       break;
    //   }
    // }
  }

  /**
   * 暂停
   */
  stop(type) {
    if (this.getTer('taskTimer')) {
      clearInterval(this.getTer('taskTimer'))
      clearInterval(this.getTer('countingDownTimer'))
      if (type) this.taskTimer = null
    }
  }

  /**
   * 用作暂停处理其他逻辑
   */
  branchTask(callback) {
    new Promise(async (resolve, reject) => {
      this.stop()
      await nextTick() //确定自动阅读副作用消失，在执行回调
      callback(resolve, reject)
    }).then(res => {
      //用this.taskTimer 是否为null判断当前是否为自动阅读得状态
      if (!this.taskTimer) return
      let time = 1000
      if (typeof res === 'number') time = res
      //部分翻页需要时间加载，所以这里用异步处理
      const t = setTimeout(() => {
        this.start(res && typeof res !== 'number' ? res : this.speed)
        clearTimeout(t)
      }, time)
    })
  }

  /**
   * 销毁任务队列
   */
  destruction() {
    this.stop(true)
    document.removeEventListener('wheel', this.one_y_mousewheel)
  }
}

const TaskScheduling = new _TaskScheduling()
TaskScheduling.one_y_mousewheel = TaskScheduling.one_y_mousewheel.bind(TaskScheduling)

const useAutoReader = defineStore('autoReading', {
  state: () => ({
    readingInProgress: '', // 自动阅读正在运行: false, true
    autoTask: TaskScheduling
  }),
  actions: {
    start(speed) {
      this.readingInProgress = AUTO_READING_RUNNING_STATUS.RUNNING
      this.autoTask.start(speed)
    },
    pause() {
      this.readingInProgress = AUTO_READING_RUNNING_STATUS.PAUSED
      this.autoTask.stop(true)
    },
    init(speed, opts) {
      readerStore = readerStore || useReader()
      this.readingInProgress = AUTO_READING_RUNNING_STATUS.RUNNING
      this.autoTask.init(speed, opts)
    },
    destruction() {
      this.readingInProgress = AUTO_READING_RUNNING_STATUS.STOPPED
      this.autoTask.destruction()
    }
  }
})

export default useAutoReader
