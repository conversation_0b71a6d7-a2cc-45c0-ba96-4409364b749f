import { DUTP_FONT_URL } from '@/utils/constant.js';
export const useFontStore = defineStore('fonts', {
  state: () => ({
    loadedFonts: new Set(),
  }),
  actions: {
    loadDutpFonts(otherFontsNameArr) {

      return new Promise((resolve) => {
        const intersection = otherFontsNameArr.map((fontName) => {
          let name = fontName.split('.')[0];
          return { name, url: `${DUTP_FONT_URL}${fontName}` };
        });
        if (intersection.length > 0) {
          intersection.map(async (font) => {
            if (!this.loadedFonts.has(font.name)) {
              const fontFace = new FontFace(font.name, `url(${font.url})`);
              await fontFace.load();
              // 加载不到字体的异常提示
              if (!fontFace) {
                console.log(`字体不存在: ${font.name}`);
              }
              document.fonts.add(fontFace);
            }
          })
        }
        resolve();
      })

    }
  },
});
