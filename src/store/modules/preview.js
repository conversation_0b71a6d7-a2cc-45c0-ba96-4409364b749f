
import { uuid } from "@/utils/index.js"
const usePreview = defineStore('preview', {
  state: () => ({
    url: '',
    title: '',
    uuid: 'aa',//触发watch
    isConvertUrl: true,//是否转换url地址
    allResourceLimages: [],
    currentOnShowImageUrl: undefined
  }),
  actions: {
    setCurrentImage(imageItem) {
      this.currentOnShowImageUrl = imageItem
    },
    setPreviewImages(images) {
      this.allResourceLimages = images
    },
    setData(val) {
      const { url, title } = val
      this.url = url
      this.title = title
      this.isConvertUrl = true
      this.uuid = uuid()
    },
    originalPreview(val) {
      const { url, title } = val
      this.url = url
      this.title = title
      this.isConvertUrl = false
      this.uuid = uuid()
    }
  },
});

export default usePreview;
