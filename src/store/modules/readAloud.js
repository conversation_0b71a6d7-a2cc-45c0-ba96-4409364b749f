
export const TTS_READING_STATUS = {
  RUNNING: 'running',
  PAUSED: 'pause',
  STOPPED: 'stop'
}

const useReadAloud = defineStore('readAloud', {
  state: () => ({
    readingStatus: TTS_READING_STATUS.STOPPED,
    textInReading: '',

    readAloudStatus: false,//朗读的控制面板
    rate: 1,//语速
    pitch: 1.2,//音高
    volume: 0.8,//音量
  }),
  actions: {
    startReadWithAloud(text) {
      this.textInReading = text
    },
    setReadingStatus(val) {
      this.readingStatus = val
    },
                    //修改朗读控制面板的状态
    setReadAloudStatus(val) {
      this.readAloudStatus = val
    }
  },
});

export default useReadAloud;
