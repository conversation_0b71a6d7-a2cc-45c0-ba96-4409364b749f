import { getSiteInfoById, getMenus } from '@/api/basic/home.js';
import { listDutpSubjectEducation } from '@/api/basic/subject';
import { getPublicKey } from '@/api/encryptApi.js';
import { SiteId } from '@/utils/constant';

const queryParams = {
  pageNum: 1,
  pageSize: 100,
  subjectName: null,
  sort: null,
};
const useSiteStore = defineStore('siteInfo', {
  state: () => ({
    siteInfo: {},
    menus: [],
    publicKey: '',
    subjectList: [],
  }),
  actions: {
    getSubjectList() {
      return new Promise((resolve, reject) => {
        listDutpSubjectEducation(queryParams)
          .then((response) => {
            let searchParamsList = [];
            let tempResponseData = JSON.parse(JSON.stringify(response.data));
            const keys = Object.keys(tempResponseData);
            if (keys && keys.length > 0) {
              keys.forEach((key, index) => {
                if (tempResponseData[key] && tempResponseData[key].length > 0) {
                  searchParamsList = searchParamsList.concat(tempResponseData[key]);
                }
              });
            }
            this.subjectList = searchParamsList;
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    getSiteInfo() {
      if (!this.siteInfo.siteName) {
        return new Promise((resolve, reject) => {
          getSiteInfoById(SiteId)
            .then((res) => {
              this.siteInfo = res.data;
              resolve();
            })
            .catch((error) => {
              reject(error);
            });
        });
      }
    },
    getMenuInfo() {
      if (this.menus == 0) {
        return new Promise((resolve, reject) => {
          getMenus()
            .then((res) => {
              this.menus = res.data;
              this.menus?.unshift({ menuName: '首页', menuRoute: '/home', routeType: 1, sort: 0 });
              resolve();
            })
            .catch((error) => {
              reject(error);
            });
        });
      }
    },
    doGetPublicKey() {
      if (!this.publicKey) {
        return new Promise((resolve, reject) => {
          getPublicKey()
            .then((res) => {
              this.publicKey = res;
              resolve();
            })
            .catch((error) => {
              reject(error);
            });
        });
      }
    },
  },
});

export default useSiteStore;
