import { getSiteConfig } from '@/api/system/siteConfig';

const useSiteConfigStore = defineStore('siteConfig', {
  state: () => ({
    siteConfigInfo: {},
  }),
  actions: {
    getSiteConfigInfo() {
      return new Promise((resolve, reject) => {
        let _configId = 1;
        getSiteConfig(_configId)
          .then((reps) => {
            this.siteConfigInfo = reps.data;
            resolve();
          })
          .catch((error) => {
            reject();
          });
      });
    },
  },
});

export default useSiteConfigStore;
