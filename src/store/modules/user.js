import { login, logout, getInfo, codeLogin } from '@/api/login';
import { getToken, setToken, setTokenExp, removeToken, setExpiresIn } from '@/utils/auth';
import defAva from '@/assets/images/profile.png';

const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    id: '',
    name: '',
    nickName: '',
    avatar: '',
    roles: [],
    permissions: [],
  }),
  actions: {
    // 登录
    scanCodeToLogin(access_token) {
      return new Promise((resolve) => {
        setToken(access_token);
        this.token = access_token;
        resolve();
      });
    },
    // 登录
    login(userInfo) {
      return new Promise((resolve, reject) => {
        login(userInfo)
          .then((res) => {
            if (res.code === 200) {
              let data = res.data;
              let expiresMinuteIn = data.expires_in ? Number(data.expires_in) : 12 * 60; // 分钟
              let timeTamp = new Date().getTime() + expiresMinuteIn * 60 * 1000;
              setTokenExp(data.access_token, timeTamp);
              // setToken(data.access_token);
              this.token = data.access_token;
              resolve();
            } else {
              // 如果返回的状态码不是 200，将错误信息传递给 reject
              reject(res);
            }
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 验证码登录
    codeLogin(userInfo) {
      return new Promise((resolve, reject) => {
        codeLogin(userInfo)
          .then((res) => {
            if (res.code === 200) {
              let data = res.data;
              let expiresMinuteIn = data.expires_in ? Number(data.expires_in) : 12 * 60; // 分钟
              let timeTamp = new Date().getTime() + expiresMinuteIn * 60 * 1000;
              setTokenExp(data.access_token, timeTamp);
              this.token = data.access_token;
              resolve();
            } else {
              // 如果返回的状态码不是 200，将错误信息传递给 reject
              reject(res);
            }
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 获取用户信息
    getInfo() {
      return new Promise((resolve, reject) => {
        getInfo()
          .then((res) => {
            const user = res.user ?? {};
            const avatar = user.avatar == '' || user.avatar == null ? defAva : user.avatar;

            if (res.roles && res.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              this.roles = res.roles;
              this.permissions = res.permissions;
            } else {
              this.roles = ['ROLE_DEFAULT'];
            }
            this.id = user.userId;
            this.name = user.userName;
            this.nickName = user.nickName;
            this.avatar = avatar;
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 退出系统
    logOut() {
      return new Promise((resolve, reject) => {
        logout(this.token)
          .then(() => {
            this.token = '';
            this.roles = [];
            this.permissions = [];
            removeToken();
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
  },
});

export default useUserStore;
