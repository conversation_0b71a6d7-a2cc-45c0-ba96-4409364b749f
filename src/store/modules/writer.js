import { defineStore } from "pinia";

const useWriterStore = defineStore("writer", {
  state: () => ({
    paragraph: '',
    fontFamilyValue: '',
    fontSizeValue: '',
    fontStyleValue: '',
    showColorPicker: false,
    fontColor: '',
    type: '',
    justify: '',
    boolCopy: false,
    copyText: '',
    orderListType: '',
    orderDialog: false,
    orderItem: '',
    lineHeightShow: false,
    lineHeightValue: '',
    showFormula: false,
    formulaValue: '',
    linkDialog: false,
    linkObject: {},
    chapterHead: '',
    sectionHeader: '',
    chapterHeadId: '',
    sectionHeadId: '',
    popupDialog: false,
    audioId: '',
    audioObject: '',
    videoId: '',
    videoObject: '',
    tableRows: '',
    tableCol: '',
    selectMode: false,
    tableHtml: '',
    tableCss: '',
    tableId: '',
    imgDialog: false,
    imgType: '',
    tableModelId: '',
    coursewareData: '',
    coursewareDataId: '',
    event: {},
    tempHtml: '',
    tempCss: '',
    tempId: '',
    appendchildStyle: null,
    imgUrlObject: {},
    frameDocument: {},//iframe内的document对象
    coursewareDialog: false,
    coursewareDialogKey: null,
    galleryImgs: '',
    galleryText: '',
    galleryImgsId: '',
    hanlineHtml: '',
    hanlineModelId: '',

    hanlineKey: '',
    hanlineDialog: false,
    hanlineUrl: '',
    hanlineResult: null,
    hanlineEvent: {},
  }),
  actions: {
    changeParagraph(newParagraph) {
      this.paragraph = newParagraph;
    },
    changeFontFamily(newFontFamily) {
      this.fontFamilyValue = newFontFamily;
    },
    changeFontSize(newFontSize) {
      this.fontSizeValue = newFontSize;
    },
    changeFontStyle(newFontStyle) {
      this.fontStyleValue = newFontStyle;
    },
    changedshowColorPicker(newshowColorPicker, type) {

      this.showColorPicker = newshowColorPicker;
      this.type = type;
    },
    changedColor(newColor) {
      this.fontColor = newColor;
    },
    changeJustify(newJustify) {
      this.justify = newJustify;
    },
    getHtmlText() {
      return this.copyText;
    },
    handleOrderListOpen(type) {

      this.orderDialog = true;
      this.orderListType = type;
    },
    orderSelect(newOrder) {
      this.orderItem = newOrder;
    },
    changeClickLineHeight(show) {
      this.lineHeightShow = show;
    },
    changeClickFontOutlineHeight(show) {
      this.fontOutlineHeightShow = show;
    },
    setLineHeight(newLineHeight) {
      this.lineHeightValue = newLineHeight;
    },
    setFontOutline(newOutline) {
      this.fontOutlineValue = newOutline;
    },
    setShowFormula(show) {
      this.showFormula = show;
    },
    setFormulaValue(newFormulaValue) {
      this.formulaValue = newFormulaValue;
    },
    setShowLinkDialog(show) {
      this.linkDialog = show;
    },
    setLink(newObject) {
      this.linkObject = newObject;
      this.linkDialog = false;
    },
    addChapterHead(html, num) {
      this.chapterHeadId = num;
      this.chapterHead = html;
    },
    addSectionHeader(html, num) {
      this.sectionHeadId = num;
      this.sectionHeader = html;
    },
    setShowPopupDialog(show) {
      this.popupDialog = show;
    },
    addAudio(newObject, num) {
      this.audioId = num;
      this.audioObject = newObject;
    },
    addVideo(newObject, num) {
      this.videoId = num;
      this.videoObject = newObject;
    },
    addTable(newRows, newCol, id) {
      this.tableRows = newRows;
      this.tableCol = newCol;
      this.tableId = id;
    },
    setSelectModeShow(show) {
      this.selectMode = show;
    },
    setTableHtml(html, css, id) {
      this.tableHtml = html;
      this.tableCss = css;
      this.tableModelId = id;
    },
    setTempHtml(html, css, id) {
      this.tempHtml = html;
      this.tempCss = css;
      this.tempModelId = id;
    },
    setImgDialogShow(show, type) {
      this.imgDialog = show;
      this.imgType = type;
    },
    setCoursewareDialog(show, key) {
      this.coursewareDialog = show
      this.coursewareDialogKey = key
    },
    setCoursewareData(html, key) {
      this.coursewareData = html
      this.coursewareDataId = key
    },
    setEvent(key, fun) {
    
      if (!key || !fun) return new Error('请将参数填写完整!')
      this.event[key] = fun
    },
    setAppendchildStyle(fun) {
      this.appendchildStyle = fun
    },
    setImgUrl(newObject) {
      this.imgUrlObject = newObject
    },
    setFrameDocument(val) {
      this.frameDocument = val
    },
    setGalleryImgs(html, id) {

      this.galleryImgs = html
      this.galleryImgsId = id
    },

    setHanlineHtml(html, id) {
      this.hanlineHtml = html;

      this.hanlineModelId = id;
    },
    setHanlineDialog(key,fun) {
      this.hanlineKey = key;
      this.hanlineEvent[key] = fun;

    }




  },
})

export default useWriterStore;