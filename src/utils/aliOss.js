/*
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-10-18 16:36:51
 * @LastEditTime: 2024-10-25 15:22:56
 * @FilePath: \App\dutp-admin-vue\src\utils\aliOss.js
 * @Description:
 *
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
 */

import { ElMessage } from 'element-plus'
import { uploadFile } from '@/api/file'

export const OssService = async (file, progressCallback) => {
  if (!file) {
    return Promise.reject('请选择文件')
  }
  const formData = new FormData()
  formData.append('file', file)
  try {
    const data = await uploadFile(formData)
    return { url: data.data.url, result: data, originName: file.name }
  } catch (err) {
    console.log('err', err)
    return Promise.reject(err)
  }
}

/**
 * @config 上传文件得配置
 * @ config.accept允许的文件类型 ".jpg,.png"
 * @ config.multiple 是否允许上传多个 config.maxLimit不为0 时 config.multiple自动为true
 * @ config.maxSize 最大欲奴上传大小  单位 MB
 * @ config.maxLimit 最多上传个数
*/
export const _uploads = (config) => {
  const _config = { accept: '', multiple: false, maxSize: 99999, maxLimit: 0, ...config }
  if (_config.maxLimit) _config.multiple = true
  return new Promise((resolve, reject) => {
    const InputFile = document.createElement('input')
    InputFile.type = 'file'
    InputFile.accept = _config.accept //文件类型
    InputFile.multiple = _config.multiple //是否多文件上传
    InputFile.click()
    InputFile.addEventListener('change', (e) => {
      const files = e.target.files
      const ossList = []
      config?.changeFn?.()
      if (files.length > _config.maxLimit && _config.maxList) {
        ElMessage({
          message: `上传文件数量不能超过 ${_config.maxLimit} 个!`,
          type: 'error',
        })
        reject()
        return
      }

      for (let i = 0; i < files.length; i++) {
        const ele = files[i]
        if (ele.size / 1024 / 1024 > _config.maxSize) {
          ElMessage({
            message: `请上传不大于${_config.maxSize}MB的文件`,
            type: 'error',
          })
          new Error('文件太大啦')
          break
        }
        ossList.push(OssService(ele))
      }
      Promise.all(ossList)
        .then((res) => {
          resolve(res)
          InputFile.remove && InputFile.remove()
          return res
        })
        .catch((error) => {
          reject(error)
          InputFile.remove && InputFile.remove()
          return error
        })
    })

  })
}

export function isImage(fileUrl) {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp'];
  const splitItems = fileUrl.split('/').filter(splitItem => !!splitItem);
  const fileName = splitItems[splitItems.length - 1].toLowerCase();
  return imageExtensions.some(ext => fileName.endsWith(ext));
}

export function isAudio(fileUrl) {
  const audioExtensions = [
    '.mp3', '.wav', '.wma', '.aac', '.flac', '.ogg', '.m4a', '.aiff', '.aif',
    '.amr', '.opus', '.mid', '.midi', '.ac3', '.ape', '.dts', '.mp2', '.ra',
    '.rm', '.voc', '.au', '.wv', '.mpc'
  ];

  const splitItems = fileUrl.split('/').filter(splitItem => !!splitItem);
  const fileName = splitItems[splitItems.length - 1].toLowerCase();

  return audioExtensions.some(ext => fileName.endsWith(ext));
}