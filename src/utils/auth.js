import Cookies from 'js-cookie';

const TokenKey = 'User-Token';

const ExpiresInKey = 'User-Expires-In';

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  return Cookies.set(Token<PERSON><PERSON>, token);
}

export function setTokenExp(token, timestamp) {
  const expireDate = new Date(timestamp);
  return Cookies.set(Token<PERSON>ey, token, {
    expires: expireDate, // 指定具体过期时间点
  });
}

export function removeToken() {
  return Cookies.remove(TokenKey);
}

export function getExpiresIn() {
  return Cookies.get(ExpiresInKey) || -1;
}

export function setExpiresIn(timestamp) {
  const expireDate = new Date(timestamp); // 时间戳转 Date 对象
  return Cookies.set(TokenKey, 'value', {
    expires: expireDate, // 指定具体过期时间点
  });
}

export function removeExpiresIn() {
  return Cookies.remove(ExpiresInKey);
}
