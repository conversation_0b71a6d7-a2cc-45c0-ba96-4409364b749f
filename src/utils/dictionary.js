// 读者反馈/纠错:纠错类型(1：错别字，2：逻辑错误，3：内容错误，4：图片错误，5：其他)
const FAULT_TYPE = [
  { value: '1', label: '错别字' },
  { value: '2', label: '逻辑错误' },
  { value: '3', label: '内容错误' },
  { value: '4', label: '图片错误' },
  { value: '5', label: '其他' }
];

// 通过 value 查询 label
function getLabelByValue(dict, query) {
  if (Array.isArray(query)) {
    return query.map(q => {
      const item = dict.find(item => item.value === q);
      return item ? item.label : null;
    });
  } else {
    const item = dict.find(item => item.value === query);
    return item ? item.label : null;
  }
}

// 通过 label 查询 value
function getValueByLabel(dict, query) {
  if (Array.isArray(query)) {
    return query.map(q => {
      const item = dict.find(item => item.label === q);
      return item ? item.value : null;
    });
  } else {
    const item = dict.find(item => item.label === query);
    return item ? item.value : null;
  }
}

// 导出字典和辅助方法
export { FAULT_TYPE, getLabelByValue, getValueByLabel };