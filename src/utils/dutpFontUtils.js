/*const fontsToLoad = [
  { name: 'FZCHYK', url: 'FZCHYK.TTF' },
  { name: 'FZCYK', url: 'FZCYK.TTF' },
  { name: 'FZHPK', url: 'FZHPK.TTF' },
  { name: 'FZPWK', url: 'FZPWK.TTF' },
  { name: 'FZSEK', url: 'FZSEK.TTF' },
  { name: 'FZXSHK', url: 'FZXSHK.TTF' },
  { name: 'FZZBHK', url: 'FZZBHK.TTF' },
  { name: 'FZZYK', url: 'FZZYK.TTF' },
];*/
import { DUTP_FONT_URL } from '@/utils/constant.js';
// 写一个函数checkFontsArr , 查询 fontsToLoad 数组的每个对象的name属性和otherFontsNameArr 的交集和差集，以对象形式返回
export const prepareFontsArr = (otherFontsNameArr) => {
  // 遍历otherFontsNameArr，根据最后一个.截取前面的字符串作为name值，返回对象数组
  return otherFontsNameArr.map((fontName) => {
    const name = fontName.split('.')[0];
    return { name, url: `${DUTP_FONT_URL}${fontName}` };
  });
};
export const loadDutpFonts = async (otherFontsNameArr) => {
  const intersection = prepareFontsArr(otherFontsNameArr);
  if (intersection.length > 0) {
    const loadPromises = intersection.map(async (font) => {
      const fontFace = new FontFace(font.name, `url(${font.url})`);
      await fontFace.load();
      // 加载不到字体的异常提示
      if (!fontFace) {
        console.log(`字体不存在: ${font.name}`);
      }
      document.fonts.add(fontFace);
    });
    await Promise.all(loadPromises);
  }
};
