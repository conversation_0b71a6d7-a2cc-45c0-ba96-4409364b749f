import JSEncrypt from 'jsencrypt';
import axios from 'axios';
import request from '@/utils/request'
import useSiteStore from '@/store/modules/site'

/**
 * 获取后端提供的公钥
 * @returns {string} 后端公钥
 */
export async function getPublicKey() {
  try {
    const response = useSiteStore().publicKey;
    return response.data;  // 返回公钥字符串
  } catch (error) {
    console.error('获取公钥失败', error);
    return null;
  }
}

/**
 * 使用 RSA 公钥加密数据
 * @param {string} data 待加密数据
 * @param {string} publicKey RSA 公钥
 * @returns {string} 加密后的数据
 */
export function encryptDataWithRSA(data, publicKey) {
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(publicKey);  // 设置公钥
  const encrypted = encryptor.encrypt(data);  // 加密数据
  if (!encrypted) {
    console.error("RSA 加密失败");
    return null;
  }
  return encrypted;
}

/**
 * 解密
 * @returns 
 */
export function decryptDataWithRSA(data, publicKey) {
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(publicKey);  // 设置公钥
  const decryptedData = encryptor.decrypt(data);  // 加密数据
  if (!decryptedData) {
    console.error("RSA 解密失败");
    return null;
  }
  return decryptedData;
}

/**
 * 使用 RSA 公钥加密数据
 * @param {Object} data 要加密的对象
 * @param {string} publicKey 后端提供的 RSA 公钥
 * @returns {Object} 返回加密后的数据
 */
export async function encryptData(data, publicKey) {
  if (!data || !publicKey) {
    throw new Error('Missing required parameters');
  }
  // 将待加密的对象转换为字符串
  let dataStr = data;
  if (typeof(data) != 'string') {
    dataStr = JSON.stringify(data)
  }
  // 使用 RSA 公钥加密数据
  const encryptedData = encryptDataWithRSA(dataStr, publicKey);

  if (encryptedData) {
    return {
      encryptedData,  // 返回加密后的数据
    };
  } else {
    console.error('加密过程失败');
    return null;
  }
}

// 解密方法
export function decryptData(data) {
  return request({
    url: '/auth/decrypt',
    method: 'post',
    data: data
  })
}