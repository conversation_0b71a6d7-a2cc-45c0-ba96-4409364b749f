import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'
import useSiteStore from '@/store/modules/site'
// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey = ''

const privateKey = ''

// 加密
export function encrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对数据进行加密
}

// 解密
export function decrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(privateKey) // 设置私钥
  return encryptor.decrypt(txt) // 对数据进行解密
}

export function encryptWithKey(txt, prv = useSiteStore().publicKey) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(prv) // 设置公钥
  return encryptor.encrypt(txt) // 对数据进行加密
}

// 解密
export function decryptWithKey(txt, pub) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(pub) // 设置私钥
  return encryptor.decrypt(txt) // 对数据进行解密
}