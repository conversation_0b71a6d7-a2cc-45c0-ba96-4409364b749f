export const fontFamilyList = [
  {
    value: "默认",
    label: "默认",
  },
  {
    value: "宋体",
    label: "宋体",
  },
  {
    value: "楷体",
    label: "楷体",
  },
  {
    value: "微软雅黑",
    label: "微软雅黑",
  },
  {
    value: "黑体",
    label: "黑体",
  },
  {
    value: "隶书",
    label: "隶书",
  },
  {
    value: "幼圆",
    label: "幼圆",
  },
  {
    value: "华文彩云",
    label: "华文彩云",
  },
  {
    value: "华文行楷",
    label: "华文行楷",
  },
  {
    value: "华文新魏",
    label: "华文新魏",
  },
  {
    value: "方正舒体",
    label: "方正舒体",
  },
  {
    value: "方正姚体",
    label: "方正姚体",
  },
  {
    value: "Arial",
    label: "Arial",
  },
  {
    value: "Times New Roman",
    label: "Times New Roman",
  },
  {
    value: "Courier New",
    label: "Courier New",
  },
  {
    value: "Verdana",
    label: "<PERSON><PERSON><PERSON>",
  },
  {
    value: "Georgia",
    label: "Georgia",
  },
];

export const fontSizeList = [
  {
    value: "10px",
    label: "10px",
  },
  {
    value: "11px",
    label: "11px",
  },
  {
    value: "12px",
    label: "12px",
  },
  {
    value: "13px",
    label: "13px",
  },
  {
    value: "14px",
    label: "14px",
  },

  {
    value: "16px",
    label: "16px",
  },

  {
    value: "18px",
    label: "18px",
  },

  {
    value: "20px",
    label: "20px",
  },
  {
    value: "24px",
    label: "24px",
  },
  {
    value: "36px",
    label: "36px",
  },
];

export const colorList = [
  "ffffff",
  "000000",
  "eeece1",
  "1f497d",
  "4f81bd",
  "c0504d",
  "9bbb59",
  "8064a2",
  "4bacc6",
  "f79646",
  "f2f2f2",
  "7f7f7f",
  "ddd9c3",
  "c6d9f0",
  "dbe5f1",
  "f2dcdb",
  "ebf1dd",
  "e5e0ec",
  "dbeef3",
  "fdeada",
  "d8d8d8",
  "595959",
  "c4bd97",
  "8db3e2",
  "b8cce4",
  "e5b9b7",
  "d7e3bc",
  "ccc1d9",
  "b7dde8",
  "fbd5b5",
  "bfbfbf",
  "3f3f3f",
  "938953",
  "548dd4",
  "95b3d7",
  "d99694",
  "c3d69b",
  "b2a2c7",
  "92cddc",
  "fac08f",
  "a5a5a5",
  "262626",
  "494429",
  "17365d",
  "366092",
  "953734",
  "76923c",
  "5f497a",
  "31859b",
  "e36c09",
  "7f7f7f",
  "0c0c0c",
  "1d1b10",
  "0f243e",
  "244061",
  "632423",
  "4f6128",
  "3f3151",
  "205867",
  "974806",
  "c00000",
  "ff0000",
  "ffc000",
  "ffff00",
  "92d050",
  "00b050",
  "00b0f0",
  "0070c0",
  "002060",
  "7030a0",
];

export const fontList = [
  {
    value: "p",
    label: "正文",
  },
  {
    value: "h1",
    label: "一级标题",
  },
  {
    value: "h2",
    label: "二级标题",
  },
  {
    value: "h2",
    label: "三级标题",
  },
  {
    value: "h3",
    label: "四级标题",
  },
];

export const orderedList = [
  {
    value: "decimal",
    label: "1,2,3...",
  },
  {
    value: "lower-alpha",
    label: "a,b,c...",
  },
  {
    value: "lower-roman",
    label: "i,ii,iii...",
  },
  {
    value: "upper-alpha",
    label: "A,B,C...",
  },
  {
    value: "upper-roman",
    label: "I,II,III...",
  },
];

export const unOrderedList = [
  {
    value: "disc",
    label: "●",
  },
  {
    value: "circle",
    label: "○",
  },
  {
    value: "square",
    label: "■",
  },
];

export const lineHeightList = [
  {
    value: 1,
    label: "1",
  },
  {
    value: 1.5,
    label: "1.5",
  },
  {
    value: 1.75,
    label: "1.75",
  },
  {
    value: 2,
    label: "2",
  },
  {
    value: 3,
    label: "3",
  },
  {
    value: 4,
    label: "4",
  },
  {
    value: 5,
    label: "5",
  },
];

export const linkTypeList = [
  {
    value: 1,
    label: "本书章节",
  },
  {
    value: 2,
    label: "网页链接",
  },
  {
    value: 3,
    label: "虚拟仿真",
  },
];

export const rangeDirectionList = [
  {
    value: 1,
    label: "横屏和竖屏",
  },
  {
    value: 2,
    label: "仅横屏",
  },
  {
    value: 3,
    label: "仅竖屏",
  },
];

export const catalogueList = [
  {
    id: 1,
    label: "版权信息",
    children: [
      {
        id: 11,
        label: "我是第一节",
        children: [
          {
            id: 111,
            label: "第一节内容",
          },
        ],
      },
    ],
  },
  {
    id: 2,
    label: "我是第二节标题",
    children: [
      {
        id: 22,
        label: "我是内容1",
        children: [
          {
            id: 222,
            label: "正文",
          },
        ],
      },
      {
        id: 33,
        label: "我是内容2",
        children: [
          {
            id: 333,
            label: "正文",
          },
        ],
      },
      {
        id: 44,
        label: "我是内容3",
        children: [
          {
            id: 444,
            label: "正文",
          },
        ],
      },
      {
        id: 55,
        label: "我是内容4",
        children: [
          {
            id: 555,
            label: "正文",
          },
        ],
      },
    ],
  },
  {
    id: 6,
    label: "我是第三节标题",
    children: [
      {
        id: 61,
        label: "我是内容1",
        children: [
          {
            id: 611,
            label: "正文",
          },
        ],
      },
      {
        id: 62,
        label: "我是内容2",
        children: [
          {
            id: 622,
            label: "正文",
          },
        ],
      },
      {
        id: 63,
        label: "我是内容3",
        children: [
          {
            id: 633,
            label: "正文",
          },
        ],
      },
      {
        id: 64,
        label: "我是内容4",
        children: [
          {
            id: 644,
            label: "正文",
          },
        ],
      },
    ],
  },
];

export const userList = [
  {
    "value": "1",
    "label": "张三 1223333"
  },
  {
    "value": "2",
    "label": "李四 1223333"
  }
];

export const tableStyleList = [
  {
    id: 1,
    img: "https://dutp-test.oss-cn-beijing.aliyuncs.com/gridtable.png",
    label: "表格1",
    isUse: false,
    className: "table1",
  },
  {
    id: 2,
    img: "https://dutp-test.oss-cn-beijing.aliyuncs.com/blueTable.png",
    label: "表格2",
    isUse: false,
    className: "blueTable",
  },
  {
    id: 3,
    img: "https://dlchhc.oss-cn-beijing.aliyuncs.com/images/1679879873320.png",
    label: "表格1",
    isUse: false,
    className: "table1",
  },
  {
    id: 4,
    img: "https://dlchhc.oss-cn-beijing.aliyuncs.com/images/1679879873320.png",
    label: "表格1",
    isUse: false,
    className: "table1",
  },
  {
    id: 5,
    img: "https://dlchhc.oss-cn-beijing.aliyuncs.com/images/1679879873320.png",
    label: "表格1",
    isUse: false,
    className: "table1",
  },
  {
    id: 6,
    img: "https://dlchhc.oss-cn-beijing.aliyuncs.com/images/1679879873320.png",
    label: "表格1",
    isUse: false,
    className: "table1",
  },
];

export const useTableList = [
  {
    id: 1,
    label: "表格1",
    insertTable:
      '<table class="gridtable"><tr><th>A</th><th>B</th><th>C</th><th>D</th></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr></table>',
    className: "gridtable",
  },
  {
    id: 2,
    label: "表格2",
    insertTable:
      '<table class="blueTable"><tr><th>A</th><th>B</th><th>C</th><th>D</th></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr></table>',
    className: "blueTable",
  },
];

export const tempStyleList = [
  {
    id: 1,
    img: "https://dutp-test.oss-cn-beijing.aliyuncs.com/gridtable.png",
    label: "模板1",
    isUse: false,
    className: "table1",
  },
  {
    id: 2,
    img: "https://dutp-test.oss-cn-beijing.aliyuncs.com/blueTable.png",
    label: "模板2",
    isUse: false,
    className: "blueTable",
  },
  {
    id: 3,
    img: "https://dlchhc.oss-cn-beijing.aliyuncs.com/images/1679879873320.png",
    label: "模板3",
    isUse: false,
    className: "table1",
  },
]

export const useTempList = [
  {
    id: 1,
    label: "表格1",
    insertTable:
        '<table class="gridtable"><tr><th>A</th><th>B</th><th>C</th><th>D</th></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr></table>',
    className: "gridtable",
  },
  {
    id: 2,
    label: "表格2",
    insertTable:
        '<table class="blueTable"><tr><th>A</th><th>B</th><th>C</th><th>D</th></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr><tr><td>-</td><td>-</td><td>-</td><td>-</td></tr></table>',
    className: "blueTable",
  },
];

export const fontOutlineList = [
  {
    value: 1,
    label: "无",
  },
  {
    value: 2,
    label: "轮廓",
  },
  {
    value: 3,
    label: "阴影",
  },
  {
    value: 4,
    label: "映像",
  },
  {
    value: 5,
    label: "光",
  },
]


export const imgList = [
  
  {
    id: 1,
    type: "file",
    img: "",
    label: "第一章文件夹",
  },
  {
    id: 2,
    type: "file",
    img: "",
    label: "第二章文件夹",
  },
  {
    id: 3,
    type: "file",
    img: "",
    label: "第三章文件夹",
  },
  {
    id: 4,
    type: "file",
    img: "",
    label: "第四章文件夹",
  },
  {
    id: 5,
    type: "img",
    img: "https://dlchhc.oss-cn-beijing.aliyuncs.com/images/1679879873320.png",
    label: "图片1",
  },
  {
    id: 6,
    type: "img",
    img: "https://dlchhc.oss-cn-beijing.aliyuncs.com/images/1679879873320.png",
    label: "图片2",
  },
  {
    id: 7,
    type: "img",
    img: "https://dlchhc.oss-cn-beijing.aliyuncs.com/images/1679879873320.png",
    label: "图片3",
  },
  {
    id: 8,
    type: "img",
    img: "https://dlchhc.oss-cn-beijing.aliyuncs.com/images/1679879873320.png",
    label: "图片4",
  },

]

export const imagesIdList = [
  {
    id:1,
    label:'第一章文件夹',
    children:[
      {
        id: 11,
        type: "file",
        img: "",
        label: "第二章文件夹",
      },
      {
        id: 12,
        type: "file",
        img: "",
        label: "第二章文件夹",
      },
      {
        id: 13,
        type: "file",
        img: "",
        label: "第二章文件夹",
      },
      {
        id: 14,
        type: "file",
        img: "",
        label: "第二章文件夹",
      },
      {
        id: 15,
        type: "img",
        img: "https://dlchhc.oss-cn-beijing.aliyuncs.com/images/1679879873320.png",
        label: "图片1",
      },
      {
        id: 16,
        type: "img",
        img: "https://dlchhc.oss-cn-beijing.aliyuncs.com/images/1679879873320.png",
        label: "图片2",
      },
      {
        id: 17,
        type: "img",
        img: "https://dlchhc.oss-cn-beijing.aliyuncs.com/images/1679879873320.png",
        label: "图片3",
      },
      {
        id: 18,
        type: "img",
        img: "https://dlchhc.oss-cn-beijing.aliyuncs.com/images/1679879873320.png",
        label: "图片4",
      },
    ]
  }
]

export const  bannerStatusOptions =
    [{
      value: '1',
      label: '未开始'
    }, {
      value: '2',
      label: '已发布'
    }, {
      value: '4',
      label: '已撤销'
    }, {
      value: '3',
      label: '已下线'
    }]

export const bannerPositionOptions = [{
  value: 1,
  label: '首页'
},{
  value: 2,
  label: '登录页'
}]
export const  bannerTypeOptions= [{
  value: 1,
  label: '内部跳转'
},{
  value: 2,
  label: '外部跳转'
}]

export function getOptionDesc(option_list, value) {
  if (!option_list) {
    return "";
  }
  const selectOptionList = option_list.filter((item) => item.value == value);
  if (selectOptionList.length == 0) {
    return "";
  }
  return selectOptionList[0].desc;
}

export const emojis = [
  { value: 1, desc: "😀 😃 😄 😁 😆 😅 🤣 😂 🙂 🙃 😉 😊 😇 🥰 😍 🤩 😘 😗 😚 😙 😋 😛 😜 🤪 😝 🤑 🤗 🤭 🤫 🤔 🤐 🤨 😐 😑 😶 😏 😒 🙄 😬 🤥 😌 😔 😪 🤤 😴 😷 🤒 🤕 🤢 🤮 🤧 🥵 🥶 🥴 😵 🤯 🤠 🥳 😎 🤓 🧐 😕 😟 🙁 ☹️ 😮 😯 😲 😳 🥺 😦 😧 😨 😰 😥 😢 😭 😱 😖 😣 😞 😓 😩 😫 🥱 😤 😡 😠 🤬 😈 👿 💀 ☠️ 💩 🤡 👹 👺 👻 👽 👾 🤖 👋 🤚 🖐️ ✋ 🖖 👌 🤏 ✌️ 🤞 🤟 🤘 🤙 👈 👉 👆 🖕 👇 ☝️ 👍 👎 ✊ 👊 🤛 🤜 👏 🙌 👐 🤲 🤝 🙏 ✍️ 💅 🤳 💪 🦾 🦿 🦵 🦶 👂 🦻" },
];
