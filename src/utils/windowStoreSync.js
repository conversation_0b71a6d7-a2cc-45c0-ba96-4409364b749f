import { ElMessage } from 'element-plus'

/**
 * 在父窗口中监听子窗口的跳转请求
 * @param {Object} store - Vuex store 实例
 * @returns {Function} 清理函数
 */
export function listenForJumpRequests(store) {
  const handleMessage = async (event) => {
    // 验证消息来源
    if (event.origin !== window.location.origin) {
      return
    }
    if (event.data && event.data.type === 'JUMP_TO_SECTION') {
      const headingItem = event.data.data
      try {
        // 直接执行跳转
        if (headingItem.domId) {
          await store.jumpToPageBasedOnNodeId(headingItem.domId, headingItem.chapterId)

          // 等待页面渲染后滚动到目标元素
          setTimeout(() => {
            let targetElement = document.querySelector(
                `:is(h1, h2, h3, h4, h5, h6)[data-toc-id="${headingItem.domId}"]`
            );
            if (targetElement) {
              targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
              // 添加高亮效果
              targetElement.style.backgroundColor = '#fff3cd'
              setTimeout(() => {
                targetElement.style.backgroundColor = ''
              }, 2000)
            }
          }, 1000)

        } else {
          await store.jumpToChapter(headingItem.chapterId)
        }
      } catch (error) {

        // 如果跳转失败，尝试简单的章节跳转
        if (headingItem.chapterId) {
          try {
            await store.jumpToChapter(headingItem.chapterId)
          } catch (fallbackError) {
          }
        } else {
        }
      }
    }
  }
  window.addEventListener('message', handleMessage)
  // 返回清理函数
  return () => {
    window.removeEventListener('message', handleMessage)
  }
}
