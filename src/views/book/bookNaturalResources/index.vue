<template>
  <div class="comp-container" v-if="currentBook">
    <div class="search-con">
      <searchBar @search="doToSearchPage" :isNoShadow="true" />
    </div>
    <div class="type-level-con">
      <typeLevelCrumbs :item="currentBook"/>
    </div>
    <div class="book-info-con">
      <div class="info-left">
        <div class="book-img-con">
          <img
            class="book-img"
            :src="currentBook.cover ? currentBook.cover : bookCoverDefault"
            alt=""
          />
        </div>
        <div class="book-info">
          <div class="book-name">{{ currentBook.bookName }}</div>
          <div class="book-author">
            {{ currentBook.authorLabel }}：{{ currentBook.authorValue }}
          </div>
          <div class="update-time">出版日期：{{ currentBook.publishDate }}</div>
        </div>
      </div>
      <div class="info-right">
        <!-- <div class="down-load-num">下载次数：{{ currentBook[0].downloadCount }}次</div> -->
        <div
          class="bath-down-btn"
          v-if="downloadData.length !== 0 && isDoNaturalResources"
          @click="doNaturalResources"
        >
          批量下载
        </div>
      </div>
    </div>
    <div class="resources-list-con">
      <div class="resources-list">
        <div class="resources-item" v-for="item in downloadData" :key="item">
          <div class="item-left">
            <div class="img-con">
              <i
                class="icon"
                :class="selectTypeName(item.fileName.split('.').pop())"
              ></i>
            </div>
            <div class="resources-name">{{ item.fileName }}</div>
          </div>
          <div class="item-right">
            <!-- <div class="time-con">时长：1小时</div> -->
            <div class="icon-con">
              <el-icon
                class="icon"
                @click="downloadFile(item)"
                v-if="item.download === 1"
                ><Download
              /></el-icon>
              <el-icon class="icon" @click="previewFile(item)" v-else
                ><ZoomIn
              /></el-icon>
            </div>
          </div>
        </div>
      </div>
      <div v-if="downloadData.length === 0" class="no-data">暂无资源</div>
    </div>
  </div>
</template>

<script setup name="bookNaturalResources">
import { onMounted } from "vue";
import searchBar from "@/views/home/<USER>/SearchBar/index.vue";
import typeLevelCrumbs from '@/views/book/components/typeLevelCrumbs/index.vue';
import downloadPlugin from "@/plugins/download.js";
import { getInfoByBookId } from "@/api/shop/book";
import { listBookFile } from "@/api/book/bookFile";
import bookCoverDefault from "@/assets/images/book-cover-default.png";
// import { decryptData } from '@/utils/encrypt.js';
import { Base64 } from "js-base64";
const currentBook = ref({});
const router = useRouter();
// 获取当前路由对象
const route = useRoute();
const bookId = route.query.bookId;
const isPay = Number(route.query.isPay);
const bookIdDecrypt = ref();
const downloadData = ref([]);
const isDoNaturalResources = ref(false);
const doToSearchPage = (queryCriteria) => {
  // 查询
  router.push({
    path: "/search",
    query: {
      ...queryCriteria,
    },
  });
};
const selectTypeName = computed(() => {
  return function (extension) {
    if (!extension) return null;

    if (extension === "doc" || extension === "docx") {
      return "doc";
    }

    if (extension === "pdf") {
      return "pdf";
    }

    if (extension === "ppt" || extension === "pptx") {
      return "ppt";
    }

    if (extension === "xls" || extension === "xlsx") {
      return "xls";
    }

    if (extension === "txt") {
      return "txt";
    }

    if (extension === "zip") {
      return "zip";
    }

    if (extension === "rar") {
      return "rar";
    }

    if (extension === "mp3") {
      return "mp3";
    }

    if (extension === "mp4" || extension === "avi" || extension === "mov") {
      return "avi";
    }

    if (
      extension === "jpg" ||
      extension === "jpeg" ||
      extension === "png" ||
      extension === "gif"
    ) {
      return "jpg";
    }
  };
});
const doNaturalResources = async () => {
  const params = {
    bookId: bookIdDecrypt.value,
    download: 1,
  };
  await listBookFile(params).then((res) => {
    downloadPlugin.zipDownload(res.rows, "资源包.zip");
  });
};
const downloadFile = (item) => {
  downloadPlugin.zip(item.fileUrl, item.fileName);
};
// 预览文件
const previewFile = (item) => {
  // 实现预览文件逻辑
  const url = item.fileUrl;
  const encodedUrl = Base64.encode(url);
  const previewUrl =
    import.meta.env.VITE_ONLINE_PREVIEW + encodeURIComponent(encodedUrl);
  window.open(previewUrl);
};
const fetchData = () => {
  getInfoByBookId(bookIdDecrypt.value).then((res) => {
    if (res.code === 200) {
      currentBook.value = res.data;
      const params = {
        bookId: bookIdDecrypt.value,
        pageNum: 1,
        pageSize: 99999,
      };
      listBookFile(params).then((res) => {
        if (isPay === 0) {
          res.rows.forEach((item) => {
            if (item.download === 1) {
              isDoNaturalResources.value = true;
            }
          });
          downloadData.value = res.rows;
        } else {
          res.rows.forEach((item) => {
            item.download = 2;
          });
          downloadData.value = res.rows;
        }
      });
    }
  });
};
onMounted(async () => {
  // const res = await decryptData({ encryptedData: bookId });
  bookIdDecrypt.value = bookId;
  await fetchData();
});
</script>

<style lang="scss" scoped>
@import "@/assets/styles/index";
.no-data {
  width: 100%;
  text-align: center;
  font-size: 16px;
  color: #999;
  padding: 20px 0;
  border-radius: 4px;
  margin-top: 20px;
}
.comp-container {
  width: 100%;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
  .search-con {
    width: 100%;
    margin-bottom: 30px;
  }
  .type-level-con {
    @extend .base-comp-card;
    height: 28px;
    text-align: left;
    font-family:
        PingFangSC,
        PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #666666;
    line-height: 28px;
    margin-bottom: 31px;
  }
  .book-info-con {
    margin-bottom: 30px;
    @extend .base-comp-card;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: stretch;
    .info-left {
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: stretch;
      .book-img-con {
        width: 140px;
        height: 200px;
        margin-right: 20px;
        .book-img {
          width: 100%;
          height: 100%;
          border-radius: 4px;
          box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.1);
        }
      }
      .book-info {
        padding-top: 20px;
        @extend .base-flex-column;
        justify-content: flex-start;
        align-items: flex-start;
        .book-name {
          font-size: 20px;
          font-weight: bold;
          color: #333333;
          margin-bottom: 20px;
        }
        .book-author {
          font-size: 14px;
          font-weight: bold;
          color: #333333;
          margin-bottom: 10px;
        }
        .update-time {
          font-size: 14px;
          font-weight: bold;
          color: #333333;
        }
      }
    }
    .info-right {
      @extend .base-flex-column;
      justify-content: flex-end;
      align-items: center;
      .down-load-num {
        color: rgba(121, 135, 150, 1);
        font-size: 16px;
        font-family: SourceHanSansSC;
        line-height: 23px;
        margin-bottom: 15px;
      }
      .bath-down-btn {
        width: 150px;
        height: 40px;
        border: 1px solid #2d8cf0;
        border-radius: 8px;
        text-align: center;
        line-height: 40px;
        color: #2d8cf0;
        cursor: pointer;
      }
      .bath-down-btn:hover {
        opacity: 0.8;
      }
    }
  }
  .resources-list-con {
    padding-bottom: 200px;
    @extend .base-comp-card;
    .resources-list {
      width: 100%;
      @extend .base-flex-column;
      justify-content: flex-start;
      align-items: center;
      .resources-item {
        width: 100%;
        padding-top: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9e9e9ff;
        @extend .base-flex-row;
        justify-content: space-between;
        align-items: center;
        .item-left {
          @extend .base-flex-row;
          justify-content: flex-start;
          align-items: center;
          .img-con {
            width: 30px;
            height: 30px;
            margin-right: 10px;
            .icon {
              width: 30px;
              height: 30px;
              display: inline-flex;
              background-size: contain;
            }

            .mp3 {
              background: url("../../../assets/svg/muics.svg") no-repeat;
              background-size: contain;
            }

            .doc {
              background: url("../../../assets/svg/word.svg") no-repeat;
              background-size: contain;
            }

            .pdf {
              background: url("../../../assets/svg/pdf.svg") no-repeat;
              background-size: contain;
            }

            .ppt {
              background: url("../../../assets/svg/ppt.svg") no-repeat;
              background-size: contain;
            }

            .xls {
              background: url("../../../assets/svg/xls.svg") no-repeat;
              background-size: contain;
            }

            .txt {
              background: url("../../../assets/svg/txt.svg") no-repeat;
              background-size: contain;
            }

            .avi {
              background: url("../../../assets/svg/video.svg") no-repeat;
              background-size: contain;
            }

            .rar {
              background: url("../../../assets/svg/rar.svg") no-repeat;
              background-size: contain;
            }

            .jpg {
              background: url("../../../assets/svg/images.svg") no-repeat;
              background-size: contain;
            }
          }
          .resources-name {
            font-size: 16px;
            font-weight: bold;
            color: #333333;
          }
        }
        .item-right {
          padding-top: 10px;
          @extend .base-flex-row;
          justify-content: flex-end;
          align-items: center;
          .time-con {
            margin-right: 20px;
            font-size: 16px;
            color: #9a9a9a;
          }
          .icon-con {
            min-width: 24px;
            cursor: pointer;
            .icon {
              font-size: 24px;
            }
          }
          .icon-con:hover {
            opacity: 0.8;
          }
        }
      }
    }
  }
}
</style>
