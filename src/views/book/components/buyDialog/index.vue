<template>
  <!--  弹窗    -->
  <el-dialog v-model="showBuyDialogFlagData" style="border-radius: 8px" width="25%" :show-close="false" :modal="false" align-center>
    <div class="buy-comp-con">
      <div class="info-con">
        <div class="title">购买教材</div>
        <div class="name">
          <div class="label">教材</div>
          <div class="info">数学数学数学</div>
        </div>
        <div class="price">
          <div class="label">价格</div>
          <div class="ori-price">￥47.00</div>
          <div class="sell-price">￥39.00</div>
        </div>
      </div>
      <div class="btn-con">
        <div class="cancel-btn" @click="cancelBuy">取消</div>
        <div class="buy-btn" @click="doBuy">确认购买</div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup name="BuyDialogComp">
const props = defineProps({
  showBuyDialogFlag: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
    default: {},
  },
});
const showBuyDialogFlagData = computed({
  get: () => props.showBuyDialogFlag,
  set: (val) => {
    emit('update:showBuyDialogFlag', val);
  },
});
const emit = defineEmits(['update:showBuyDialogFlag', 'cancelBuy', 'doBuy']);
const cancelBuy = () => {
  emit('cancelBuy', false);
};
const doBuy = () => {
  // 处理购买逻辑
  emit('doBuy', false);
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.buy-comp-con {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
  .info-con {
    width: 85%;
    @extend .base-flex-column;
    justify-content: flex-start;
    align-items: flex-start;
    .title {
      width: 100%;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      text-align: left;
      margin-bottom: 20px;
    }
    .name {
      width: 100%;
      margin-bottom: 20px;
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: center;
      .label {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        text-align: left;
        margin-right: 10px;
      }
      .info {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 300;
        font-size: 16px;
        color: #7a8796ff;
        text-align: left;
      }
    }
    .price {
      margin-bottom: 40px;
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: center;
      .label {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        text-align: left;
        margin-right: 10px;
      }
      .ori-price {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 300;
        font-size: 16px;
        color: #7a8796ff;
        text-align: left;
        margin-right: 10px;
        text-decoration: line-through;
      }
      .sell-price {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 300;
        font-size: 16px;
        color: #7a8796ff;
        text-align: left;
      }
    }
  }
  .btn-con {
    width: 90%;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: center;
    .cancel-btn {
      width: 115px;
      height: 42px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 16px;
      border-radius: 8px;
      text-align: center;
      line-height: 42px;
      background-color: #eaeeefff;
      color: #3665a3;
      cursor: pointer;
    }
    .cancel-btn:hover {
      opacity: 0.8;
    }
    .buy-btn {
      width: 280px;
      height: 42px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 16px;
      border-radius: 8px;
      background-color: #2354a1ff;
      color: #ffffff;
      text-align: center;
      line-height: 42px;
      cursor: pointer;
    }
    .buy-btn:hover {
      opacity: 0.8;
    }
  }
}
</style>
