<template>
  <div class="catalog-head-cell">
    <el-icon class="item-lock-icon"><Lock v-if="item.locked" /></el-icon>
    <div class="label">封面{{ item.label }}</div>
  </div>
</template>

<script setup name="CatalogHeadCell">
const props = defineProps({
  item: Object,
  default: {},
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';

.catalog-head-cell {
  width: 95%;
  height: 44px;
  @extend .base-flex-row;
  justify-content: flex-start;
  align-items: center;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 44px;
  text-align: left;
  .item-lock-icon {
    width: 24px;
    height: 26px;
    margin-right: 10px;
  }
}
</style>
