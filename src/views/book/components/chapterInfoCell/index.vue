<template>
  <div class="chapter-info-cell">
    <!-- 章 -->
    <div class="chapter-cell">
      <div class="cell-left" @click="gotoReaderChapter(item)">
        <el-icon class="item-lock-icon">
          <Lock v-if="item.free === 1" />
        </el-icon>
        <div class="label">{{ item.chapterName }}</div>
      </div>
      <div class="cell-right">
        <div class="right-icon-con">
          {{}}
          <el-icon
            class="right-icon"
            @click="doClickChapterInfoCell"
            v-if="currentChildrenCellShowFlag"
          >
            <ArrowUp />
          </el-icon>
          <el-icon class="right-icon" @click="doClickChapterInfoCell" v-else>
            <ArrowDown />
          </el-icon>
        </div>
      </div>
    </div>
    <!-- 节 -->
    <div
      class="chapter-info-children-cell-con"
      v-show="currentChildrenCellShowFlag"
    >
      <chapterInfoChildrenCell
        v-if="item.children"
        v-for="data in item.children"
        :item="data"
        @click="gotoReaderCell(item, data)"
      />
    </div>
  </div>
  <!--  弹窗    -->
  <Payment v-model="showBuyDialogFlag" :form="currentBook" />
</template>

<script setup name="ChapterInfoCell">
import chapterInfoChildrenCell from "@/views/book/components/chapterInfoChildrenCell/index.vue";
import { ElMessageBox } from "element-plus";
import Payment from "@/components/payment/index.vue";
import { getShopBook } from "@/api/shop/book";
import { getToken } from "@/utils/auth";
import { ElMessage } from "element-plus";

const showBuyDialogFlag = ref(false);
const router = useRouter();
const currentChildrenCellShowFlag = ref(false);
const currentBook = ref({});
function doClickChapterInfoCell() {
  currentChildrenCellShowFlag.value = !currentChildrenCellShowFlag.value;
}
function gotoReaderChapter(item) {
  if (item.free === 2) {
    router.push({
      path: "/reader",
      query: { k: item.bookId, cid: item.chapterId },
    });
    sessionStorage.setItem("chapterId", item.chapterId);
  } else {
    ElMessageBox.confirm("当前章节收费，无法阅读。是否确认购买", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        if (getToken()) {
          await getShopBook({ bookId: item.bookId }).then((res) => {
            if (res.code === 200) {
              currentBook.value = res.data;
              showBuyDialogFlag.value = true;
            }
          });
        } else {
          // 跳转登录页
          router.push({ path: "/login" });
        }
      })
      .catch(() => {
        // 用户点击取消时的操作
        ElMessage({
          type: "info",
          message: "已取消购买",
        });
      });
  }
}

function gotoReaderCell(item, children) {
  if (item.free === 2 && children.free === 2) {
    window.open(
      `/reader?k=${item.bookId}&cid=${item.chapterId}&cataid=${children.catalogId}`,
      "_blank"
    );
    sessionStorage.setItem("chapterId", item.chapterId);
  } else {
    ElMessageBox.confirm("当前章节收费，无法阅读。是否确认购买", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        if (getToken()) {
          await getShopBook({ bookId: item.bookId }).then((res) => {
            if (res.code === 200) {
              currentBook.value = res.data;
              showBuyDialogFlag.value = true;
            }
          });
        } else {
          // 跳转登录页
          proxy.$router.push({ path: "/login" });
        }
      })
      .catch(() => {
        // 用户点击取消时的操作
        ElMessage({
          type: "info",
          message: "已取消购买",
        });
      });
  }
}
const props = defineProps({
  item: {
    type: Object,
    default: { chapterName: "", children: [] },
  },
  expandAllFlag: {
    type: Boolean,
    default: false,
  },
});

// currentChildrenCellShowFlag.value = computed(() => props.expandAllFlag);
watch(
  () => props.expandAllFlag,
  (newValue, oldValue) => {
    currentChildrenCellShowFlag.value = newValue;
  }
);
</script>

<style lang="scss" scoped>
@import "@/assets/styles/index.scss";

.chapter-info-cell {
  width: 95%;
  min-height: 44px;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: flex-start;

  .chapter-cell {
    width: 100%;
    height: 100%;
    background: #f6f6f6;
    border-radius: 4px;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: center;

    &:hover {
      cursor: pointer;
    }

    .cell-left {
      width: 95%;
      height: 100%;
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: center;

      .item-lock-icon {
        width: 24px;
        height: 26px;
        margin-right: 10px;
      }

      .label {
        flex: 1;
        height: 100%;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 40px;
        text-align: left;
      }
    }

    .cell-right {
      @extend .base-flex-row;
      jsustify-content: flex-end;
      align-items: center;

      .right-icon-con {
        width: 26px;
        height: 26px;
        background: #d8d8d8;
        border-radius: 4px;
        @extend .base-flex-row;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .chapter-info-children-cell-con {
    width: 100%;
    padding-left: 30px;
    transition: all 0.5s ease-in-out;
    @extend .base-flex-column;
    justify-content: flex-start;
    align-items: flex-start;

    &:hover {
      cursor: pointer;
    }
  }
}
</style>
