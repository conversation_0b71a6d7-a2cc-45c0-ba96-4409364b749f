<template>
  <div class="chapter-info-children-cell">
    <div class="ch-cell-left">
      <el-icon class="item-lock-icon-ch"><Lock v-if="item.free === 1" /></el-icon>
      <div class="label-ch">{{ item.chapterName }}</div>
    </div>
    <div class="ch-cell-right">
      <div class="right-icon-con">
        <el-icon class="right-icon"><ArrowDown v-if="false" /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup name="ChapterInfoChildrenCell">
const props = defineProps({
  item: {
    name: '',
    children: [],
  },
});
const show = ref(false);
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';

.chapter-info-children-cell {
  width: 100%;
  min-height: 44px;
  @extend .base-flex-row;
  justify-content: space-between;
  align-items: center;
  .ch-cell-left {
    width: 95%;

    @extend .base-flex-row;
    justify-content: flex-start;
    align-items: center;
    .item-lock-icon-ch {
      width: 14px;
      height: 16px;
      margin-right: 10px;
    }
    .label-ch {
    }
  }
  .ch-cell-right {
    flex: 1;
  }
}
</style>
