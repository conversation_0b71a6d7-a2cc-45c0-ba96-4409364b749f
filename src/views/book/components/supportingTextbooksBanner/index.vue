<template>
  <!-- 配套教材轮播  -->
  <div class="book-list">
    <div v-if="showBtnFlag" class="list-mv-btn" @click="prevBookItem"><</div>
    <div class="book-item-con">
      <div
        ref="targetElement"
        class="book-item"
        :class="{ active: index === currentBookItemIndex % 10 && localIsShow }"
        v-for="(item, index) in visibleBooks"
        :key="index"
        @mouseenter="hoverBookItem(index + (currentBookItemIndex >= 10 ? 10 : 0))"
        @mouseleave="hoverBookItem(null)"
        @click="supportingTextbooks(item)"
      >
        <div class="book-img-con">
          <img
            class="book-img"
            :src="item.cover ? item.cover : bookCoverDefault"
            alt=""
            @click="updateCurrentBookItemIndex(index + (currentBookItemIndex >= 10 ? 10 : 0))"
            :class="{ hover: hoverBookIndex === index + (currentBookItemIndex >= 10 ? 10 : 0) }"
          />
        </div>
      </div>
    </div>
    <div v-if="showBtnFlag" class="list-mv-btn" @click="nextBookItem">></div>
  </div>
</template>

<script setup name="supportingTextbooksBanner">
import bookCoverDefault from '@/assets/images/book-cover-default.png';

const props = defineProps({
  deputyBookList: {
    type: Array,
    default: () => {
      return [];
    },
  },
  isShow: {
    type: Boolean,
    default: () => {
      return false;
    },
  },
});
const emit = defineEmits(['update']);
const visibleBooks = ref([]);
let currentBookcount = ref(props.deputyBookList.length);
let currentBookItemIndex = ref(0);
const hoverBookIndex = ref(null); // 鼠标悬停的教材索引
const showBtnFlag = computed(() => {
  return props.deputyBookList.length > 10;
});
const localIsShow = ref(false);
/** 获取配套教材的章节信息 **/
function supportingTextbooks(item) {
  localIsShow.value = true;
  emit('update', item);
}

// 更新当前配套教材索引
function updateCurrentBookItemIndex(index) {
  currentBookItemIndex.value = index;
}
function prevBookItem() {
  if (currentBookItemIndex.value > 0) {

    // 计算当前页码
    let parseIntValue = Math.ceil((currentBookItemIndex.value + 1) / 10);

    currentBookItemIndex.value--;

    // 计算新的页码
    let newPageValue = Math.ceil((currentBookItemIndex.value + 1) / 10);

    if (newPageValue !== parseIntValue) {
      visibleBooks.value = props.deputyBookList.slice(currentBookItemIndex.value - (currentBookItemIndex.value % 10), currentBookItemIndex.value + 1);
    }
  }
}
function nextBookItem() {
  currentBookItemIndex.value++;
  if (currentBookItemIndex.value % 10 === 0 && currentBookItemIndex.value < currentBookcount.value) {
    updateVisibleBooks();
  }
  if (currentBookcount.value <= currentBookItemIndex.value) {
    currentBookItemIndex.value = currentBookcount.value - 1;
  }
}
function updateVisibleBooks() {
  visibleBooks.value = props.deputyBookList.slice(currentBookItemIndex.value, currentBookItemIndex.value + 10);
}
function hoverBookItem(index) {
  hoverBookIndex.value = index;
}
function simulateMouseEnter() {
  nextTick(() => {
    const element = document.querySelector('.book-item.active');
    if (element) {
      const event = new MouseEvent('mouseenter', {
        view: window,
        bubbles: true,
        cancelable: true
      });
      element.dispatchEvent(event);
    }
  });
}
watch(() => props.isShow, (newVal) => {
  localIsShow.value = newVal
})
onMounted(() => {
  updateVisibleBooks();
  simulateMouseEnter();
  localIsShow.value = props.isShow;
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.book-list {
  width: 100%;
  min-height: 100px;
  @extend .base-flex-row;
  justify-content: space-between;
  align-items: center;
  .list-mv-btn {
    width: 31px;
    height: 100px;
    background: #f7f7f7;
    border-radius: 4px;
    border: 1px solid #e5e6e7;
    text-align: center;
    line-height: 85px;
    color: #666666;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
  }
  .list-mv-btn:last-child {
    margin: 0px;
  }
  .list-mv-btn:hover {
    opacity: 0.8;
    background: #f0f1f1;
  }
  .book-item-con {
    flex: 1;
    @extend .base-flex-row;
    justify-content: flex-start;
    align-items: center;
    .book-item {
      margin-right: 20px;
      align-self: flex-end;
      .book-img-con {
        cursor: pointer;
        .book-img {
          width: 72px;
          height: 100px;
          border-radius: 4px;
        }
      }
      .book-img-con:hover {
        opacity: 0.8;
      }
    }
  }
}
.book-item {
  display: inline-block;
  margin: 5px;
  transition:
    transform 0.3s,
    box-shadow 0.3s;
}
.book-item.active .book-img {
  border: 3px solid #007bff; /* 高亮的边框颜色 */
  transform: scale(1.1); /* 高亮时稍微放大 */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}
.book-img.hover {
  transform: scale(1.2); /* 鼠标悬停时放大 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}
</style>
