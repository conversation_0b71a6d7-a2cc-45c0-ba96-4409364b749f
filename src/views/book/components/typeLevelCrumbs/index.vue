<template>
  <div class="level-con">
    <span>{{ topSubjectName }}</span>
    <span v-if="secondSubjectName">-{{ secondSubjectName }}</span>
    <span v-if="thirdSubjectName">-{{ thirdSubjectName }}</span>
    <span v-if="forthSubjectName">-{{ forthSubjectName }}</span>
  </div>
</template>

<script setup name="TypeLevelCrumbs">
import { ref, watch } from 'vue';
import useSiteStore from "@/store/modules/site.js";
const subjectList = computed(() => useSiteStore().subjectList);

const props = defineProps({
  item: Object,
  default: {},
});

const topSubjectName = ref('');
const secondSubjectName = ref('');
const thirdSubjectName = ref('');
const forthSubjectName = ref('');

watch(
  () => props.item,
  (newValue) => {
    if (!newValue) {
      return;
    }
    prepareTo2ForSbujectName(newValue);
  }
);

function getSubjectNameById(subjectId) {
  // 返回 subjectList.value
  if (subjectList.value && subjectList.value.length > 0) {
    // 返回 subjectList.value 数组里对象 item.subjectId = subjectId 的对象的 subjectName
    return subjectList.value.find((item) => item.subjectId === subjectId)?.subjectName;
  }
}
function prepareTo2ForSbujectName(bookItem) {
  if (bookItem.topSubjectId) {
    topSubjectName.value = getSubjectNameById(bookItem.topSubjectId) || '';
  }
  if (bookItem.secondSubjectId) {
    secondSubjectName.value = getSubjectNameById(bookItem.secondSubjectId);
  }
  if (bookItem.thirdSubjectId) {
    thirdSubjectName.value = getSubjectNameById(bookItem.thirdSubjectId);
  }
  if (bookItem.forthSubjectId) {
    forthSubjectName.value = getSubjectNameById(bookItem.forthSubjectId);
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.level-con {
  width: 100%;
  height: 28px;
  text-align: left;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #666666;
  line-height: 28px;
  margin-bottom: 31px;
}
</style>
