<template>
  <div class="token-expired-container">
    <div class="error-content">
      <div class="warning-icon">⚠️</div>
      <h2 class="error-title">链接已过期或无效</h2>
      <p class="error-message">请关闭此页面并重新获取有效链接</p>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

// 页面加载时禁用一些功能
onMounted(() => {
  // 禁用右键菜单
  document.addEventListener('contextmenu', (e) => e.preventDefault())
  
  // 禁用打印快捷键
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.key === 'p') {
      e.preventDefault()
    }
  })
})
</script>

<style scoped>
.token-expired-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  margin: 0;
  padding: 20px;
  box-sizing: border-box;
}

.error-content {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.warning-icon {
  font-size: 64px;
  margin-bottom: 20px;
  display: block;
}

.error-title {
  color: #303133;
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 600;
}

.error-message {
  color: #606266;
  margin: 0 0 32px 0;
  font-size: 16px;
  line-height: 1.5;
}

.button-group {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.close-btn, .reload-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.close-btn {
  background-color: #409eff;
  color: white;
}

.close-btn:hover {
  background-color: #66b1ff;
}

.reload-btn {
  background-color: #67c23a;
  color: white;
}

.reload-btn:hover {
  background-color: #85ce61;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .error-content {
    padding: 30px 20px;
  }
  
  .error-title {
    font-size: 20px;
  }
  
  .error-message {
    font-size: 14px;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .close-btn, .reload-btn {
    width: 100%;
  }
}
</style>
