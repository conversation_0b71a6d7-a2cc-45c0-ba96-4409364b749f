<template>
  <div class="comp-con">
    <div class="comp-title-con">
      <img class="title-arrow-img" src="@/assets/images/home/<USER>" alt="" />
      <span class="title-text">{{ title }}</span>
      <img class="title-arrow-img" src="@/assets/images/home/<USER>" alt="" />
    </div>
    <!-- 01 下载图标 -->
    <div class="data-list-con">
      <div class="data-list">
        <div class="item-card">
          <div class="list-item">
            <div class="common-item">
              <div class="item-top">
                <div class="desc">已支持Apple全系列芯片</div>
                <img class="code-edge-img" src="@/assets/images/home/<USER>" alt="" />
              </div>
              <div class="icon-con">
                <img class="icon-img" :src="siteInfo.iosCover" alt="" />
              </div>
            </div>
            <div class="hover-item-con">
              <div class="hover-item">
                <div class="or-img-con">
                  <img class="or-img" :src="siteInfo.iosAppQrcode" alt="" />
                </div>
                <div class="download-notice">扫码下载</div>
                <div class="version-notice">版本号：{{ siteInfo.iosVersion }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="item-card">
          <div class="list-item">
            <div class="common-item">
              <div class="item-top">
                <img class="code-edge-img" src="@/assets/images/home/<USER>" alt="" />
              </div>
              <div class="icon-con">
                <img class="icon-img" :src="siteInfo.androidCover" alt="" />
              </div>
            </div>
            <div class="hover-item-con">
              <div class="hover-item">
                <div class="or-img-con">
                  <img class="or-img" :src="siteInfo.androidAppQrcode" alt="" />
                </div>
                <div class="download-notice">扫码下载</div>
                <div class="version-notice">版本号：{{ siteInfo.androidVersion }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="item-card" @click="jumpDownload(siteInfo.windowsDownloadUrl)">
          <div class="list-item">
            <div class="common-item">
              <div class="item-top">
                <img class="down-icon-img" src="@/assets/images/home/<USER>" alt="" />
              </div>
              <div class="icon-con">
                <img class="icon-img" :src="siteInfo.windowsCover" alt="" />
              </div>
            </div>
            <div class="hover-item-con">
              <div class="hover-item">
                <img class="img" src="@/assets/images/home/<USER>" alt="" />
                <div class="download-notice">点击下载</div>
                <div class="version-notice">版本号：{{ siteInfo.windowsVersion }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="item-card" @click="jumpDownload(siteInfo.macDownloadUrl)">
          <div class="list-item">
            <div class="common-item">
              <div class="item-top">
                <img class="down-icon-img" src="@/assets/images/home/<USER>" alt="" />
              </div>
              <div class="icon-con">
                <img class="icon-img" :src="siteInfo.macCover" alt="" />
              </div>
            </div>
            <div class="hover-item-con">
              <div class="hover-item">
                <img class="img" src="@/assets/images/home/<USER>" alt="" />
                <div class="download-notice">点击下载</div>
                <div class="version-notice">版本号：{{ siteInfo.macVersion }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="item-card" @click="jumpDownload(siteInfo.linuxDownloadUrl)">
          <div class="list-item">
            <div class="common-item">
              <div class="item-top">
                <img class="down-icon-img" src="@/assets/images/home/<USER>" alt="" />
              </div>
              <div class="icon-con">
                <img class="icon-img" :src="siteInfo.linuxCover" alt="" />
              </div>
            </div>
            <div class="hover-item-con">
              <div class="hover-item">
                <img class="img" src="@/assets/images/home/<USER>" alt="" />
                <div class="download-notice">点击下载</div>
                <div class="version-notice">版本号：{{ siteInfo.linuxVersion }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="DownloadCenter">
import useSiteStore from '@/store/modules/site';
const siteInfo = computed(() => useSiteStore().siteInfo);
const props = defineProps({
  title: {
    type: String,
    default: '下载中心',
  },
});
function jumpDownload(url) {
  if (url) {
    if (url.indexOf('http') < 0) {
      url = 'http://' + url;
    }
    window.open(url, '_blank');
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index';
.comp-con {
  width: 100%;
  height: 508px;
  background: url('../../../../assets/images/home/<USER>') no-repeat center;
  background-size: cover;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
  .comp-title-con {
    width: 75%;
    overflow: hidden;
    margin-top: 90px;
    margin-bottom: 79px;
    @extend .base-flex-row;
    justify-content: center;
    align-items: center;
    .title-arrow-img {
      width: 172px;
      height: 24px;
    }
    .title-text {
      height: 26px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 26px;
      color: #333333;
      line-height: 26px;
      text-align: justify;
      font-style: normal;
      margin-left: 25px;
      margin-right: 25px;
    }
  }
  .tab-head-con {
    width: 75%;
    margin-bottom: 44px;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: center;
    .tab-con {
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: center;
      .tab-item {
        width: 104px;
        height: 34px;
        background: #e3e9ec;
        border-radius: 27px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 34px;
        text-align: center;
        font-style: normal;
        margin-right: 30px;
      }
      & .active {
        background: #0966b4;
        border-radius: 27px;
        color: #ffffff;
      }
    }
    .btn-con {
      @extend .base-flex-row;
      justify-content: space-between;
      align-items: center;
      .circle-btn {
        width: 34px;
        height: 34px;
        border-radius: 17px;
        background: #ffffff;
        box-shadow: 0px 0px 7px 0px rgba(201, 219, 237, 0.27);
        @extend .base-flex-row;
        justify-content: center;
        align-items: center;
        .btn-img {
          width: 7px;
          height: 12px;
        }
      }
      & .disabled {
        opacity: 0.49;
      }
      .circle-btn:first-child {
        margin-right: 25px;
      }
    }
  }
  .data-list-con {
    width: 75%;
    margin-bottom: 70px;
    .data-list {
      width: 100%;
      @extend .base-flex-row;
      justify-content: space-between;
      align-items: center;
      .item-card {
        width: 202px;
        height: 202px;
        background: url('../../../../assets/images/home/<USER>') no-repeat center;
        background-size: cover;
        cursor: pointer;
        .list-item {
          width: 100%;
          height: 100%;
          position: relative;
          cursor: pointer;
          .common-item {
            width: 100%;
            height: 100%;
            border-radius: 20px;
            cursor: pointer;
            position: absolute;
            top: 0;
            left: 0;
            @extend .base-flex-column;
            justify-content: flex-start;
            align-items: center;
            .item-top {
              width: 100%;
              height: 50px;
              @extend .base-flex-row;
              justify-content: flex-end;
              align-items: center;
              .desc {
                font-family:
                  PingFangSC,
                  PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #ffffff;
              }
              .down-icon-img {
                width: 15px;
                height: 15px;
                margin-right: 13px;
              }
              .code-edge-img {
                width: auto;
                height: 100%;
              }
            }
            .icon-con {
              .icon-img {
                height: 114px;
                width: auto;
              }
            }
          }
          .hover-item-con {
            width: 100%;
            height: 100%;
            display: none;
            z-index: 2;
            position: absolute;
            top: 0;
            left: 0;
            .hover-item {
              width: 100%;
              height: 100%;
              z-index: 2;
              border-radius: 20px;
              @extend .base-flex-column;
              justify-content: center;
              align-items: center;
              .img {
                width: 58px;
                height: 62px;
                margin-bottom: 14px;
              }
              .or-img-con {
                width: 108px;
                height: 108px;
                background: #ffffff;
                border-radius: 8px;
                margin-bottom: 5px;
                @extend .base-flex-row;
                justify-content: center;
                align-items: center;
                .or-img {
                  width: 87px;
                  height: 87px;
                }
              }
              .download-notice {
                font-family:
                  PingFangSC,
                  PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #ffffff;
                margin-bottom: 12px;
              }
              .version-notice {
                font-family:
                  PingFangSC,
                  PingFang SC;
                font-weight: 500;
                font-size: 12px;
                color: #ffffff;
              }
              .mr-top {
                margin-top: 12px;
              }
            }
          }
        }
        .list-item:hover .common-item {
          display: none;
          transition: all 0.3s ease;
        }
        .list-item:hover .hover-item-con {
          transition: all 0.3s ease;
          animation: show-item 0.5s linear 0.5s 1 normal forwards;
          display: block;
        }
      }
    }
  }
  .btn-con {
    cursor: pointer;
    .btn {
      width: 176px;
      height: 45px;
      line-height: 45px;
      border-radius: 8px;
      border: 1px solid #0966b4;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #0966b4;
      text-align: center;
      font-style: normal;
    }
  }
}
</style>
