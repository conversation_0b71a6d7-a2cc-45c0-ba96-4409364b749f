<template>
  <div class="comp-con">
    <div class="content-con">
      <div class="comp-left">
        <div class="title-con">
          <img class="title-arrow-img" src="@/assets/images/home/<USER>" alt="" />
          <div class="title-text">合作院校</div>
          <img class="title-arrow-img" src="@/assets/images/home/<USER>" alt="" />
        </div>
        <div class="btn-con">
          <div class="btn" @click="toMore">查看更多</div>
        </div>
      </div>
      <div class="comp-right">
        <div class="list-con">
          <div class="item-card-list">
            <div class="item-card mg-left" :class="{ 'empty-card': isEmptyCard(item) }" v-for="(item, index) in partnersListOne" :key="index">
              <img class="item-img" :src="item.logoUrl" alt="" :key="index" />
            </div>
          </div>
        </div>
        <div class="list-con">
          <div class="item-card-list">
            <div class="item-card" :class="{ 'empty-card': isEmptyCard(item) }" v-for="(item, index) in partnersListTwo" :key="index">
              <img class="item-img" :src="item.logoUrl" alt="" :key="index" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="PartnersSchool">
import { ref } from 'vue';
import { getPartners } from '@/api/basic/home.js';
const router = useRouter();
// props
const props = defineProps({
  componentData: {
    type: Object,
    default: {},
  },
});

const emit = defineEmits(['toMore']);
const partners = ref([]);
const partnersListOne = ref([]);
const partnersListTwo = ref([]);
const blank = { schoolName: '', logoUrl: '' };
function doGetPartners() {
  partnersListOne.value = [];
  partnersListTwo.value = [];
  getPartners(8)
    .then((res) => {
      partners.value = res.data;
      if (partners.value.length >= 4) {
        partnersListOne.value = partners.value.slice(0, 4);
        partnersListTwo.value = partners.value.slice(4);
      } else {
        partnersListOne.value = partners.value;
      }
      partnersListOne.value.unshift(blank);
      partnersListOne.value.push(blank);
      partnersListTwo.value.unshift(blank);
      partnersListTwo.value.push(blank);
    })
    .catch((error) => {});
}
doGetPartners();

const isEmptyCard = (item) => {
  return item.logoUrl == '';
};

const toMore = () => {
  // emit('toMore');
  toPartnersSchool();
};

const toPartnersSchool = () => {
  router.push({ path: '/home-partners-school' });
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index';
.comp-con {
  width: 100%;
  height: 448px;
  background: url('../../../../assets/images/home/<USER>') no-repeat center;
  background-size: cover;
  @extend .base-flex-column;
  justify-content: center;
  align-items: center;
  .content-con {
    width: 1920px;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: center;
    .comp-left {
      height: calc(100% - 31px);
      margin-left: 184px;
      @extend .base-flex-column;
      justify-content: center;
      align-items: center;
      .title-con {
        padding-top: 31px;
        @extend .base-flex-row;
        justify-content: center;
        align-items: center;
        margin-bottom: 50px;
        .title-arrow-img {
          width: 52px;
          height: 24px;
          margin-right: 25px;
        }
        .title-text {
          min-width: 104px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 26px;
          color: #333333;
          margin-right: 25px;
        }
      }
      .btn-con {
        cursor: pointer;
        .btn {
          width: 176px;
          height: 45px;
          line-height: 45px;
          border-radius: 8px;
          border: 1px solid #0966b4;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #0966b4;
          text-align: center;
          font-style: normal;
        }
        .btn:hover {
          transition: all 0.3s ease;
          background: #0966b4;
          color: #ffffff;
        }
      }
    }
    .comp-right {
      flex: 1;
      overflow: hidden;
      .list-con {
        width: 100%;
        .item-card-list {
          @extend .base-flex-row;
          justify-content: flex-start;
          align-items: center;
          .item-card {
            width: 229px;
            height: 128px;
            background: #ffffff;
            border-radius: 8px;
            cursor: pointer;
            @extend .base-flex-row;
            justify-content: center;
            align-items: center;
            margin-right: 12px;
            flex-shrink: 0;
            .item-img {
              height: 84px;
              border-radius: 4px;
            }
            .item-img:hover {
              opacity: 0.8;
            }
          }
          .empty-card {
            opacity: 0.4 !important;
          }
          .mg-left {
            margin-bottom: 12px;
          }
          .mg-left:nth-child(1) {
            margin-left: 46px;
          }
        }
      }
    }
  }
}
</style>
