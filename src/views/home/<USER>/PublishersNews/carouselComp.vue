<template>
  <div class="carousel">
    <div class="common-scroll-btn" @click="rotateLeft">
      <img src="@/assets/images/home/<USER>" alt="" />
    </div>
    <div class="carousel-track">
      <div v-for="(item, index) in displayedBooks" :key="index" class="carousel-item" :style="getStyle(index)">
        <img v-if="item?.cover" :src="item?.cover ? item.cover : bookCoverDefault" :alt="item?.bookName" @click="toDetail(item)" />
      </div>
    </div>
    <div class="common-scroll-btn" @click="rotateRight">
      <img src="@/assets/images/home/<USER>" alt="" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import bookCoverDefault from '@/assets/images/book-cover-default-ts.jpg';
// import { encryptData } from '@/utils/encrypt.js';
// import useSiteStore from '@/store/modules/site'
const router = useRouter();
const books = ref([]);

const props = defineProps({
  dataList: {
    type: Array,
    default: [],
  },
});

watch(
  () => props.dataList,
  (newVal) => {
    initBooks();
  }
);

const defaultEmptyBook = {
  // cover: bookCoverDefault,
  cover: '',
  isbn: -1,
  bookName: '',
  authorLabel: '',
  authorValue: '',
  priceCounter: '0.00',
  priceSale: '0.00',
};

/* 初始化全部数据 */
function initBooks() {
  books.value = [];
  if (props.dataList && props.dataList.length > 0) {
    if (props.dataList.length >= 7) {
      books.value = JSON.parse(JSON.stringify(props.dataList));
    } else {
      for (let i = 0; i < 7; i++) {
        books.value.push(defaultEmptyBook);
      }
      let listNum = props.dataList.length;
      let insertQue = [3, 2, 4, 1, 5, 0, 6, 7];
      for (let j = 0; j < listNum; j++) {
        books.value[insertQue[j]] = props.dataList[j];
      }
    }
  }
}

const toDetail = async (item) => {
  /*// // 获取后端的公钥
  // const publicKey = useSiteStore().publicKey;
  // if (!publicKey) {
  //   console.error('无法获取公钥');
  //   return;
  // }

  // // 使用公钥加密数据
  // const { encryptedData } = await encryptData(item.bookId, publicKey);

  // if (!encryptedData) {
  //   console.error('数据加密失败');
  //   return;
  // }*/
  router.push({ path: '/book-detail', query: { key: item.bookId } });
};

// 显示的图片索引，初始化显示 [0, 1, 2, 3, 4, 5, 6]
const visibleIndices = ref([0, 1, 2, 3, 4, 5, 6]);

// 根据索引计算显示的图片
const displayedBooks = computed(() => visibleIndices.value.map((index) => books.value[index]));

// 获取图片样式
const getStyle = (index) => {
  const scaleMap = [0.7, 0.8, 0.9, 1, 0.9, 0.8, 0.7]; // 图片缩放比例
  const zIndexMap = [1, 2, 3, 4, 3, 2, 1]; // 层级
  const translateXMap = [-880, -540, -250, 0, 250, 540, 880]; // X 偏移
  const opacityMap = [0.4, 0.7, 0.8, 1, 0.8, 0.7, 0.4]; // 透明度

  return {
    transform: `scale(${scaleMap[index]}) translateX(${translateXMap[index]}px)`,
    zIndex: zIndexMap[index],
    opacity: opacityMap[index],
  };
};

const refreshFlag = ref(true);
const doRefresh = () => {
  refreshFlag.value = false;
  setTimeout(() => {
    refreshFlag.value = true;
  }, 1);
};
// 左旋转
const rotateLeft = () => {
  const first = visibleIndices.value.shift(); // 移出第一个
  visibleIndices.value.push((first + 7) % books.value.length); // 添加下一个
  doRefresh();
};

// 右旋转
const rotateRight = () => {
  const last = visibleIndices.value.pop(); // 移出最后一个
  visibleIndices.value.unshift((last - 1 + books.value.length) % books.value.length); // 添加上一个
  doRefresh();
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index';

.carousel {
  display: flex;
  display: -webkit-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: 293px;
  margin: 50px auto;
  border-radius: 15px;
}

.carousel-track {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
  padding: 10px 0;
  perspective: 1000px;
}

.carousel-item {
  width: 216px;
  height: 290px;
  position: absolute;
  top: 0;
  transform-origin: center;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
  cursor: pointer;
  margin-bottom: 20px;
  background: transparent;
}

.carousel-item img {
  width: 216px;
  height: 290px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  animation: activeItem 1.5s forwards;
  background: transparent;
  @keyframes activeItem {
    0% {
      transform: scale(1);
    }
    25% {
      transform: scale(1.02);
    }
    50% {
      transform: scale(1.03);
    }
    75% {
      transform: scale(1.02);
    }
    100% {
      transform: scale(1);
    }
  }
}

.arrow-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
}

.arrow-btn.left {
  left: 10px;
}

.arrow-btn.right {
  right: 10px;
}

.common-scroll-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  cursor: pointer;
  @extend .base-flex-row;
  justify-content: center;
  align-items: center;

  img {
    width: 9px;
    height: 15px;
  }
}
.common-scroll-btn:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
</style>
