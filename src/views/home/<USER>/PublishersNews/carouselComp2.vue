<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
  <div class="container">
    <div class="stage-container">
      <div class="book-carousel">
        <swiper
          :modules="swiperModules"
          :effect="'coverflow'"
          :slides-per-view="5"
          :space-between="60"
          :centered-slides="true"
          :pagination="{ clickable: true }"
          :autoplay="{ delay: 3000, disableOnInteraction: false }"
          :loop="true"
          :coverflow-effect="{
            rotate: 0,
            stretch: 0,
            depth: 200,
            modifier: 1,
            slideShadows: true,
          }"
          class="swiper-container"
          @swiper="onSwiper"
        >
          <swiper-slide v-for="(book, index) in books" :key="index">
            <div class="book-item">
              <img :src="book.cover" :alt="book.title" class="book-image" />
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <div class="navigation">
        <el-button class="prev-btn !rounded-button" @click="prevSlide">
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        <el-button class="next-btn !rounded-button" @click="nextSlide">
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>
    </div>
    <el-button type="primary" class="enter-button !rounded-button whitespace-nowrap" @click="enterZone"> 进入专区 </el-button>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Pagination, Autoplay, EffectCoverflow } from 'swiper/modules';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/effect-coverflow';
const swiperModules = [Pagination, Autoplay, EffectCoverflow];
const swiperInstance = ref();
const onSwiper = (swiper) => {
  swiperInstance.value = swiper;
};
const books = ref([
  {
    title: '路面工程施工 第一版',
    cover: 'https://ai-public.mastergo.com/ai/img_res/d5584a9f530d684b59dbe7a1e7754eb1.jpg',
  },
  {
    title: '路面工程施工 第二版',
    cover: 'https://ai-public.mastergo.com/ai/img_res/69e7098b0ba0768c437681a7ba31042d.jpg',
  },
  {
    title: '路面工程施工 第三版',
    cover: 'https://ai-public.mastergo.com/ai/img_res/ba56e214f54b47e3430377934c4af595.jpg',
  },
  {
    title: '路面工程施工 第四版',
    cover: 'https://ai-public.mastergo.com/ai/img_res/32df865521be54a2b58aa97a231b90b4.jpg',
  },
  {
    title: '路面工程施工 第五版',
    cover: 'https://ai-public.mastergo.com/ai/img_res/3470d7bafcc277cd10c8ee832168dc07.jpg',
  },
  {
    title: '路面工程施工 第六版',
    cover: 'https://ai-public.mastergo.com/ai/img_res/fa5bf14c46fb94b93df1e8eb25700114.jpg',
  },
  {
    title: '路面工程施工 第七版',
    cover: 'https://ai-public.mastergo.com/ai/img_res/c3e13969dd87dad0293a012e8e840baa.jpg',
  },
]);
const prevSlide = () => {
  swiperInstance.value?.slidePrev();
};
const nextSlide = () => {
  swiperInstance.value?.slideNext();
};
const enterZone = () => {
  console.log('进入专区');
};
</script>
<style scoped>
.container {
  min-height: 100vh;
  background-color: rgb(249, 250, 251);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.stage-container {
  position: relative;
  width: 1440px;
  height: 290px;
  margin: 0 auto;
  perspective: 1200px;
  overflow: hidden;
  padding: 0 60px;
}
.book-carousel {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  margin: 0 auto;
  padding: 0 20px;
}
.swiper-container {
  width: 100%;
  height: 100%;
}
.book-item {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
  overflow: hidden;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  height: 290px;
  width: 216px;
}
.book-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}
.book-item:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
.navigation {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0;
  left: 0;
}
.navigation button {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  border: none;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
}
.navigation button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
.enter-button {
  margin-top: 32px;
}
:deep(.swiper) {
  width: 100%;
  padding: 0;
}
:deep(.swiper-slide) {
  background-position: center;
  background-size: cover;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  width: auto !important;
  display: flex;
  justify-content: center;
}
:deep(.swiper-slide-active) {
  z-index: 2;
}
:deep(.swiper-pagination) {
  position: absolute;
  bottom: 0;
  width: 100%;
}
:deep(.swiper-pagination-bullet) {
  background: #4a5568;
  opacity: 0.5;
  transition: all 0.3s ease;
}
:deep(.swiper-pagination-bullet-active) {
  background: #2d3748;
  opacity: 1;
  transform: scale(1.2);
}
:deep(.swiper-3d) {
  perspective: 1200px;
}
:deep(.swiper-3d .swiper-slide-shadow-left),
:deep(.swiper-3d .swiper-slide-shadow-right) {
  background-image: none;
}
:deep(.swiper-pagination) {
  position: absolute;
  text-align: center;
  transition: 300ms opacity;
  transform: translate3d(0, 0, 0);
  z-index: 10;
}
</style>
