<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
  <div class="container">
    <div class="stage-container">
      <div class="book-carousel">
        <swiper
          :modules="swiperModules"
          :effect="'coverflow'"
          :slides-per-view="7"
          :space-between="40"
          :centered-slides="true"
          :pagination="{ clickable: true }"
          :autoplay="{ delay: 3000, disableOnInteraction: false }"
          :loop="true"
          :coverflow-effect="{
            rotate: 0,
            stretch: 0,
            depth: 180,
            modifier: 1,
            slideShadows: true,
          }"
          class="swiper-container"
          @swiper="onSwiper"
        >
          <swiper-slide v-for="(item, index) in books" :key="index">
            <div class="book-item">
              <!-- :style="getStyle(swiperInstance?.activeIndex)"  -->
              <img v-if="item?.cover" :src="item?.cover ? item.cover : bookCoverDefault" alt="" class="book-image" @click="toDetail(item)" />
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <div class="navigation">
        <el-button class="prev-btn !rounded-button" @click="nextSlide">
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        <el-button class="next-btn !rounded-button" @click="prevSlide">
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Pagination, Autoplay, EffectCoverflow } from 'swiper/modules';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/effect-coverflow';

// const swiperModules = [Pagination, Autoplay, EffectCoverflow];
const swiperModules = [EffectCoverflow];
const swiperInstance = ref();
import bookCoverDefault from '@/assets/images/book-cover-default-ts.jpg';
import { useRouter } from 'vue-router';

const router = useRouter();
const props = defineProps({
  dataList: {
    type: Array,
    default: [],
  },
});

watch(
  () => props.dataList,
  (newVal) => {
    initBooks();
  }
);

const getStyle = (index) => {
  console.log('----index------');
  console.log(index);
  // const scaleMap = [0.7, 0.8, 0.9, 1, 0.9, 0.8, 0.7]; // 图片缩放比例
  // const zIndexMap = [1, 2, 3, 4, 3, 2, 1]; // 层级
  // const translateXMap = [-880, -540, -250, 0, 250, 540, 880]; // X 偏移
  const opacityMap = [0.4, 0.7, 0.8, 1, 0.8, 0.7, 0.4]; // 透明度

  /*return {
    transform: `scale(${scaleMap[index]}) translateX(${translateXMap[index]}px)`,
    zIndex: zIndexMap[index],
    opacity: opacityMap[index],
  };*/
  return {
    opacity: opacityMap[index],
  };
};

const defaultEmptyBook = {
  // cover: 'https://dutp-test.oss-cn-beijing.aliyuncs.com/cms/book-cover-default-ts.jpg',
  cover: '',
  isbn: -1,
  bookName: '占位图书',
  authorLabel: '',
  authorValue: '',
  priceCounter: '0.00',
  priceSale: '0.00',
};

function initBooks() {
  if (props.dataList && props.dataList.length > 0) {
    books.value = JSON.parse(JSON.stringify(props.dataList));

    if (props.dataList.length < 7) {
      let lostQuantity = 7 - props.dataList.length;

      for (let i = 0; i < lostQuantity; i++) {
        books.value.push(defaultEmptyBook);
      }

      /*// 头加一个，尾加一个，一共lostQuantity次
      let needAddNum = lostQuantity / 2 + 1;
      for (let i = 0; i < needAddNum; i++) {
        // books.value.unshift(defaultEmptyBook);
        // books.value.push(defaultEmptyBook);
      }*/
    }
  }
}

function toDetail(item) {
  router.push({ path: '/book-detail', query: { key: item.bookId } });
}

const onSwiper = (swiper) => {
  swiperInstance.value = swiper;
};

const books = ref([]);

/*const books = ref([
  {
    title: '路面工程施工 第一版',
    cover: 'https://ai-public.mastergo.com/ai/img_res/d5584a9f530d684b59dbe7a1e7754eb1.jpg',
  },
  {
    title: '路面工程施工 第二版',
    cover: 'https://ai-public.mastergo.com/ai/img_res/69e7098b0ba0768c437681a7ba31042d.jpg',
  },
  {
    title: '路面工程施工 第三版',
    cover: 'https://ai-public.mastergo.com/ai/img_res/ba56e214f54b47e3430377934c4af595.jpg',
  },
  {
    title: '路面工程施工 第四版',
    cover: 'https://ai-public.mastergo.com/ai/img_res/78b88c7ba8be60b3417afc6d2bcf9ece.jpg',
  },
  {
    title: '路面工程施工 第五版',
    cover: 'https://ai-public.mastergo.com/ai/img_res/10f5f372e7faee3e669cd6b5ab0391ef.jpg',
  },
]);*/
const prevSlide = () => {
  swiperInstance.value?.slidePrev();
};
const nextSlide = () => {
  swiperInstance.value?.slideNext();
};
const enterZone = () => {
  console.log('进入专区');
};
</script>
<style scoped>
.container {
  min-height: 450px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.stage-container {
  position: relative;
  width: 1440px;
  height: 290px;
  margin: 0 auto;
  perspective: 2400px;
  overflow: hidden;
  padding: 0 60px;
}
.book-carousel {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  margin: 0 auto;
  padding: 0 20px;
}
.swiper-container {
  width: 100%;
  height: 100%;
}
.book-item {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
  overflow: hidden;
  /*box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);*/
  height: 290px;
  width: 216px;
  background: transparent;
}
.book-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}
.book-item:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
.navigation {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0;
  left: 0;
}
.navigation button {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  border: none;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
}
.navigation button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
.enter-button {
  margin-top: 32px;
}
:deep(.swiper) {
  width: 100%;
  padding: 0;
}
:deep(.swiper-slide) {
  background-position: center;
  background-size: cover;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  width: auto !important;
  display: flex;
  justify-content: center;
}
:deep(.swiper-slide-active) {
  z-index: 2;
}
:deep(.swiper-pagination) {
  position: absolute;
  bottom: 0;
  width: 100%;
}
:deep(.swiper-pagination-bullet) {
  background: #4a5568;
  opacity: 0.5;
  transition: all 0.3s ease;
}
:deep(.swiper-pagination-bullet-active) {
  background: #2d3748;
  opacity: 1;
  transform: scale(1.2);
}
:deep(.swiper-3d) {
  perspective: 2400px;
}
:deep(.swiper-3d .swiper-slide-shadow-left),
:deep(.swiper-3d .swiper-slide-shadow-right) {
  background-image: none;
}
:deep(.swiper-pagination) {
  position: absolute;
  text-align: center;
  transition: 300ms opacity;
  transform: translate3d(0, 0, 0);
  z-index: 10;
}
</style>
