<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
  <div class="container">
    <!-- 测试字体动态引用   -->
    测试字体动态引用
    <i class="edui-iconfont edui-icon-align-justify"></i>
    <i class="edui-iconfont edui-icon-smile"></i>

    <div>2、出版社-其他字体</div>
    <!-- 根据字体加载状态显示不同内容 -->
    <h1 :style="{ fontFamily: 'FZCHYK' }">这是自定义字体效果6666666</h1>
    <h1 :style="{ fontFamily: 'FZCYK' }">这是自定义字体效果7777</h1>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import '@/assets/font/iconfont.css';
// 组件中使用
import { useFontStore } from '@/store/modules/fontStore';

const fontStatus = ref('loading'); // 状态：loading | loaded | error
const fontStore = useFontStore();
const loadFont = async () => {
  try {
    // 1. 创建字体对象
    const font = new FontFace('FZCHYK', 'url(http://************/font/test-font/FZCHYK.TTF)', {
      style: 'normal',
      weight: '400',
      display: 'swap',
    });

    // 2. 加载字体
    const loadedFont = await font.load();
    // 3. 将字体添加到文档
    document.fonts.add(loadedFont);

    // 4. 验证字体是否可用
    await document.fonts.ready;
    fontStatus.value = 'loaded';
  } catch (err) {
    console.error('字体加载失败:', err);
    fontStatus.value = 'error';
  }
};

// 组件挂载时加载字体
onMounted(() => {
  // loadFont();
  // let fontArr = ['FZCHYK.TTF', 'FZCYK.TTF'];
  // fontStore.loadDutpFonts(fontArr);
});

// 可选：离开页面时移除字体（根据需求）
// onUnmounted(() => {
//   document.fonts.forEach(font => {
//     if(font.family === 'FZCHYK') {
//       document.fonts.delete(font);
//     }
//   });
// });
</script>
<style scoped>
.container {
  min-height: 450px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.stage-container {
  position: relative;
  width: 1440px;
  height: 290px;
  margin: 0 auto;
  perspective: 2400px;
  overflow: hidden;
  padding: 0 60px;
}
.book-carousel {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  margin: 0 auto;
  padding: 0 20px;
}
.swiper-container {
  width: 100%;
  height: 100%;
}
.book-item {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
  overflow: hidden;
  /*box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);*/
  height: 290px;
  width: 216px;
  background: transparent;
}
.book-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}
.book-item:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
.navigation {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0;
  left: 0;
}
.navigation button {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  border: none;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
}
.navigation button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
.enter-button {
  margin-top: 32px;
}
:deep(.swiper) {
  width: 100%;
  padding: 0;
}
:deep(.swiper-slide) {
  background-position: center;
  background-size: cover;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  width: auto !important;
  display: flex;
  justify-content: center;
}
:deep(.swiper-slide-active) {
  z-index: 2;
}
:deep(.swiper-pagination) {
  position: absolute;
  bottom: 0;
  width: 100%;
}
:deep(.swiper-pagination-bullet) {
  background: #4a5568;
  opacity: 0.5;
  transition: all 0.3s ease;
}
:deep(.swiper-pagination-bullet-active) {
  background: #2d3748;
  opacity: 1;
  transform: scale(1.2);
}
:deep(.swiper-3d) {
  perspective: 2400px;
}
:deep(.swiper-3d .swiper-slide-shadow-left),
:deep(.swiper-3d .swiper-slide-shadow-right) {
  background-image: none;
}
:deep(.swiper-pagination) {
  position: absolute;
  text-align: center;
  transition: 300ms opacity;
  transform: translate3d(0, 0, 0);
  z-index: 10;
}
</style>
