<template>
  <div class="publisher-list-comp-con">
    <div class="list-scroll-btn" @click="rotateLeft">
      <img src="@/assets/images/home/<USER>" alt="" />
    </div>
    <div class="publisher-list">
      <div
        class="item-card"
        :class="{ active: index === currentActiveIndex }"
        v-for="(item, index) in displayItemsList"
        :key="index"
        @click="changeCurrentActiveIndex(index, item)"
      >
        <div class="list-item" :class="{ 'active-item': index === currentActiveIndex }">
          <img class="item-img" :src="item.logoUrl" alt="" />
          <div class="item-label">{{ item.houseName }}</div>
        </div>
      </div>
    </div>
    <div class="list-scroll-btn" @click="rotateRight">
      <img src="@/assets/images/home/<USER>" alt="" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
const emit = defineEmits(['changePublisher']);

const props = defineProps({
  dataList: {
    type: Array,
    default: [],
  },
});

let timer = ref(null);
let pauseDuration = ref(2000);
const minDataListLength = 6;
const currentActiveIndex = ref(0);
const currentSelectedItemsListIndex = ref(0);
let displayItemsList = ref([]);
let itemsList = ref([]);

/*
onMounted(() => {
  itemsList.value = props.dataList;
  initDisplayItemsList();
});
*/

watch(
  () => props.dataList,
  (newValue) => {
    itemsList.value = newValue;
    initDisplayItemsList();
  }
);

function changeCurrentActiveIndex(index) {
  currentActiveIndex.value = index;
  emit('changePublisher', currentActiveIndex.value % itemsList.value.length);
}

// 初始化显示列表
const initDisplayItemsList = () => {
  if (!Array.isArray(itemsList.value)) {
    console.error('itemsList is not an array');
    return;
  }

  if (itemsList.value.length === 0) {
    displayItemsList.value = [];
    currentSelectedItemsListIndex.value = 0;
    return;
  }

  if (itemsList.value.length >= minDataListLength) {
    displayItemsList.value = itemsList.value.slice(0, minDataListLength);
    currentSelectedItemsListIndex.value = minDataListLength - 1;
  } else {
    displayItemsList.value = Array.from({ length: minDataListLength }, (_, i) => itemsList.value[i % itemsList.value.length]);
    currentSelectedItemsListIndex.value = minDataListLength - 1;
  }
  // 定时循环执行任务
  startPublisherTimer();
};

const startPublisherTimer = () => {
  timer.value = setInterval(() => {
    rotateRight();
  }, pauseDuration.value);
  document.body.addEventListener('click', () => {
    clearInterval(timer.value);
  });
};

onBeforeUnmount(() => {
  clearInterval(timer.value);
  timer.value = null;
  document.body.removeEventListener('click', close);
});

// 向左旋转
const rotateLeft = () => {
  handleScroll(-1);
};

// 向右旋转
const rotateRight = () => {
  handleScroll(1);
};

// 滚动逻辑处理
const handleScroll = (stepValue) => {
  const prevIndex = currentActiveIndex.value;
  currentActiveIndex.value += stepValue;
  if (currentActiveIndex.value >= minDataListLength) {
    currentActiveIndex.value = 0;
    loadRightNextBatchData();
    triggerSlideAnimation('right');
  } else if (currentActiveIndex.value < 0) {
    currentActiveIndex.value = minDataListLength - 1;
    loadLeftNextBatchData();
    triggerSlideAnimation('left');
  }
  if (prevIndex !== currentActiveIndex.value) {
    // triggerSlideAnimation(stepValue > 0 ? 'right' : 'left');
  }
  emit('changePublisher', currentActiveIndex.value % itemsList.value.length);
};

// 加载下一批数据（右方向）
const loadRightNextBatchData = () => {
  const startIndex = (currentSelectedItemsListIndex.value + 1) % itemsList.value.length;
  const newItems = [];
  for (let i = 0; i < minDataListLength; i++) {
    const index = (startIndex + i) % itemsList.value.length;
    newItems.push(itemsList.value[index]);
  }
  displayItemsList.value = newItems;
  currentSelectedItemsListIndex.value = (startIndex + minDataListLength - 1) % itemsList.value.length;
};

// 加载下一批数据（左方向）
const loadLeftNextBatchData = () => {
  const startIndex = (currentSelectedItemsListIndex.value - minDataListLength + itemsList.value.length) % itemsList.value.length;
  const newItems = [];
  for (let i = 0; i < minDataListLength; i++) {
    const index = (startIndex + i + itemsList.value.length) % itemsList.value.length;
    newItems.push(itemsList.value[index]);
  }
  displayItemsList.value = newItems;
  currentSelectedItemsListIndex.value = startIndex;
};

// 触发横移动画
function triggerSlideAnimation(direction) {
  const listElement = document.querySelector('.publisher-list');
  if (!listElement) return;

  const animationClass = direction === 'left' ? 'slide-left' : 'slide-right';
  listElement.classList.add(animationClass);

  setTimeout(() => {
    listElement.classList.remove(animationClass);
  }, 100); // 动画持续时间与 CSS 一致
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index';
.publisher-list-comp-con {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .publisher-list {
    max-width: 90%;
    height: 100%;
    border-bottom: 6px solid #c6d6ea;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    &.slide-left {
      animation: slideLeft 0.5s forwards;
    }
    &.slide-right {
      animation: slideRight 0.5s forwards;
    }
    @keyframes slideLeft {
      from {
        transform: translateX(0);
      }
      to {
        transform: translateX(100%);
      }
    }
    @keyframes slideRight {
      from {
        transform: translateX(0);
      }
      to {
        transform: translateX(-100%);
      }
    }

    .item-card {
      width: 16%;
      height: 100%;
      margin-right: 6px;
      margin-bottom: -11px;
      display: flex;
      justify-content: center;
      align-items: center;
      .list-item {
        width: 208px;
        height: 104px;
        background: #ffffff;
        box-shadow: 0px 0px 12px 0px #e6eef8;
        border-radius: 16px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-bottom: 2px;
        cursor: pointer;
        .item-img {
          height: 53px;
          border-radius: 4px;
          margin-bottom: 5px;
        }
        .item-label {
          max-width: 200px;
          font-size: 14px;
          color: #333333;
        }
      }
      .list-item:hover {
        opacity: 0.8;
      }
      .active-item {
        width: 208px;
        height: 135px;
        border: 3px solid #0966b4;
        // 动画显示，边框颜色加进是由虚变实在，透明度
        animation: activeItem 0.5s forwards;
        @keyframes activeItem {
          0% {
            transform: scale(1);
          }
          25% {
            transform: scale(1.03);
          }
          50% {
            transform: scale(1.04);
          }
          75% {
            transform: scale(1.03);
          }
          100% {
            transform: scale(1);
          }
        }
      }
    }
    .active {
      border-bottom: 6px solid #0966b4;
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
    }
    .item-card:last-child {
      margin-right: 0px;
      bakckground: #f5f5f5;
    }
  }

  .list-scroll-btn {
    width: 40px;
    height: 40px;
    background: #ffffff;
    border-radius: 20px;
    display: flex;
    cursor: pointer;
    justify-content: center;
    align-items: center;
    margin-right: 20px;
    img {
      width: 9px;
      height: 15px;
    }
  }
  .list-scroll-btn:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
}
</style>
