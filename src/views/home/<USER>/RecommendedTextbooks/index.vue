<template>
  <div class="comp-con">
    <div class="comp-title-con">
      <img
        class="title-arrow-img"
        src="@/assets/images/home/<USER>"
        alt=""
      />
      <span class="title-text">{{ data.title }}</span>
      <img
        class="title-arrow-img"
        src="@/assets/images/home/<USER>"
        alt=""
      />
    </div>
    <!-- 01 教材列表   -->
    <div class="data-list-con">
      <div class="common-scroll-btn" @click="handleBooksScroll(-1)">
        <img src="@/assets/images/home/<USER>" alt="" />
      </div>
      <div class="books-list">
        <div class="item-card" v-for="(item, index) in books" :key="index">
          <transition name="el-fade-in-linear">
            <div class="list-item" @click="toDetail(item)">
              <img
                class="item-img"
                :src="item.cover ? item.cover : bookCoverDefault"
                alt=""
              />
              <div class="item-name">{{ item.bookName }}</div>
              <div class="item-author">
                {{ item.authorLabel }}：{{ item.authorValue }}
              </div>
              <div class="item-price">￥{{ item.priceSale }}</div>
            </div>
          </transition>
        </div>
      </div>
      <div class="common-scroll-btn" @click="handleBooksScroll(1)">
        <img src="@/assets/images/home/<USER>" alt="" />
      </div>
    </div>
    <!-- 03 跳转按钮 -->
    <div class="btn-con">
      <div class="btn" @click="toMore">查看更多</div>
    </div>
  </div>
</template>

<script setup name="RecommendedTextbooks">
import { ref } from "vue";
import bookCoverDefault from "@/assets/images/book-cover-default.png";
import { useRouter } from "vue-router";
const router = useRouter();
const props = defineProps({
  componentData: {
    type: Object,
    default: {},
  },
});
// let title = ref('重点教材推荐');
const data = ref({});
let books = ref([]);
//
onMounted(() => {
  data.value = props.componentData;
  books.value = data.value.books.slice(0, 5);
});

function toMore() {
  // router.push(data.value.toMoreRoute);
  router.push({
    path: '/search'
    , query: { isPublisher: 2 }
  });
}

function toDetail(item) {
  router.push({ path: "/book-detail", query: { key: item.bookId } });
}
const pageNum = ref(1);
function handleBooksScroll(step) {
  const maxPage = Math.ceil(data.value.books.length / 5);
  if (pageNum.value <= 1 && step < 0) return;
  if (pageNum.value >= maxPage && step > 0) return;
  // 前翻
  if (step > 0) {
    books.value = data.value.books.slice(
      5 * pageNum.value,
      5 * (pageNum.value + 1)
    );
    triggerSlideAnimation("right");
    pageNum.value++;
    // 后返
  } else {
    pageNum.value--;
    books.value = data.value.books.slice(
      5 * (pageNum.value - 1),
      5 * pageNum.value
    );
    triggerSlideAnimation("left");
  }
}
// 触发横移动画
function triggerSlideAnimation(direction) {
  const listElement = document.querySelector(".books-list");
  if (!listElement) return;

  const animationClass = direction === "left" ? "slide-left" : "slide-right";
  listElement.classList.add(animationClass);

  setTimeout(() => {
    listElement.classList.remove(animationClass);
  }, 100); // 动画持续时间与 CSS 一致
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/index";
.comp-con {
  width: 100%;
  height: 879px;
  background: url("../../../../assets/images/home/<USER>")
    no-repeat center;
  background-size: cover;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
  .comp-title-con {
    width: 100%;
    overflow: hidden;
    margin-top: 90px;
    margin-bottom: 79px;
    @extend .base-flex-row;
    justify-content: center;
    align-items: center;

    .title-arrow-img {
      width: 172px;
      height: 24px;
    }

    .title-text {
      height: 26px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 26px;
      color: #333333;
      line-height: 26px;
      text-align: justify;
      font-style: normal;
      margin-left: 25px;
      margin-right: 25px;
    }
  }
  .data-list-con {
    width: 80%;
    height: 400px;
    margin-bottom: 80px;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: center;
    .common-scroll-btn {
      width: 40px;
      height: 40px;
      border-radius: 20px;
      cursor: pointer;
      @extend .base-flex-row;
      justify-content: center;
      align-items: center;
      img {
        width: 105px;
        height: 105px;
      }
    }
    .books-list {
      height: 100%;
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: center;
      &.slide-left {
        animation: slideLeft 0.5s forwards;
      }
      &.slide-right {
        animation: slideRight 0.5s forwards;
      }
      @keyframes slideLeft {
        from {
          transform: translateX(0);
        }
        to {
          transform: translateX(100%);
        }
      }
      @keyframes slideRight {
        from {
          transform: translateX(0);
        }
        to {
          transform: translateX(-100%);
        }
      }
      .item-card {
        width: 239px;
        height: 418px;
        background: #ffffff;
        box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);
        padding-top: 16px;
        border-radius: 16px;
        margin-right: 26px;
        @extend .base-flex-column;
        justify-content: flex-start;
        align-items: center;
        .list-item {
          padding:0 20px;
          cursor: pointer;
          @extend .base-flex-column;
          justify-content: flex-start;
          align-items: center;
          .item-img {
            width: 191px;
            height: 279px;
            border-radius: 4px;
            margin-bottom: 16px;
          }
          .item-name {
            width: 191px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #333333;
            text-align: left;
            text-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
            font-style: normal;
            margin-bottom: 7px;
            align-self: flex-start;
            // 超出一行时...
            @extend .base-text-ellipsis;
          }
          .item-author {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            text-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
            text-align: left;
            font-style: normal;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            height: 37px;
            text-overflow: ellipsis;
            align-self: flex-start;
          }
          .item-price {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #0966b4;
            text-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
            text-align: left;
            font-style: normal;
            align-self: flex-start;
          }
        }
        .list-item:hover {
          opacity: 0.9;
        }
      }
    }
  }
  .btn-con {
    width: 100%;
    @extend .base-flex-row;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    .btn {
      width: 176px;
      height: 45px;
      line-height: 45px;
      border-radius: 8px;
      border: 1px solid #0966b4;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #0966b4;
      line-height: 45px;
      text-align: justify;
      font-style: normal;
      text-align: center;
    }
    .btn:hover {
      transition: all 0.3s ease;
      background: #0966b4;
      color: #ffffff;
    }
  }
}
</style>
