<template>
  <div class="desc-con">
    <div class="content-con">
      <div class="desc-left">
        <div class="img-con">
          <img :src="data.leftImage" alt="" />
        </div>
      </div>
      <div class="right-con">
        <div class="desc-right">
          <div class="right-title">{{data.title}}</div>
          <div class="right-content">
            {{data.content}}
          </div>
          <div class="line" />
          <div class="desc-data-list">
            <div class="desc-data-item" v-for="(item, index) in data.timeLine" :key="index">
              <div class="item-title">{{item.number}}<span>{{item.unit}}</span></div>
              <div class="item-content">{{item.desc}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="aboutUsDesc">
  const show = ref(false);
  const props = defineProps({
    componentData: {
      type: Object,
      default: {},
    },
    });
    const data = ref({});
    onMounted(() => {
      data.value = props.componentData;
    });
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.desc-con {
  width: 100%;
  @extend .base-flex-row;
  justify-content: center;
  align-items: center;
  .content-con {
    width: 1400px;
    padding-top: 51px;
    @extend .base-flex-row;
    justify-content: center;
    align-items: center;
    .desc-left {
      position: relative;
      width: 448px;
      height: 658px;
      border-radius: 2px;
      .img-con {
        position: relative;
        z-index: 1;
        img {
          position: absolute;
          width: 100%;
          height: 659px;
          margin-right: -200px;
          border-radius: 4px;
        }
      }
    }
    .right-con {
      margin-left: -50px;
      width: 100%;
      min-height: 778px;
      background: #f6fbff;
      padding-left: 140px;
      border-radius: 4px;
      .desc-right {
        width: 90%;
        padding-top: 45px;
        padding-bottom: 45px;
        @extend .base-flex-column;
        justify-content: center;
        align-items: flex-start;
        .right-title {
          width: 100%;
          min-height: 53px;
          font-family:
              PingFangSC,
              PingFang SC;
          font-weight: 600;
          font-size: 38px;
          color: #0966b4;
          line-height: 53px;
          text-align: left;
          margin-bottom: 25px;
        }
        .right-content {
          width: 100%;
          min-height: 400px;
          font-family:
              PingFangSC,
              PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #333333;
          line-height: 27px;
          text-align: justify;
          text-indent: 2em;
        }
        .line {
          width: 186px;
          height: 2px;
          background: #0771bc;
        }
        .desc-data-list {
          width: 100%;
          overflow-y: auto;
          padding-top: 10px;
          @extend .base-flex-row;
          justify-content: space-between;
          align-items: flex-start;
          .desc-data-item {
            @extend .base-flex-column;
            justify-content: center;
            align-items: center;
            .item-title {
              font-family: DINAlternate, DINAlternate;
              font-weight: bold;
              font-size: 45px;
              color: #333333;
              line-height: 78px;
              span {
                width: 17px;
                height: 23px;
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 400;
                font-size: 17px;
                color: #333333;
                line-height: 23px;
              }
            }
            .item-content {
              width: 73px;
              min-height: 20px;
              font-family:
                  PingFangSC,
                  PingFang SC;
              font-weight: 400;
              font-size: 14px;
              color: #666666;
              line-height: 20px;
              text-align: left;
            }
          }
        }
      }
    }
  }
}
</style>
