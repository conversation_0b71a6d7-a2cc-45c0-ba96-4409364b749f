<template>
  <div class="honor-con">
    <div class="honor-title-con">
      <img class="btn-img" src="@/assets/images/home/<USER>" alt="" @click="honorBtnClick(-1)" />
      <div class="title">{{data.title}}</div>
      <div class="time-line">
        <div class="time-line-item" v-for="(item, index) in data.honors" :key="index">
          <div class="item-cell">
            <div class="year">{{ item.year }}</div>
            <div class="bottom-line">
              <div class="left-line" />
              <div :class="[{ circle: index === currentIndex }, { 'circle-blank': index !== currentIndex }]" />
              <div class="right-line" />
            </div>
          </div>
        </div>
      </div>
      <img class="btn-img" src="@/assets/images/home/<USER>" alt="" @click="honorBtnClick(1)" />
    </div>
    <div class="honor-detail-con" @mousedown="startDrag" @mousemove="drag" @mouseup="endDrag" @mouseleave="endDrag">
      <div class="detail-list-con" :style="{ transform: `translateX(${translateX}px)` }">
        <div class="honor-detail" v-for="(item, index) in data.honors" :key="index">
          <div class="detail-title">{{ item.year }}</div>
          <div class="title-blue-line" />
          <div class="title-crosse-line" />
          <div class="detail-content">
            {{item.content}}
          </div>
          <div class="detail-img-con">
            <img :src="item.image" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="aboutUsHonor">
const props = defineProps({
  componentData: {
    type: Object,
    default: {},
  },
});
const data = ref({});
onMounted(() => {
  data.value = props.componentData;
});

const currentYear = ref(2021);
const translateX = ref(0);
const currentIndex = ref(0);
const itemWidth = 300; // 假设每个item的宽度是350px

const isDragging = ref(false);
const startX = ref(0);
const startScrollLeft = ref(0);

function honorBtnClick(flagNum) {
  // currentIndex.value = years.value.indexOf(currentYear.value) + 1;
  refreshCurrentIndex(flagNum);
  translateX.value -= flagNum * itemWidth;
}
function refreshCurrentIndex(flagNum) {
  currentIndex.value = currentIndex.value + flagNum;
  if (currentIndex.value < 0) {
    currentIndex.value = 0;
  } else if (currentIndex.value >= data.value.honors.length - 1) {
    currentIndex.value =  data.value.honors.length - 1;
  }
}

function startDrag(event) {
  isDragging.value = true;
  startX.value = event.pageX - event.currentTarget.offsetLeft;
  startScrollLeft.value = translateX.value;
}

function drag(event) {
  if (!isDragging.value) return;
  const x = event.pageX - event.currentTarget.offsetLeft;
  const walk = (x - startX.value) * 2; // 可以调整这个系数来改变拖拽速度
  translateX.value = startScrollLeft.value + walk;
}

function endDrag() {
  isDragging.value = false;
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.honor-con {
  width: 100%;
  min-height: 720px;
  background: url('../../../../assets/images/home/<USER>') no-repeat;
  background-size: cover;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
  .honor-title-con {
    width: 100%;
    padding: 110px 95px 50px 95px;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: center;
    .btn-img {
      width: 70px;
      height: 70px;
      border-radius: 35px;
      cursor: pointer;
    }
    .btn-img:hover {
      opacity: 0.8;
    }
    .title {
      // width: 160px;
      height: 56px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 40px;
      color: #333333;
      line-height: 56px;
    }
    .time-line {
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: center;
      .time-line-item {
        width: 120px;
        height: 48px;
        @extend .base-flex-column;
        justify-content: center;
        align-items: stretch;
        .year {
          min-width: 45px;
          height: 28px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 600;
          font-size: 20px;
          color: #333333;
          text-align: center;
          line-height: 28px;
          margin-bottom: 10px;
        }
        .bottom-line {
          width: 100%;
          @extend .base-flex-row;
          justify-content: center;
          align-items: center;
          .left-line {
            flex: 1;
            height: 1px;
            background-color: #0966b4;
          }
          .circle {
            width: 12px;
            height: 12px;
            border-radius: 12px;
            border: 1px solid #0966b4;
            background-color: #0966b4;
          }
          .circle-blank {
            @extend .circle;
            background-color: rgba(9, 102, 180, 0);
          }
          .right-line {
            width: 40px;
            height: 1px;
            background-color: #0966b4;
            flex: 1;
          }
        }
      }
      .time-line-item:first-child {
        .item-cell .bottom-line .left-line {
          background-color: rgba(9, 102, 180, 0) !important;
        }
      }
      .time-line-item:last-child {
        .item-cell .bottom-line .right-line {
          background-color: rgba(9, 102, 180, 0) !important;
        }
      }
    }
  }
  .honor-detail-con {
    width: 78%;
    align-self: flex-end;
    overflow-x: auto;
    scroll-behavior: smooth; /* 平滑滚动 */
    user-select: none; /* 防止选中文本 */
    -ms-overflow-style: none; /* IE 和 Edge */
    scrollbar-width: none; /* Firefox */
    .detail-list-con {
      transition: transform 0.3s ease-in-out;
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: center;
      .honor-detail {
        min-width: 350px;
        max-width: 400px;
        min-height: 400px;
        @extend .base-flex-column;
        justify-content: flex-start;
        align-items: flex-start;
        .detail-title {
          min-width: 67px;
          height: 42px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 600;
          font-size: 30px;
          color: #333333;
          line-height: 42px;
          text-align: left;
          @extend .base-text-ellipsis;
          margin-bottom: 19px;
        }
        .title-blue-line {
          width: 35px;
          height: 2px;
          background: #0966b4;
          border-radius: 1px;
        }
        .title-crosse-line {
          width: 100%;
          height: 2px;
          background: #8c98a7;
          margin-bottom: 20px;
        }
        .detail-content {
          width: 100%;
          min-height: 50px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #333333;
          line-height: 30px;
          text-align: left;
          padding-right: 20px;
          text-indent: 1em;
        }
        .detail-img-con {
          width: 100%;
          @extend .base-flex-column;
          justify-content: flex-start;
          align-items: flex-start;
          img {
            width: 160px;
            height: 160px;
            margin-bottom: 10px;
          }
        }
      }
    }
    .honor-detail-con::-webkit-scrollbar {
      display: none; /* Chrome, Safari 和 Opera */
    }
  }
}
</style>
