<template>
  <div class="memorabilia-con">
    <div class="title-con">
      <img class="btn" src="@/assets/images/home/<USER>" alt="" />
      <div class="title">{{data.title}}</div>
      <img class="btn" src="@/assets/images/home/<USER>" alt="" />
    </div>
    <div class="detail-con">
      <div class="detail-item" v-for="(item, index) in data.things" :key="index">
        <div class="detail-item-left">
          <div class="item-step">{{ item.title }}</div>
          <img class="step-img" src="@/assets/images/home/<USER>" alt="" />
          <div class="item-circle">
            <div class="point" />
          </div>
        </div>
        <div class="detail-item-right">
          {{item.content}}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="aboutUsMemorabilia">
const props = defineProps({
  componentData: {
    type: Object,
    default: {},
  },
});
const data = ref({});
onMounted(() => {
  data.value = props.componentData;
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.memorabilia-con {
  width: 100%;
  min-height: 500px;
  padding-top: 100px;
  // padding-bottom: 140px;
  @extend .base-flex-column;
  justify-content: center;
  align-items: center;
  .title-con {
    margin-bottom: 100px;
    @extend .base-flex-row;
    justify-content: center;
    align-items: flex-start;
    .btn {
      width: 200px;
    }
    .title {
      // width: 78px;
      height: 26px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 26px;
      color: #333333;
      line-height: 26px;
      text-align: center;
      margin-left: 20px;
      margin-right: 20px;
    }
  }
  .detail-con {
    width: 50%;
    @extend .base-flex-column;
    justify-content: flex-start;
    align-items: flex-start;
    .detail-item {
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: flex-start;
      .detail-item-left {
        width: 180px;
        border-right: 1px solid #c0c0c0;
        min-height: 200px;
        position: relative;
        margin-right: 34px;
        @extend .base-flex-row;
        justify-content: flex-end;
        align-items: flex-start;
        .item-step {
          width: 65px;
          height: 28px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 600;
          font-size: 20px;
          color: #0966b4;
          line-height: 28px;
          letter-spacing: 1px;
          text-align: right;
          z-index: 2;
        }
        .step-img {
          width: 44px;
          height: 16px;
          margin-top: 15px;
          margin-left: -40px;
          margin-right: 30px;
        }
        .item-circle {
          width: 22px;
          height: 22px;
          background: #daeeff;
          border-radius: 11px;
          margin-right: -11px;
          @extend .base-flex-row;
          justify-content: center;
          align-items: center;
          .point {
            width: 8px;
            height: 8px;
            background: #0966b4;
            border-radius: 4px;
          }
        }
      }
      .detail-item-right {
        width: 800px;
        min-height: 90px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        line-height: 30px;
        text-align: left;
      }
    }
    .detail-item:last-child {
      .detail-item-left {
        border-right: 0px !important;
      }
    }
  }
}
</style>
