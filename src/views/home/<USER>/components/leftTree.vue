<template>
  <el-tree
    class="tree-menu"
    ref="treeEl"
    node-key="label"
    :data="treeData"
    :props="defaultProps"
    @node-click="handleNodeClick"
    highlight-current
    :expand-on-click-node="false"
  >
    <!--
    :current-node-key="defaultNodeKey"
    :default-expanded-keys="expandedKeys"
    :default-checked-keys="checkedKeys"  -->
    <template #default="{ node, data }">
      <span class="custom-tree-node" :class="{ active: node.isCurrent }" @click="changeCollapseExpand(node)">
        <span>{{ node.label }}</span>
        <span class="tree-node-icons">
          <span v-if="node.childNodes.length">
            <el-icon v-if="!node.expanded">
              <ArrowRight />
            </el-icon>
            <el-icon v-else>
              <ArrowDown />
            </el-icon>
          </span>
        </span>
      </span>
    </template>
  </el-tree>
</template>

<script setup>
import { ArrowRight, ArrowDown } from '@element-plus/icons-vue';
import { ref } from 'vue';
import { listArticleTypeEducation } from '@/api/openApi/openApi';

const treeData = ref([]);
const defaultProps = { label: 'typeName', children: 'children' };

// 根据实际数据动态设置默认选中的节点
const defaultNodeKey = computed(() => {
  return treeData.value.length > 0 ? treeData.value[0].label : null;
});

// 根据实际数据动态设置默认展开的节点
const expandedKeys = computed(() => {
  return treeData.value.length > 0 ? [treeData.value[0].label] : [];
});

// 根据实际数据动态设置默认勾选的节点
const checkedKeys = computed(() => {
  return treeData.value.length > 0 ? [treeData.value[0].label] : [];
});

const emit = defineEmits(['node-click']);

function changeCollapseExpand(node) {
  if (node && node.expanded) {
    node.collapse();
  } else {
    node.expand();
  }
}
// 加载左侧树数据
onBeforeMount(async () => {
  try {
    const res = await listArticleTypeEducation();
    treeData.value = res.rows;
  } catch (error) {
    console.error('Error fetching article types:', error);
  }
});

const treeEl = ref(null);
const handleNodeClick = (data) => {
  emit('node-click', data);
};
</script>

<style lang="scss" scoped>
.custom-tree-node {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  padding-right: 8px;
}
.active {
  color: #409eff;
}
.tree-node-icons {
  display: inline-block;
  margin-right: 8px;
}
.tree-menu {
  width: 100%;
  min-height: 56px;
  line-height: 56px;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 18px;
  color: #000000;
  text-align: left;
  background: #ffffff;
  padding: 0px 24px 12px 18px;
}
:deep(.el-tree-node__content) {
  width: 100%;
  min-height: 56px;
  line-height: 56px;
  background: #ffffff;
  border-radius: 8px;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 18px;
  color: #000000;
  text-align: left;
}
:deep(.el-tree-node__expand-icon) {
  opacity: 0;
}
</style>
