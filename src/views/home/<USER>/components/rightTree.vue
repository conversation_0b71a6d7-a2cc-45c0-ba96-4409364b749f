<template>
  <div v-for="item in articleList">
    <span v-if="item.articleTitle" class="blue-dot"></span><span>{{ item.articleTitle }}</span>
    <!-- 富文本展示区域 -->
    <div class="editor-container">
      <div v-if="item.articleContentType === '1'" v-html="item.content"></div>
      <div v-if="item.articleContentType === '2'">
        <span style="color: blue; cursor: pointer" @click="previewFile(item.url)">
          {{ item.articleTitle }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Base64 } from 'js-base64';
import { listArticle } from '@/api/basic/article';
// 定义 props
const props = defineProps({
  selectedId: {
    type: Number,
    required: true,
  },
});
const queryParams = ref({
  pageNum: -1,
  pageSize: 10,
  typeId: null,
});
const articleList = ref('');
// 预览文件
const previewFile = (examineFileUrl) => {
  // 实现预览文件逻辑
  const url = examineFileUrl;
  const encodedUrl = Base64.encode(url);
  const previewUrl = import.meta.env.VITE_ONLINE_PREVIEW + encodeURIComponent(encodedUrl);
  window.open(previewUrl);
};
// 监控 selectedId 的变化
watch(
  () => props.selectedId,
  (newVal, oldVal) => {
    queryParams.value.typeId = newVal;
    listArticle(queryParams.value).then((response) => {
      articleList.value = response.rows.sort((a, b) => {
        return b.id - a.id;
      });
    });
  }
);
</script>

<style scoped>
.editor-container {
  border-radius: 4px;
  padding: 20px;
  background-color: #ffffff;
  font-size: 14px;
  line-height: 1.8;
  color: #333;
}

.editor-container h1 {
  margin-bottom: 10px;
}

.editor-container ul {
  padding-left: 20px;
}
.blue-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #409eff;
  border-radius: 50%;
  margin-right: 4px; /* 可选：设置圆点与文本之间的间距 */
}
</style>
