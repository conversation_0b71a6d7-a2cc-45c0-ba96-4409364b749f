

<template>
  <div class="head-bg-con" :style="{ backgroundImage: 'url(' + data.backgroundImage + ')' }">
    <div class="title">{{data.title1}}</div>
    <div class="title mab30">{{data.title2}}</div>
    <div class="sub-title">
        {{data.title3}}
    </div>
  </div>
</template>
<script setup name="aboutUsDesc">
const props = defineProps({
  componentData: {
    type: Object,
    default: {},
  },
});
const data = ref({});
onMounted(() => {
  data.value = props.componentData;
});
</script>
<style lang="scss" scoped>
@import "@/assets/styles/index.scss";
.head-bg-con {
  width: 100%;
  height: 440px;
  background-size: cover;
  @extend .base-flex-column;
  justify-content: center;
  align-items: center;
  .title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 50px;
    color: #ffffff;
    line-height: 70px;
    letter-spacing: 3px;
    text-align: center;
  }
  .mab30 {
    margin-bottom: 30px;
  }
  .sub-title {
    width: 624px;
    height: 56px;
    font-family: <PERSON>FangSC, PingFang SC;
    font-weight: 400;
    font-size: 20px;
    color: #eaf3ff;
    line-height: 28px;
    letter-spacing: 1px;
    text-align: center;
  }
}
</style>