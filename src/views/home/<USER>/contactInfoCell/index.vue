<template>
  <div class="contact-info-cell">
    <div class="cell-left">
      <div class="img-con">
        <img src="@/assets/images/home/<USER>" alt="" />
      </div>
    </div>
    <div class="cell-right">
      <div class="cell-right-title">专业：{{ item.specialityName }}</div>
      <div class="contact-info">邮箱：{{ item.emailAdress }}</div>
    </div>
  </div>
</template>

<script setup name="ContactInfoCell">
const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';

.contact-info-cell {
  width: 452px;
  height: 102px;
  background: #f5f6f9;
  border-radius: 8px;
  margin-right: 22px;
  margin-bottom: 22px;
  @extend .base-flex-row;
  justify-content: center;
  align-items: center;
  .cell-left {
    width: 56px;
    height: 56px;
    margin-right: 23px;
    margin-left: 23px;
    .img-con {
      width: 100%;
      height: 100%;
      background: #0966b4;
      border-radius: 28px;
      @extend .base-flex-row;
      justify-content: center;
      align-items: center;
      img {
        width: 38px;
        height: 38px;
      }
    }
  }
  .cell-right {
    flex: 1;
    @extend .base-flex-column;
    justify-content: center;
    align-items: flex-start;
    .cell-right-title {
      width: 100%;
      min-height: 28px;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: bold;
      font-size: 16px;
      color: #0966b4;
      line-height: 28px;
      text-align: left;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 4px;
    }
    .contact-info {
      height: 28px;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      line-height: 28px;
      text-align: left;
      webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
