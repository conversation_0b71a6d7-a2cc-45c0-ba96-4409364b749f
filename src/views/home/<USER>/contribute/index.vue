<template>
  <div class="contribute-container-sty">
    <el-row>
      <el-col :span="21" style="text-align: end; margin: 20px">
        <el-button type="primary" plain @click="downloadTemplate">下载自荐表模板</el-button>
      </el-col>
    </el-row>
    <el-row :span="24">
      <el-col :span="16" :offset="5">
        <el-row :span="24">
          <el-col :span="12" v-for="item in items" :key="item.id" style="margin-bottom: 30px">
            <el-card style="max-width: 480px">
              <div class="list-container">
                <el-divider direction="vertical" />
                <div class="text-container">
                  <p style="color: #1059a5; font-weight: 600">专业：{{ item.major }}</p>
                  <p>邮箱：{{ item.email }}</p>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
const items = ref([]);

const fetchMockData = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([
        { id: 1, major: '艺术管理', email: '<EMAIL>' },
        { id: 2, major: '音乐表演', email: '<EMAIL>' },
        { id: 3, major: '舞蹈编导', email: '<EMAIL>' },
        { id: 4, major: '戏剧影视文学', email: '<EMAIL>' },
      ]);
    }, 1000);
  });
};

onMounted(async () => {
  items.value = await fetchMockData();
});

const toLogin = () => {
  // 登录逻辑
};

const downloadTemplate = () => {
  // 下载模板逻辑
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';

.list-container {
  display: flex;
  align-items: flex-start;
  /* 垂直对齐方式 */
}

.vertical-divider {
  height: 100%;
  /* 分割线高度 */
  margin-right: 10px;
  /* 分割线与文本之间的间距 */
}

.text-container {
  flex-direction: column;
  /* 文本上下排列 */
}

.el-divider--vertical {
  border-left: 5px #1059a5 var(--el-border-style);
  display: inline-block;
  height: 5em;
  margin: 8px 12px;
  position: relative;
  vertical-align: middle;
  width: 1px;
}
</style>
