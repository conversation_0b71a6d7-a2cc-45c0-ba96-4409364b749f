<template>
  <div class="affix-con">
    <el-affix :offset="120">
      <template #default>
        <div class="content-item-con">
          <div class="content-item" v-for="(item, index) in optionList" :key="index" @sendMessage="clickItem(item.val)" @click="clickItem(item.val)">
            <img class="item-img" :src="item.icon" alt="" />
            <span class="item-label">{{ item.label }}</span>
            <div class="bottom-line" v-if="index !== optionList.length - 1"></div>
          </div>
        </div>
        <Feedback  v-model="isFeedbackVisible" />
      </template>
    </el-affix>
  </div>
</template>

<script setup name="CustomerServiceBar">
import { ref } from 'vue';
import customerServiceIcon from '@/assets/images/home/<USER>';
import feedbackIcon from '@/assets/images/home/<USER>';
import instructionsIcon from '@/assets/images/home/<USER>';
import Feedback from '@/components/Feedback/index.vue';
const optionList = ref([
  {
    label: '联系客服',
    icon: customerServiceIcon,
    val: 1,
  },
  {
    label: '意见反馈',
    icon: feedbackIcon,
    val: 2,
  },
  {
    label: '使用说明',
    icon: instructionsIcon,
    val: 3,
  },
]);
const isFeedbackVisible = ref(false);
const emit = defineEmits(['sendMessage'])
const { proxy } = getCurrentInstance();
const clickItem = (val) => {
  switch (val) {
    case 1:
      isChatBoxVisible.value = !isChatBoxVisible.value;
      emit('sendMessage', isChatBoxVisible.value);
      break;
    case 2:
      isFeedbackVisible.value = true;
      break;
    case 3:
      proxy.$router.push({ path: '/site-instructions' });
      break;
    default:
      break;
  }
};
const isChatBoxVisible = ref(false);
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index';
.affix-con {
  width: 96px;
  position: fixed;
  top: 350px;
  right: 30px;
  z-index: 999;
  .content-item-con {
    width: 100%;
    height: 320px;
    background: linear-gradient(180deg, #f2f8f9 0%, #ffffff 100%);
    box-shadow: 0px 4px 12px 0px rgba(72, 107, 136, 0.13);
    border-radius: 8px;
    border: 2px solid #ffffff;
    @extend .base-flex-column;
    justify-content: center;
    align-items: center;
    .content-item {
      cursor: pointer; /* 添加鼠标悬停效果 */
      @extend .base-flex-column;
      justify-content: center;
      align-items: center;
      .item-img {
        width: 33px;
        height: 30px;
        border-radius: 4px;
        margin-bottom: 12px;
      }
      .item-label {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #98a8ab;
        text-shadow: 0px 4px 12px rgba(72, 107, 136, 0.13);
        text-align: justify;
        font-style: normal;
        margin-bottom: 21px;
      }
      .item-label:last-child {
        margin-bottom: 0;
      }
      .bottom-line {
        width: 84px;
        height: 1px;
        box-shadow: 0px 4px 12px 0px rgba(72, 107, 136, 0.13);
        border: 1px solid #d6e3e7;
        opacity: 0.63;
        margin-bottom: 21px;
      }
    }
  }
}

</style>
