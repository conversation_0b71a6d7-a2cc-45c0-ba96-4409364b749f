<template>
  <div class="foot-con">
    <div class="foot-top">
      <div class="foot-top-item" v-for="(item, index) in footerRoute" :key="index" @click="jumpLink(item.route)">
        {{ item.label }}
      </div>
    </div>
    <div class="foot-center">
      <span
        >友情链接：
        <span v-for="(item, index) in friends" :key="index" @click="jumpFriend(item.linkUrl)">{{ ' | ' + item.linkLabel }}</span>
      </span>
    </div>
    <div class="foot-end">
      <div>Copyright {{ siteInfo?.copyRight ?? '' }} &nbsp; ICP:</div>
      <el-link href="https://beian.miit.gov.cn/" target="_blank">{{ siteInfo?.icp ?? '' }}</el-link>
    </div>
    <el-backtop :right="100" :bottom="100" />
  </div>
</template>

<script setup name="footComp">
import { getFriends, getSiteInfoById } from '@/api/basic/home.js';
import { SiteId } from '@/utils/constant';
import useSiteStore from '@/store/modules/site';

const siteInfo = computed(() => useSiteStore().siteInfo);
const footerRoute = ref([]);
/*nextTick(() => {
  if (siteInfo.value?.footerRoute) {
   footerRoute.value = JSON.parse(siteInfo.value.footerRoute);
  }
});*/
const { proxy } = getCurrentInstance();
const friends = ref([]);

/*watch(
  () => siteInfo.value?.footerRoute,
  () => {
    initFooterRoute();
  },
  { immediate: true, deep: true }
);*/

function initFooterRoute() {
  /*if (siteInfo.value?.footerRoute) {
    footerRoute.value = JSON.parse(siteInfo.value.footerRoute);
  }*/
  getSiteInfoById(SiteId)
    .then((res) => {
      // this.siteInfo = res.data;
      if (res.data && res.data.footerRoute) {
        footerRoute.value = JSON.parse(siteInfo.value.footerRoute);
      }
    })
    .catch((error) => {});
}

function jumpLink(route) {
  proxy.$router.push({ path: route });
}

function jumpFriend(url) {
  if (url.indexOf('http') < 0) {
    url = 'http://' + url;
  }
  window.open(url, '_blank');
}

function doGetFriendLink() {
  getFriends()
    .then((res) => {
      if (res.code == 200) {
        friends.value = res.data;
        // sessionStorage.setItem(FRIEND_KEY, JSON.stringify(res.data));
      }
    })
    .catch((error) => {});
}

onMounted(() => {
  doGetFriendLink();
  initFooterRoute();
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index';

.foot-con {
  width: 100%;
  height: 220px;
  background-color: #2d363e;
  /*
  background-image: radial-gradient(rgba(159, 156, 156, 0.2) 1px, transparent 1px); !* 使用渐变创建白点 *!
  background-size: 10px 10px; !* 调整白点的间距 *!
  */
  @extend .base-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .foot-top {
    @extend .base-flex;
    justify-content: center;
    align-items: center;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 10px;
    margin-bottom: 30px;

    .foot-top-item {
      margin-right: 50px;
      cursor: pointer;
    }
  }

  .foot-center {
    height: 14px;
    cursor: pointer;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 14px;
    margin-bottom: 30px;
  }

  .foot-end {
    height: 14px;
    display: flex;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #a8a8a8;
    line-height: 14px;
  }
}
</style>
