<template>
  <div class="head-nav-con" :class="{ 'blue-bg': showBackground }">
    <div class="head-nav">
      <div class="head-nav-con-left">
        <template v-if="siteInfo?.logoUrl || siteInfo?.blueLogoUrl">
          <el-image class="logo" referrerpolicy="referrer" :src="siteInfo?.logoUrl" alt=" " v-if="!isLoginPage"  @click="router.push({ path:'/' })"/>
          <el-image class="logo" referrerpolicy="referrer" :src="siteInfo?.blueLogoUrl" alt=" " v-else   @click="router.push({ path: '/' })"/>
        </template>
      </div>
      <div class="head-nav-con-right">
        <div class="nav-right-menu">
          <template v-for="(item, index) in menus" :key="index">
            <div
              :class="[{ 'menu-item': !isLoginPage }, { 'menu-item-ln': isLoginPage }, { active: index === activeMenuIndex }]"
              @click="handleSelect(item, index)"
              v-if="isShowItem(item)"
            >
              <el-badge :is-dot="true" :hidden="index !== 6">
                <span class="label"> {{ item.menuName }} </span>
              </el-badge>
            </div>
          </template>
        </div>
        <div class="nav-right-name">
          <template v-if="getToken()">
            <div class="avatar-container">
              <el-dropdown @command="handleCommand" trigger="click">
                <div style="display: flex; align-items: center">
                  <img class="avg" :src="avatarUrl ? avatarUrl : profile" alt="Logo" referrerpolicy="referrer" />
                  <span class="name">{{ userName }}</span>
                  <el-icon :style="{ color: sideTheme === 'theme-dark' ? '#fff' : '' }">
                    <caret-bottom />
                  </el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="routerLink">个人中心</el-dropdown-item>
                    <el-dropdown-item divided command="logout">
                      <span>退出登录</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
          <template v-else>
            <span class="span_login" :class="{ 'ln-sty': isLoginPage }" @click="toLogin">
              <span>登录/注册</span>
            </span>
          </template>
          <!--招标结束，删除-->
          <!-- <span class="text_carsi" @click="toCarsi">carsi</span> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="headNavComp">
import useUserStore from '@/store/modules/user.js';
import useSiteStore from '@/store/modules/site.js';
import useSettingsStore from '@/store/modules/settings.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, onUpdated, ref, watch } from 'vue';
//
import { getToken } from '@/utils/auth';
import { useRouter } from 'vue-router';
import profile from '@/assets/images/home/<USER>';
import { getSiteInfoById } from '@/api/basic/home.js';
import { SiteId } from '@/utils/constant';
const router = useRouter();
const userStore = useUserStore();
const menus = computed(() => useSiteStore().menus);
const siteInfo = ref(null);
let token = computed(() => getToken());
const userName = ref('');
const userInfo = ref({});
if (getToken()) {
  userInfo.value = userStore.getInfo();
}
const avatarUrl = ref('');
function saveVisitLog() {
  addVisitLog()
    .then((res) => {})
    .catch((error) => {});
}

const settingsStore = useSettingsStore();
const { proxy } = getCurrentInstance();
const sideTheme = computed(() => settingsStore.sideTheme);
const activeMenuIndex = ref(-1);
const props = defineProps({
  showBackground: {
    type: Boolean,
    default: false,
  },
  isLoginPage: {
    type: Boolean,
    default: false,
  },
});
const isShowItem = (item) => {
  return item.key !== 7 || (userName.value && item.key === 7);
};
const handleSelect = (item, index) => {
  activeMenuIndex.value = index;
  // 内部链接
  if (item.routeType == 1) {
    if (item.menuRoute || item.menuRoute === 0) {
      proxy.$router.push({ path: item.menuRoute });
    } else {
      proxy.$router.push({ path: '/user' });
    }
    // 外部链接
  } else {
    let url = item.menuRoute;
    if (url.indexOf('http') == -1) {
      url = 'http://' + item.menuRoute;
    }
    window.open(url, '_blank');
  }
};
const toLogin = () => {
  proxy.$emit('toLogin');
};
// 招标结束，删除
// const toCarsi = () => {
//   window.open('https://www.carsi.edu.cn/', '_blank');
// };

const routerLink = () => {
  if (getToken()) {
    router.push({ path: `/basic-information` });
  }
};

function logout() {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      userStore.logOut().then(() => {
        location.href = '/index';
      });
    })
    .catch(() => {});
}

function handleCommand(command) {
  switch (command) {
    case 'setLayout':
      setLayout();
      break;
    case 'logout':
      logout();
      break;
    default:
      break;
  }
}

const emits = defineEmits(['setLayout']);

function setLayout() {
  emits('setLayout');
}

function checkTokenNotExpired() {
  try {
    userStore
      .getInfo()
      .then((response) => {
        if (response.code !== 200) {
          ElMessage.error(response.msg);
          setTimeout(() => {
            userStore.logOut().then(() => {
              location.href = '/index';
            });
          }, 500);
        }
      })
      .catch(() => {
        // return false;
      });
  } catch (e) {
    // return false;
  }
}
async function initUserInfo() {
  await userStore.getInfo().then((response) => {
    if (response.code === 200) {
      userName.value = response?.user?.nickName;
      avatarUrl.value = response?.user?.avatar;
    } else {
      userName.value = "";
    }
  });
}

function checkLoginTimeOut() {
  if (token.value) {
    checkTokenNotExpired();
  }
}
onMounted(async () => {
  initUserInfo();
  checkLoginTimeOut();
  await getSiteInfoById(SiteId).then((res) => {
    if (res.code === 200) {
    }
    siteInfo.value = res.data;
  });
  console.log('siteInfo.value ', siteInfo.value);
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index';

.head-nav-con {
  width: 100%;
  height: 68px;
  padding-top: 10px;
  padding-bottom: 10px;
  @extend .base-flex;
  justify-content: center;
  align-items: center;

  .head-nav {
    width: 1400px;
    height: 48px;
    padding: 0 10px;
    @extend .base-flex;
    justify-content: space-between;
    align-items: center;

    .head-nav-con-left {
      .logo {
        width: 175px;
        height: 54px;
      }
    }

    .head-nav-con-right {
      @extend .base-flex;
      justify-content: space-between;
      align-items: center;

      .nav-right-menu {
        height: 48px;
        margin-right: 60px;
        @extend .base-flex;
        justify-content: space-between;
        align-items: center;

        .menu-item {
          height: 30px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          text-align: justify;
          font-style: normal;
          padding: 0 12px;
          cursor: pointer;
          @extend .base-flex;
          justify-content: center;
          align-items: center;
        }

        .menu-item:hover {
          opacity: 0.7;
        }

        .menu-item-ln {
          height: 30px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #333333;
          text-align: justify;
          font-style: normal;
          padding: 0 12px;
          cursor: pointer;
          @extend .base-flex;
          justify-content: center;
          align-items: center;
        }

        .menu-item-ln:hover {
          opacity: 0.7;
        }

        .active {
          border-bottom: 1px solid white;
        }
      }

      .nav-right-name {
        @extend .base-flex;
        justify-content: space-between;
        align-items: center;

        .avg {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          margin-right: 8px;
        }

        .name {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          font-style: normal;
        }
      }
    }
  }
}

.blue-bg {
  background: #0966b4;
}

.span_login {
  height: 17px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
  font-style: normal;
  line-height: 17px;
  cursor: pointer;
  @extend .base-flex;
  justify-content: center;
  align-items: center;
}

.span_login:hover {
  opacity: 0.7;
}

.ln-sty {
  color: #0966b4;
}

.text_carsi {
  font-size: 20px;
  font-weight: bold;
  margin: 0px 0px 0px 10px;
  color: rgb(0 139 255);
  cursor: pointer;
}

:deep(.el-image__placeholder) {
  background-color: transparent;
  background-image: none;
}
</style>
