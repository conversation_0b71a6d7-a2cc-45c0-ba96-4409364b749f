<template>
  <div class="page-nav-container">
    <div class="search-con">
      <searchBar @search="doSearch" :isNoShadow="true" />
    </div>
    <!-- 01 特殊教材tab(国家优秀教材， -->
    <div class="special-book-tab-con">
      <specialSubjectBookTab @scrollToAnchor="doScrollToAnchor" :selfAnchorChain="true" />
    </div>
    <!-- 专题图书列表  -->
    <div class="subject-book-list-con">
      <div class="list-head">
        <img class="list-head-img" src="@/assets/images/home/<USER>" alt="" />
        <span class="list-head-title">规划教材与优秀教材专区</span>
        <img class="list-head-img" src="@/assets/images/home/<USER>" alt="" />
      </div>
      <!--  01  -->
      <div class="subject-book-list-card" v-for="item in itemList" :id="`part-${item.areaId}`">
        <div class="list-card-head">
          <img class="card-head-img" src="@/assets/images/home/<USER>" alt="" />
          <div class="card-head-title">{{ item.areaName }}</div>
          <img class="card-head-img" src="@/assets/images/home/<USER>" alt="" />
        </div>
        <div class="card-book-list-con">
          <searchContentCardList :itemList="item.dtbBookList" />
        </div>
      </div>

      <!--      -->
    </div>
  </div>
</template>

<script setup name="SpecialSubjectBooksList">
import searchBar from '@/views/home/<USER>/SearchBar/index.vue';
import specialSubjectBookTab from '@/views/search/components/specialSubjectBookTab/index.vue';
import searchContentCardList from '@/views/search/components/searchContentCardList/index.vue';
import { bookAreaList } from '@/api/basic/bookArea';
const router = useRouter();
const itemList = ref([]);

function doScrollToAnchor(typeVal) {
  const elementId = 'part-' + typeVal;
  const targetElement = document.getElementById(elementId);
  if (targetElement) {
    targetElement.scrollIntoView({ behavior: 'smooth' });
  }
}
const doSearch = (queryCriteria) => {
};
const getInFo = () => {
  bookAreaList({}).then((res) => {
    itemList.value = res.data;
  });
};
onMounted(() => {
  // 获取数据
  getInFo()
});
</script>

<style scoped lang="scss">
@import '@/assets/styles/index.scss';
.page-nav-container {
  width: 100%;
  .search-con {
    width: 100%;
  }
  .special-book-tab-con {
    width: 100%;
    margin-bottom: 58px;
  }
  .subject-book-list-con {
    width: 100%;
    margin-bottom: 13px;
    @extend .base-flex-column;
    justify-content: flex-start;
    align-items: center;

    .list-head {
      width: 100%;
      margin-bottom: 60px;
      @extend .base-flex-row;
      justify-content: center;
      align-items: center;
      .list-head-img {
        width: 363px;
        height: 31px;
      }
      .list-head-title {
        margin-left: 33px;
        margin-right: 33px;
        min-width: 352px;
        height: 46px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 32px;
        color: #0966b4;
        line-height: 46px;
        text-align: center;
      }
    }

    .subject-book-list-card {
      width: 75%;
      @extend .base-flex-column;
      justify-content: flex-start;
      align-items: center;
      .list-card-head {
        width: 100%;
        margin-bottom: 56px;
        @extend .base-flex-row;
        justify-content: center;
        align-items: center;
        .card-head-img {
          width: 41px;
          height: 35px;
        }
        .card-head-title {
          margin-left: 22px;
          margin-right: 22px;
          min-width: 200px;
          height: 46px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 32px;
          color: #333333;
          line-height: 46px;
          text-align: center;
        }
      }

      .card-book-list-con {
        width: 100%;
      }
    }
  }
}
</style>
