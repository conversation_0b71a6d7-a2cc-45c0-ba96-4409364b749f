<template>
  <div class="comp-con">
    <div class="instructions-con">
      <div class="top-head">
        <div class="top-head-left">
          <img class="title-img" src="@/assets/images/instructions/instructions-title-img-icon.png" alt="" />
          <span class="title-text">使用说明</span>
        </div>
        <div class="top-head-right">
          <div class="down-load-btn" @click="doGetInstructions">下载使用说明</div>
        </div>
      </div>
      <div class="instructions-content">
        <div class="instructions-content-left">
          <div class="instructions-content-left-title">产品功能介绍</div>
          <div class="instructions-content-menu-list">
            <LeftTree @node-click="handleLeftTreeClick" />
          </div>
        </div>
        <div class="instructions-content-right">
          <div class="instructions-content-html">
            <h3>{{ title }}</h3>
            <RightTree :selectedId="selectedId" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Instructions">
import { siteInstructionsFileName } from '@/api/constant.js';
import { listArticleTypeEducation } from '@/api/openApi/openApi';
import LeftTree from './components/leftTree.vue';
import RightTree from './components/rightTree.vue';
import { nextTick, ref, onBeforeMount } from 'vue';
import { ElMessageBox } from 'element-plus'
import { getToken } from '@/utils/auth';
const { proxy } = getCurrentInstance();

const selectedId = ref(null);
const title = ref('');

function handleLeftTreeClick(data) {
  nextTick(() => {
    selectedId.value = data.typeId;
  });
  title.value = data.typeName;
}

const menuList = ref([]);
const currentItem = ref({});
let currentActiveIndex = ref(-1); // 一级
let currentChActiveIndex = ref(-1); // 二级

onMounted(async () => {
  // 判断是否有token，没有token的时候去登录
  if (!getToken()) {
    ElMessageBox.alert('请您先进行登录', '提示', {
      confirmButtonText: '确定',
      callback: () => {
        proxy.$router.push({ path: "/login" });
      },
    });
  } else {
    const res = await listArticleTypeEducation();
    menuList.value = res.rows;
    // currentItem.value = menuList.value[0];
  }
});
function changeCurrentItemIndex(index, chIndex) {
  currentActiveIndex.value = index;
  currentChActiveIndex.value = chIndex;
}

function doGetInstructions() {
  const fileName = siteInstructionsFileName.value;
  const encodedFileName = encodeURIComponent(fileName);
  const fileUrl = `/${encodedFileName}`;
  try {
    // 创建下载链接
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    // 将链接添加到页面中
    document.body.appendChild(link);
    // 触发下载
    link.click();
    // 移除链接，清理DOM
    document.body.removeChild(link);
  } catch (error) {
    console.error('下载文件时出错:', error);
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';

.comp-con {
  width: 100%;
  padding-top: 9px;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
  .instructions-con {
    @extend .base-comp-card-per;
    @extend .base-flex-column;
    justify-content: flex-start;
    aligin-items: center;
    .top-head {
      border-bottom: 1px solid #e5e6e7;

      padding-bottom: 13px;
      @extend .base-flex-row;
      justify-content: space-between;
      align-items: center;
      .top-head-left {
        @extend .base-flex-row;
        justify-content: flex-start;
        align-items: center;
        .title-img {
          width: 24px;
          height: 24px;
          margin-right: 12px;
        }
        .title-text {
          width: 88px;
          height: 36px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: bold;
          font-size: 22px;
          color: #333333;
          line-height: 36px;
          text-align: left;
        }
      }
      .top-head-right {
        .down-load-btn {
          width: 130px;
          height: 44px;
          background: #ffffff;
          border-radius: 8px 8px 8px 8px;
          border: 1px solid #0966b4;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #0966b4;
          line-height: 44px;
          text-align: center;
        }
        .down-load-btn:hover {
          color: #ffffff;
          background: #0966b4;
          cursor: pointer;
          transition: all 0.3s;
        }
      }
    }
    .instructions-content {
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: stretch;
      .instructions-content-left {
        width: 33.3%;
        min-height: calc(100vh - 300px);
        padding-top: 22px;
        border-right: 1px solid #e5e6e7;
        .instructions-content-left-title {
          max-width: 100%;
          height: 36px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: bold;
          font-size: 20px;
          color: #333333;
          line-height: 36px;
          text-align: left;
          margin-bottom: 26px;
        }
        .instructions-content-menu-list {
          width: 100%;
          padding-left: 0px;
          padding-right: 19px;
          @extend .base-flex-column;
          justify-content: flex-start;
          align-items: flex-start;
          .menu-item {
            width: 100%;
            .menu-item-title {
              width: 100%;
              min-height: 28px;
              line-height: 28px;
              background: #ffffff;
              border-radius: 8px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 400;
              color: #000000;
              text-align: left;
              padding: 12px 24px;
            }
            .ch-menu-item {
              width: 100%;
              padding-left: 24px;
              .ch-menu-item-title {
                width: 100%;
                min-height: 56px;
                background: #ffffff;
                border-radius: 8px;
                font-family:
                  PingFang SC,
                  PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #000000;
                line-height: 28px;
                text-align: left;
                padding: 12px 24px;
                margin-bottom: 2px;
              }
              .active {
                background: #f5f8ff;
                color: #598dc6;
              }
            }
            .active {
              background: #f5f8ff;
              color: #598dc6;
            }
          }
        }
      }
      .instructions-content-right {
        flex: 1;
        height: 100%;
        padding-top: 34px;
        padding-left: 42px;
      }
    }
  }
}
</style>
