<template>
  <div class="comp-container">
    <div class="download-btn-con">
      <div class="download-btn" @click="toDownload">下载自荐表模板</div>
    </div>
    <div class="contact-info-list-con">
      <contactInfoCell :item="item" v-for="item in itemList" />
    </div>
  </div>
</template>

<script setup name="ContactSubmitArticle">
import contactInfoCell from '@/views/home/<USER>/contactInfoCell/index.vue';
import { listEmail } from "@/api/openApi/openApi";
import { onMounted } from 'vue';
const itemList = ref([]);

const fetchData = () => {
  listEmail({
    page: 1,
    limit: 99999
  }).then((res) => {
    itemList.value = res.rows?.sort((a, b) => a.sort - b.sort);
  });
};

onMounted(async () => {
  itemList.value = await fetchData();
});
function toDownload() {
  const fileName = '大连理工大学出版社·教材编写自荐表（空）.doc';
  const encodedFileName = encodeURIComponent(fileName);
  const fileUrl = `/${encodedFileName}`;
  const link = document.createElement('a');
  link.href = fileUrl;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index';
.comp-container {
  width: 100%;
  height: calc(100vh - 100px);
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
  padding-top: 32px;
  .download-btn-con {
    @extend .base-comp-card-per;
    margin-bottom: 29px;
    @extend .base-flex-row;
    justify-content: flex-end;
    .download-btn {
      width: 158px;
      height: 44px;
      background: #0966b4;
      border-radius: 8px;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 44px;
      text-align: center;
      cursor: pointer;
    }
    .download-btn:hover {
      opacity: 0.8;
    }
  }
  .contact-info-list-con {
    @extend .base-comp-card-per;
    @extend .base-flex-row;
    width: 50%;
    justify-content: start;
    align-items: center;
    flex-wrap: wrap;
  }
}
</style>
