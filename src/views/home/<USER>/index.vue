<template>
  <div class="home-container-sty">
    <!-- 头部导航栏 -->
    <headNavComp @toLogin="toLogin" :showBackground="true" />
    <!-- 主体内容 -->
    <div class="content-body-con">
      <div class="content-body">
        <!-- 通过 router-view 动态加载组件内容 -->
        <router-view />
      </div>
    </div>
    <footComp />
  </div>
</template>
<script setup name="HomePage">
import headNavComp from '@/views/home/<USER>/headNavComp/index.vue';
import footComp from '@/views/home/<USER>/footComp/index.vue';
const toLogin = () => {
  proxy.$router.push({ path: '/login' });
};
</script>

<style scoped lang="scss">
@import '@/assets/styles/index.scss';

.list-item {
  padding: 10px;
  margin: 5px 0;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  cursor: move;
  /* 确保鼠标指针显示为拖动图标 */
}

.home-container-sty {
  width: 100%;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;

  .content-body-con {
    @extend .base-comp-card;
    margin-bottom: 23px;
    min-height: 705px;

    .content-title {
      width: 100%;
      margin-top: 25px;
      margin-bottom: 25px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 18px;
      color: #333333;
      line-height: 25px;
      text-align: right;
      font-style: normal;
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: center;
    }

    .content-body {
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: flex-start;

      .content-left-con {
        @extend .base-flex-column;
        justify-content: flex-start;
        align-items: flex-start;

        .left-top-menu-con {
          width: 191px;
          height: 450px;
          background: #d4eeff;
          border-radius: 10px;
          @extend .base-flex-column;
          justify-content: flex-start;
          align-items: flex-start;
          margin-bottom: 5px;
        }

        .left-middle-menu-con {
          width: 191px;
          height: 130px;
          background: #d1f3db;
          border-radius: 20px;
          @extend .base-flex-column;
          justify-content: space-around;
          align-items: flex-start;
          margin-bottom: 5px;
        }

        .left-bottom-menu-con {
          width: 191px;
          height: 45px;
          background: #FFEEC6;
          border-radius: 20px;
          @extend .base-flex-column;
          justify-content: space-around;
          align-items: flex-start;
          margin-bottom: 5px;
        }

        .menu-item {
          width: 100px;
          height: 16px;
          margin-left: 29px;
          margin-bottom: 33px;
          @extend .base-flex-row;
          justify-content: flex-start;
          align-items: center;

          .menu-icon {
            width: 16px;
            height: 16px;
            margin-right: 14px;
          }

          span {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            line-height: 50px;
            text-align: left;
            font-style: normal;
          }
        }

        .menu-item:nth-child(1) {
          margin-top: 30px;
        }
      }

      .content-right-con {
        width: 1239px;
        min-height: 700px;
        border-radius: 20px;
        background: #ffffff;
        box-shadow: 0px 0px 20px 0px rgba(182, 182, 182, 1.0);;
        margin-left: -70px;

        .right-top-con {
          width: 100%;
          border-bottom: 1px solid #e5e6e7;
          margin-bottom: 10px;
          @extend .base-flex-row;
          justify-content: space-between;
          align-items: center;

          .menu-con {
            margin-left: 57px;

            .el-menu-demo {
              width: 600px;
              border-bottom: none !important;
            }
          }

          .filter-con {
            margin-right: 61px;
          }
        }

        .right-bottom-con {
          width: 100%;
          height: 580px;
          overflow-x: hidden;

          .book-list-con {
            margin-left: 10px;
            margin-top: 27px;
            width: 100%;
            @extend .base-flex-row;
            justify-content: space-between;
            align-items: flex-start;
            flex-wrap: wrap;

            .book-item {
              width: 216px;
              height: 259px;
              background: #f7f7f7;
              border-radius: 8px;
              margin-right: 19px;
              margin-bottom: 20px;
              @extend .base-flex-row;
              justify-content: center;
              align-items: center;

              .book-item-cell {
                @extend .base-flex-column;

                img {
                  width: 120px;
                  height: 176px;
                  margin-bottom: 5px;
                }

                span {
                  font-family:
                    PingFangSC,
                    PingFang SC;
                  font-weight: 500;
                  font-size: 16px;
                  color: #333333;
                  line-height: 50px;
                  text-align: center;
                  font-style: normal;
                }
              }
            }
          }
        }
      }
    }
  }
}

.el-l-menu {
  background-color: var(--el-menu-bg-color);
  border-right: 1px solid var(--el-menu-border-color);
  box-sizing: border-box;
  list-style: none;
  margin: 0;
  padding-left: 0;
  position: initial;
}
</style>
