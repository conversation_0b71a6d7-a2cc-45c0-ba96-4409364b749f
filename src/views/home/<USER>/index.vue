<template>
  <div class="about-us-page-con">
    <div class="page-nav-container">
      <div class="nav-con">
        <headNavComp @toLogin="toLogin" :showBackground="false" />
      </div>
    </div>
    <div class="page-content-con">
       <!-- 动态组件加载 -->
    <div class="auto-com-con" v-for="(item, index) in pageDataList" :key="index">
      <component :is="componentMap[item.componentKey]" v-if="componentMap[item.componentKey]" :componentData="item.componentData" />
    </div>
    </div>
    <!-- 底部   -->
    <div class="foot-con">
      <footComp />
    </div>
  </div>
</template>

<script setup name="SearchPage">
import aboutUsDesc from '@/views/home/<USER>/components/aboutUsDesc.vue';
import aboutUsHonor from '@/views/home/<USER>/components/aboutUsHonor.vue';
import aboutUsMemorabilia from '@/views/home/<USER>/components/aboutUsMemorabilia.vue';
import topImage from '@/views/home/<USER>/components/topImage.vue';
import footComp from '@/views/home/<USER>/footComp/index.vue';
import headNavComp from '@/views/home/<USER>/headNavComp/index.vue';

import banner from "@/views/home/<USER>/Banner/index.vue";
import searchBar from "@/views/home/<USER>/SearchBar/index.vue";
import publishersNews from "@/views/home/<USER>/PublishersNews/index.vue";
import recommendedTextbooks from "@/views/home/<USER>/RecommendedTextbooks/index.vue";
import provincialRecommendedTextbooks from "@/views/home/<USER>/ProvincialRecommendedTextbooks/index.vue";
import downloadCenter from "@/views/home/<USER>/DownloadCenter/index.vue";
import partnersSchool from "@/views/home/<USER>/PartnersSchool/index.vue";
import customerServiceBar from "@/views/home/<USER>/customerServiceBar/index.vue";
import chatBox from "@/views/home/<USER>/chatBox/index.vue";
import PasswordChangePrompt from "@/components/PasswordChangePrompt/index.vue";

import { useRouter } from 'vue-router';
import { getPageCmsComponents } from '@/api/cmc/cmc';
const componentMap = {
  'introduce': aboutUsDesc,
  'honor': aboutUsHonor,
  'big-thing': aboutUsMemorabilia,
  'top-img': topImage,
  "publisher-recommend": publishersNews,
  "book-recommend": recommendedTextbooks,
  "book-type-recommend": provincialRecommendedTextbooks,
  "download-center": downloadCenter
};
const data = ref({topData: {title1:'', title2: '', title3:''}});
const pageDataList = ref([]);
onMounted(() => {
  let _pageId = 2;
  getPageCmsComponents(_pageId).then((reps) => {
    if (reps.code !== 200 || !reps.data) {
      return;
    }
    initPageDataList(reps);
  });
});
function initPageDataList(reps) {
  pageDataList.value = reps.data;
  if (pageDataList.value && pageDataList.value.length > 0) {
    pageDataList.value.forEach((item) => {
      try {
        if (item.componentData && item.componentData.length > 0) {
          item.componentData = JSON.parse(item.componentData);
        }
      } catch (e) {
        item.componentData = {};
      }
    });
  }
}
const router = useRouter();
function toLogin() {
  router.push({ path: '/login' });
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/index.scss';
.about-us-page-con {
  width: 100%;
  position: relative;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
  .page-nav-container {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    .nav-con {
      width: 100%;
    }
  }
  .page-content-con {
    width: 100%;
    min-height: calc(100vh - 218px);
    @extend .base-flex-column;
    justify-content: flex-start;
    align-items: center;
  }
  .foot-con {
    width: 100%;
  }
  .auto-com-con {
    width: 100%;
    flex: 1;
  }
}
</style>
