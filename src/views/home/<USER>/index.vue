<template>
  <div class="comp-con">
    <div class="copy-right-notice-comp">
      <div class="title-con">
        <div class="title-info">
          <div class="title">版权声明</div>
          <div class="en-title">Copyright notice</div>
        </div>
      </div>
      <!-- -->
      <div class="content-info-con">
        <div class="content-info">
          <div class="title">版权声明</div>
          <div class="content-text">
            本网站中大连理工大学出版社有限公司出品，本网站及附属软件和在线服务的所有权和运营权归大连理工大学出版社有限公司所有。大连理工大学出版社高度重视和保护作者的权利，一贾遵守中国的各项知识产权法律、法规和具有约束力的规范性文件，在本网站发布的所有作品，均获得了出版社、著作权人等内容提供方的合法授权。如果读者发现在本网站发布的作品有侵犯著作权的行为，请提供“权利证明”与我方取得联系，出版社将在核实作品权属后，根据中国法律法规和政府规范性文件采取措施移除相关内容或屏蔽相关内容。“权利证明”包括如下内容:(1)著作权人身份证明;(2)著作权权属证明:(3)被侵权内容在本网站中所处位置或链接;(4)您的联系方式，包括但不限于地址、电话和电子邮箱;(5)您表示该内容或资料的使用未经版权所有人或其代理人/许可人的合法授权的声明;(6)法律明文要求的其他资料:请您把以上资料和联络方式发往以下地址:大连市市高新区****大连理工大学出版社
            邮政编码:10888.邮 箱:<EMAIL>.cn一旦收到符合上述要求条款资料，出版社会尽快与您取得联合，核实作品的权属。
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="CopyrightNoticeComp"></script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.comp-con {
  width: 100%;
  flex: 1;
  @extend .base-flex-row;
  justify-content: center;
  aligin-items: flex-start;
  .copy-right-notice-comp {
    width: 100%;
    height: 100%;
    .title-con {
      width: 100%;
      height: 258px;
      background: url('../../../assets/images/contactUs/contact-us-bg.png') no-repeat center;
      background-size: cover;
      padding-top: 90px;
      @extend .base-flex-column;
      justify-content: flex-start;
      align-items: center;
      .title-info {
        @extend .base-comp-card-per;
        .title {
          width: 192px;
          height: 36px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 800;
          font-size: 48px;
          color: #0966b4;
          line-height: 36px;
          text-align: left;
          margin-bottom: 21px;
        }
        .en-title {
          min-width: 121px;
          height: 36px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 24px;
          color: #0966b4;
          line-height: 36px;
          text-align: left;
          margin-bottom: 91px;
        }
      }
    }
    .content-info-con {
      width: 100%;
      background-color: #ffffff;
      background-image: radial-gradient(rgb(219, 219, 219) 1px, transparent 1px);
      background-size: 40px 40px;
      flex: 1;
      padding-top: 60px;
      padding-bottom: 200px;
      @extend .base-flex-column;
      justify-content: flex-start;
      align-items: center;
      .content-info {
        @extend .base-comp-card-per;
        .title {
          width: 100%;
          height: 36px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: bold;
          font-size: 32px;
          color: #333333;
          line-height: 36px;
          text-align: left;
          margin-bottom: 41px;
        }
        .content-text {
          width: 100%;
          min-height: 304px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #000000;
          line-height: 42px;
          text-align: left;
        }
      }
    }
  }
}
</style>
