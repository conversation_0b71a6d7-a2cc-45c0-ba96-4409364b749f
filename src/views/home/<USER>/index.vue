<template>
  <div class="contact-us-comp-con">
    <div class="contact-us-comp">
      <div class="contact-us-title">{{ data.title }}</div>
      <div class="contact-us-en-title">{{ data.titleEn }}</div>
      <div class="contact-us-info-list">
        <div class="cell-con" v-for="(item, index) in data.contacts" :key="index">
          <div class="contact-info-cell">
            <div class="contact-info-cell-icon">
              <img :src="item.icon" alt="" />
            </div>
            <div class="contact-info-cell-title">{{ item.title }}</div>
            <div class="contact-info-cell-content">
              <div class="label">{{ item.label }}</div>
              <div class="text-link">{{ item.content }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="ContactUsComp">
import { getPageCmsComponents } from '@/api/cmc/cmc';
onMounted(() => {
  let _pageId = 3;
  getPageCmsComponents(_pageId).then((reps) => {
    if (reps.code !== 200 || !reps.data) {
      return;
    }
    initPageDataList(reps);
  });
});
const data = ref({ title: '', titleEn: '', contacts: [] });
function initPageDataList(reps) {
  data.value = JSON.parse(reps.data[0].componentData);
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.contact-us-comp-con {
  width: 100%;
  flex: 1;
  background: url('../../../assets/images/contactUs/contact-us-bg.png') no-repeat center;
  background-size: cover;
  padding-top: 129px;
  @extend .base-flex-row;
  justify-content: center;
  align-items: flex-start;
  .contact-us-comp {
    @extend .base-comp-card-per;
    height: 100%;
    .contact-us-title {
      width: 100%;
      height: 36px;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 800;
      font-size: 48px;
      color: #0966b4;
      line-height: 36px;
      text-align: left;
      margin-bottom: 21px;
    }
    .contact-us-en-title {
      min-width: 121px;
      height: 36px;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 24px;
      color: #0966b4;
      line-height: 36px;
      text-align: left;
      margin-bottom: 91px;
    }
    .contact-us-info-list {
      width: 100%;
      margin-bottom: 150px;
      @extend .base-flex-row;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      .cell-con {
        width: 335px - 40px;
        height: 190px;
        box-shadow: 0px 2px 15px 0px rgba(95, 180, 244, 0.08);
        border-radius: 10px;
        background-color: #ffffff;
        margin-right: 10px;
        margin-bottom: 20px;
        .contact-info-cell {
          width: 100%;
          height: 100%;
          border-radius: 10px;
          padding-left: 29px;
          background: url('../../../assets/images/contactUs/contact-us-cell-bg.png') no-repeat center;
          background-size: cover;

          @extend .base-flex-column;
          justify-content: center;
          align-items: flex-start;
          margin-bottom: 20px;
          .contact-info-cell-icon {
            margin-bottom: 26px;
            img {
              width: 30px;
              height: 30px;
            }
          }
          .contact-info-cell-title {
            width: 100%;
            height: 36px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: bold;
            font-size: 18px;
            color: #333333;
            line-height: 36px;
            text-align: left;
          }
          .contact-info-cell-content {
            height: 36px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            line-height: 36px;
            text-align: left;
            @extend .base-flex-row;
            justify-content: flex-start;
            align-items: center;
            .label {
              margin-right: 5px;
            }
            .text {
              height: 36px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 400;
              font-size: 14px;
              color: #333333;
              line-height: 36px;
              text-align: left;
            }
            .text-link {
              height: 36px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: bold;
              font-size: 14px;
              color: #0966b4;
              line-height: 36px;
              text-align: left;
            }
            .text-last {
              margin-left: 5px;
            }
          }
        }
      }
      .cell-con:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
