<template>
  <div class="comp-container">
    <div class="comp-card">
      <div class="top-tab-con">
        <div class="title">合作院校</div>
        <div class="filter-con">
          <!-- 02 搜索条件卡 -->
            <searchParnerSchoolParamsCard @paramsChanged="paramsChangedHandler" />
        </div>
      </div>
      <div class="partner-list-con">
        <template v-for="(item, index) in partnerSchoolList" :key="index">
          <div class="partner-item" @click="selectSchool(item)">
            <img class="partner-img" :src="item.logoUrl ? item.logoUrl : bookCoverDefault" alt="" />
            <div class="partner-name">
              <span class="name-text">{{ item.schoolName }}</span>
            </div>
            <div class="partner-desc">
              <span class="desc-text" v-if="item.bookCount">{{ item.bookCount ? item.bookCount : '' }}本教材</span>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup name="partnersSchoolComp">
import { defineEmits } from 'vue';
import searchParnerSchoolParamsCard from '@/views/search/components/searchParnerSchoolParamsCard/index.vue';
import { listSchoolByQuery } from '@/api/basic/school.js';
import bookCoverDefault from '@/assets/images/book-cover-default.png';
// 定义 emit 函数
const emit = defineEmits(['select-school']);
const { proxy } = getCurrentInstance();
const partnerSchoolList = ref([]);
let defaultParams = ref({
  degreeId: '',
  isPartner: 2, // 数据备注，1不是合作院校，2是合作院校
});
//
function paramsChangedHandler(currentSubjectId) {
  defaultParams.value.degreeId = currentSubjectId ? currentSubjectId : '';
  getList(defaultParams.value);
}
function getList(paramsObject) {
  listSchoolByQuery(paramsObject)
    .then((reps) => {
      partnerSchoolList.value = reps.rows;
    })
    .catch((error) => {
    });
}
//
const toLogin = () => {
  // 跳转登录页
  proxy.$router.push({ path: '/login' });
};
// 模拟选择学校的逻辑
const selectSchool = (item) => {
  // proxy.$router.push({ path: '/home-school-books/:清华大学/:0001' });
  // emit('select-school', schoolId); // 发出自定义事件并传递 schoolId
  proxy.$router.push({
    path: '/partner-school-books',
    query: {
      schoolId: item.schoolId,
      schoolName: item.schoolName,
    },
  });
};

onMounted(() => {
  getList(defaultParams.value);
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index';
.comp-container {
  width: 100%;
  background-color: rgba(239, 248, 255, 1);
  @extend .base-flex-column;
  justify-content: center;
  align-items: center;
  .comp-card {
    width: 75%;
    min-height: 580px;
    margin-top: 50px;
    @extend .base-comp-card;
    .top-tab-con {
      width: 100%;
      margin-bottom: 20px;
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: center;
      .title {
       min-width: 150px;
        padding-left: 30px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 20px;
        color: #333333;
      }
      .filter-con {
        width: 100%;
        padding-right: 10px;
        @extend .base-flex-row;
        justify-content: flex-start;
        align-items: center;
        .tab-btn {
          width: 52px;
          height: 24px;
          line-height: 24px;
          border-radius: 12px;
          text-align: center;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #3a3a3a;
        }
        .active {
          color: #ffffff;
          background: #0966b4;
        }
      }
    }
    .partner-list-con {
      width: 100%;
      padding-bottom: 120px;
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: flex-start;
      flex-wrap: wrap;
      .partner-item {
        width: 250px;
        height: 268px;
        margin-right: 25px;
        margin-bottom: 25px;
        border-radius: 8px;
        background: #ffffff;
        cursor: pointer;
        @extend .base-flex-column;
        justify-content: center;
        align-items: center;
        .partner-img {
          width: 130px;
          height: 130px;
          border-radius: 6px;
          margin-bottom: 19px;
        }
        .partner-name {
          margin-bottom: 22px;
          .name-text {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #3a3a3a;
          }
        }
        .partner-desc {
          min-height: 12px;
          .desc-text {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
          }
        }
      }
      .partner-item:hover {
        opacity: 0.8;
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
      }
    }
  }
}
</style>
