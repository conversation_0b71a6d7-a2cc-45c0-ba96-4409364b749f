<template>
  <div class="home-page-sty">
    <div class="home-nav-container">
      <div class="nav-con">
        <headNavComp @toLogin="toLogin" />
      </div>
      <div class="banner-con">
        <banner />
      </div>
      <div class="search-con">
        <searchBar />
      </div>
      <div class="chat-box">
        <chatBox v-if="isChatBoxVisible" :key="componentBKey" />
      </div>
    </div>
    <!-- 动态组件加载 -->
    <div
      class="auto-com-con"
      v-for="(item, index) in pageDataList"
      :key="index"
    >
      <component
        :is="componentMap[item.componentKey]"
        v-if="componentMap[item.componentKey]"
        :componentData="item.componentData"
      />
    </div>
    <PasswordChangePrompt />
    <!-- 底部   -->
    <footComp />
    <!-- 联系客服 -->
    <customerServiceBar @sendMessage="receiveMessage" />
  </div>
</template>

<script setup name="HomePage">
import { ref } from "vue";
import aboutUsDesc from '@/views/home/<USER>/components/aboutUsDesc.vue';
import aboutUsHonor from '@/views/home/<USER>/components/aboutUsHonor.vue';
import aboutUsMemorabilia from '@/views/home/<USER>/components/aboutUsMemorabilia.vue';
import topImage from '@/views/home/<USER>/components/topImage.vue';

import banner from "@/views/home/<USER>/Banner/index.vue";
import searchBar from "@/views/home/<USER>/SearchBar/index.vue";
import publishersNews from "@/views/home/<USER>/PublishersNews/index.vue";
import recommendedTextbooks from "@/views/home/<USER>/RecommendedTextbooks/index.vue";
import provincialRecommendedTextbooks from "@/views/home/<USER>/ProvincialRecommendedTextbooks/index.vue";
import downloadCenter from "@/views/home/<USER>/DownloadCenter/index.vue";
import partnersSchool from "@/views/home/<USER>/PartnersSchool/index.vue";
import footComp from "@/views/home/<USER>/footComp/index.vue";
import headNavComp from "@/views/home/<USER>/headNavComp/index.vue";
import customerServiceBar from "@/views/home/<USER>/customerServiceBar/index.vue";
import chatBox from "@/views/home/<USER>/chatBox/index.vue";
import PasswordChangePrompt from "@/components/PasswordChangePrompt/index.vue";
//
import { getPageCmsComponents } from "@/api/cmc/cmc";

//
const router = useRouter();
const route = useRoute();
const isChatBoxVisible = ref(false);
const font = reactive({
  color: "rgba(0, 0, 0, .15)",
});

const pageDataList = ref([]);
const componentBKey = ref(0);

/*const DataKeyEnum = ref({
  CommonImageKey: 'common-image',
  PublisherRecommendKey: 'publisher-recommend',
  BookTypeRecommendKey: 'book-type-recommend',
  BookRecommendKey: 'book-recommend',
  PartnerKey: 'partner',
  CommonBannerKey: 'common-banner',
  DownloadCenterKey: 'download-center',
});*/

const componentMap = {
  "publisher-recommend": publishersNews,
  "book-recommend": recommendedTextbooks,
  "book-type-recommend": provincialRecommendedTextbooks,
  "download-center": downloadCenter,
  'introduce': aboutUsDesc,
  'honor': aboutUsHonor,
  'big-thing': aboutUsMemorabilia,
  'top-img': topImage,
  partner: partnersSchool,
  //'common-banner': banner,
  // 'common-image': commonImage, // 通栏图片没有
};

onMounted(() => {
  let _pageId = 1;
  getPageCmsComponents(_pageId).then((reps) => {
    if (reps.code !== 200 || !reps.data) {
      return;
    }
    initPageDataList(reps);
  });
});

function initPageDataList(reps) {
  pageDataList.value = reps.data;
  if (pageDataList.value && pageDataList.value.length > 0) {
    pageDataList.value.forEach((item) => {
      try {
        if (item.componentData && item.componentData.length > 0) {
          item.componentData = JSON.parse(item.componentData);
        }
      } catch (e) {
        item.componentData = {};
      }
    });
  }
}

const toLogin = () => {
  router.push({ path: "/login" });
};

const toPartnersSchool = () => {
  router.push({ path: "/home-partners-school" });
};
// 定义状态变量
const showPartners = ref(true);
const selectedSchoolId = ref("");

// 处理选择学校的事件
const handleSelectSchool = (schoolId) => {
  selectedSchoolId.value = schoolId;
  showPartners.value = false; // 隐藏 partnersSchoolComp 并显示 schoolBooksList
};
const receiveMessage = (data) => {
  isChatBoxVisible.value = data;
  componentBKey.value++; // 改变key值强制重新创建子组件B
};
function openDev() {
  console.log(route)
  const dev = route.query.dev9527;
  if (dev) {
    sessionStorage.setItem('OPEN_DEV', "1");
  }
}
openDev();
</script>

<style scoped lang="scss">
@import "@/assets/styles/index.scss";
.home-page-sty {
  width: 100%;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
  .home-nav-container {
    width: 100%;
    height: 442px;
    position: relative;
    .nav-con {
      width: 100%;
      // height: 442px;
      position: absolute;
      top: 11px;
      left: 0px;
      z-index: 2;
    }
    .banner-con {
      width: 100%;
      height: 442px;
      position: absolute;
      top: 0px;
      left: 0px;
      z-index: 1;
      background-color: #1c84c6;
    }
    .search-con {
      width: 100%;
      position: absolute;
      bottom: -45px;
      left: 0px;
      z-index: 3;
    }

    .chat-box {
      z-index: 1;
    }
  }
  .auto-com-con {
    width: 100%;
    flex: 1;
  }

  .bellCss {
    background-color: red;
  }
}
</style>
