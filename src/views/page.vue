<template>
  <div class="common-page-sty">
    <div class="home-nav-container">
      <div class="nav-con">
        <headNavComp @toLogin="toLogin" />
      </div>
    </div>
    <!-- 动态组件加载 -->
    <div class="auto-com-con" v-for="(item, index) in pageDataList" :key="index">
      <component :is="componentMap[item.componentKey]" v-if="componentMap[item.componentKey]" :componentData="item.componentData" />
    </div>
    <!-- 底部   -->
    <footComp />
  </div>
</template>

<script setup name="DiyPage">
import { ref } from 'vue';
import publishersNews from '@/views/home/<USER>/PublishersNews/index.vue';
import recommendedTextbooks from '@/views/home/<USER>/RecommendedTextbooks/index.vue';
import provincialRecommendedTextbooks from '@/views/home/<USER>/ProvincialRecommendedTextbooks/index.vue';
import downloadCenter from '@/views/home/<USER>/DownloadCenter/index.vue';
import partnersSchool from '@/views/home/<USER>/PartnersSchool/index.vue';
import aboutUsDesc from '@/views/home/<USER>/components/aboutUsDesc.vue';
import aboutUsHonor from '@/views/home/<USER>/components/aboutUsHonor.vue';
import aboutUsMemorabilia from '@/views/home/<USER>/components/aboutUsMemorabilia.vue';
import topImage from '@/views/home/<USER>/components/topImage.vue';
import footComp from '@/views/home/<USER>/footComp/index.vue';
import headNavComp from '@/views/home/<USER>/headNavComp/index.vue';
import customerServiceBar from '@/views/home/<USER>/customerServiceBar/index.vue';
import chatBox from '@/views/home/<USER>/chatBox/index.vue';
import contactUs from '@/views/home/<USER>/index.vue';
//
import { getPageCmsComponents } from '@/api/cmc/cmc';
const route = useRoute()
const router = useRouter();
const isChatBoxVisible = ref(false);
const font = reactive({
  color: 'rgba(0, 0, 0, .15)',
});

const pageDataList = ref([]);
const componentBKey = ref(0);

const componentMap = {
  'publisher-recommend': publishersNews,
  'book-recommend': recommendedTextbooks,
  'book-type-recommend': provincialRecommendedTextbooks,
  'download-center': downloadCenter,
  'introduce': aboutUsDesc,
  'honor': aboutUsHonor,
  'big-thing': aboutUsMemorabilia,
  'top-img': topImage,
  'partner': partnersSchool,
  'about-us': contactUs,
};
const toLogin = () => {
  router.push({ path: '/login' });
};
onMounted(() => {
  let _pageId = route.query.pageId;
  getPageCmsComponents(_pageId).then((reps) => {
    if (reps.code !== 200 || !reps.data) {
      return;
    }
    initPageDataList(reps);
  });
});

function initPageDataList(reps) {
  pageDataList.value = reps.data;
  if (pageDataList.value && pageDataList.value.length > 0) {
    pageDataList.value.forEach((item) => {
      try {
        if (item.componentData && item.componentData.length > 0) {
          item.componentData = JSON.parse(item.componentData);
        }
      } catch (e) {
        item.componentData = {};
      }
    });
  }
}

</script>

<style scoped lang="scss">
@import '@/assets/styles/index.scss';
.common-page-sty {
  width: 100%;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;
  .home-nav-container {
    width: 100%;
    position: relative;
    .nav-con {
      width: 100%;
      position: absolute;
      top: 11px;
      left: 0px;
      z-index: 2;
    }
    .banner-con {
      width: 100%;
      height: 442px;
      position: absolute;
      top: 0px;
      left: 0px;
      z-index: 1;
      background-color: #1c84c6;
    }
    .search-con {
      width: 100%;
      position: absolute;
      bottom: -45px;
      left: 0px;
      z-index: 3;
    }

    .chat-box {
      z-index: 1;
    }
  }
  .auto-com-con {
    width: 100%;
    flex: 1;
  }
}
</style>
