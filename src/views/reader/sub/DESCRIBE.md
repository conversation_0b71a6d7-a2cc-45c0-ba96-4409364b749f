### 1-目录结构

```text
reader
	index.vue ------------------------------------------//阅读器页面入口
	sub ------------------------------------------------//辅助组件
		Header.vue -------------------------------------//阅读器头部功能条
		Header -----------------------------------------//对Header.vue的补充
			AiQA.vue -----------------------------------//AI问答
			AiReadingAloud.vue -------------------------//AI朗读
			AiReadingElves.vue -------------------------//阅读精灵
			AiTeachingAssistant.vue --------------------//头部AI功能的的下拉窗
			AiTranslation.vue --------------------------//AI翻译
			AutomaticReadingBox.vue --------------------//自动阅读模式的可拖拽操作面板
			BookmarkList.vue ---------------------------//书签列表
			PageTurning.vue ----------------------------//头部中间分页部分
			PersonalizedTheme.vue ----------------------//个性主题
			Reading.vue --------------------------------//切换阅读模式
			Search.vue ---------------------------------//搜索的下拉窗口
			SignList.vue -------------------------------//标记列表
			SpeedOfProgress.vue ------------------------//全书进度
			StyleSettings.vue --------------------------//阅读器样式设置
		content.vue ------------------------------------//展示文字部分、各种弹窗入口、选中文字划线菜单入口
		Menus.vue --------------------------------------//左侧菜单
		MenusContent -----------------------------------//对Menus.vue的补充
      chapterJumpTo.js ----------------------------//跳转章节的工具
			index.vue ----------------------------------//目录入口
			item.vue -----------------------------------//目录的子集
			RecursionItem.vue --------------------------//目录子集的递归引用
		Tool.vue ---------------------------------------//右侧菜单
      Tool -------------------------------------------//右侧菜单的子类、以及各种工具
			AddNote.vue --------------------------------//添加笔记
			AudioPlayPanel.vue -------------------------//播放音频的面板
      Discuss.vue --------------------------------//讨论
			Examination.vue ----------------------------//考试
			FloatNote.vue ------------------------------//点击页面出现的笔记阅读弹窗
			Jiucuo.vue ---------------------------------//纠错弹窗
      Note.vue -----------------------------------//笔记列表
      PracticalTraining.vue ----------------------//实训列表
			PreviewDialog.vue --------------------------//预览弹窗
			ReadAloud.js -------------------------------//朗读的辅助工具
      ReadingPanel.vue ---------------------------//朗读的课拖拽控制面板
      Resources.vue ------------------------------//资源列表
			Selection.vue ------------------------------//选中文字的菜单
			SideToolsBox.vue ---------------------------//选中文字菜单的容器,计算菜单出现的位置
      SoundRecording.js --------------------------//录音的辅助工具
			Task.vue -----------------------------------//作业的列表
		Upload.vue -------------------------------------//诸多上传文件
    offlineReading ---------------------------------//导出html离线阅读的
    PageModule -------------------------------------//解析JSON引用到的自定义组件
      *.vue ---------------------------------------//各种自定义组件
      index.js ------------------------------------//JSON 组件的type值和组件的映射对象
		QuestionsItem -------------------------------//各种题型
    Pages ------------------------------------------//不同的阅读形式滑动横向滚动等
		  ParagraphTool.js ----------------------------//编译JSON的工具
		readerTheme.scss -------------------------------//阅读器部分的主题样式
```

### 2-阅读器控制方法

```
@/store/modules/reader
值-------------
总页数
当前章节的JSON数据
左右菜单隐藏出现的状态
菜单的激活项
阅读模式
阅读器样式
布局方式
主题
页面背景
翻页方式
章节ID
书本ID
已有的书签数据
已有的划线数据
已有的笔记数据
阅读记录
阅读器的模版配置

方法----------------
上一页
下一页
跳转到指定页码
打开或关闭专注模式
修改布局样式
修改翻页方式
纵向翻页定位到页面
修改需要划线的数据并生成样式注入到body标签
设置菜单的激活项
```

### 3-阅读器通用预览弹窗

```
@/store/modules/preview
预览的URL
是否需要的吧预览的地址转二进制
```

### 4-朗读部分的语速音量音高

```
@/store/modules/readAloud
```

### 5-解析JSON生成vue组件

```
通过Vue.h函数动态生成
Vue.h(标签或组件名,传递给标签的参数,子节点)
 getLableName JSON文件type值与标签和组件的映射关系
 getProps 根据JSON文件生成props和style样式
 getStyle 生成样式,以及部分样式的特殊处理
 getChildren 获取子节点,递归到最内层

 总结
 所有的文字都有一个单独的span和唯一的id,id生成规则是page下 attrs.id + 'a' + 文字顺序
```

### 6-JSON解析文本

```
 文本格式
 {
  "type": "doc",
  "content": [
    {
      "type": "page",
      "attrs": {
        "id": "2fwaqc4k",
        "extend": false,
        "class": "bellCss",
        "HTMLAttributes": null,
        "pageNumber": 1,
        "force": false,
        "slots": {},
        "pagesPosition": "top",
        "pagesAlign": "center",
        "pagesShow": true
      },
      "content": [
        {
          "type": "chapterHeader",
          "attrs": {
            "vnode": true,
            "id": null,
            "title": "认识与安装 Linux 操作系统",
            "bgColor": null,
            "bgImg": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1739582800602.svg",
            "textAlign": "center",
            "height": "258"
          }
        },
        {
          "type": "jointHeader",
          "attrs": {
            "vnode": true,
            "id": null,
            "title": "1.1 项目描述",
            "bgColor": null,
            "bgImg": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1739598253503.png",
            "textAlign": "center",
            "height": "45",
            "color": "#fff"
          }
        },
        {
          "type": "paragraph",
          "attrs": {
            "id": "odrr2mi8",
            "extend": false,
            "class": "bellCss",
            "columnCount": 0,
            "indent": 1,
            "textAlign": "left",
            "lineHeight": 1,
            "margin": {},
            "backgroundImage": "",
            "containerColor": "",
            "backgroundSize": "",
            "backgroundBorder": ""
          },
          "content": [
            {
              "type": "text",
              "marks": [
                {
                  "type": "textStyle",
                  "attrs": {
                    "fontFamily": "Times New Roman",
                    "fontSize": "16px",
                    "color": "rgb(35, 31, 32)",
                    "letterSpacing": "0px"
                  }
                }
              ],
              "text": "Linux "
            },
            {
              "type": "text",
              "marks": [
                {
                  "type": "textStyle",
                  "attrs": {
                    "fontFamily": "方正书宋_GBK",
                    "fontSize": "16px",
                    "color": "rgb(35, 31, 32)",
                    "letterSpacing": "0px"
                  }
                }
              ],
              "text": "是一种应用广泛的操作系统，它不仅安装在通用计算机中，还大量嵌入智能手机、平板电脑、机顶盒、智能电视机、路由器、防火墙、导航系统和游戏机等各种物联网的智能设备中，"
            },
            {
              "type": "text",
              "marks": [
                {
                  "type": "textStyle",
                  "attrs": {
                    "fontFamily": "Times New Roman",
                    "fontSize": "16px",
                    "color": "rgb(35, 31, 32)",
                    "letterSpacing": "0px"
                  }
                }
              ],
              "text": "Linux "
            },
            {
              "type": "text",
              "marks": [
                {
                  "type": "textStyle",
                  "attrs": {
                    "fontFamily": "方正书宋_GBK",
                    "fontSize": "16px",
                    "color": "rgb(35, 31, 32)",
                    "letterSpacing": "0px"
                  }
                }
              ],
              "text": "已成为全球各类网络终端设备中装有量和用户数量最多的操作系统。"
            }
          ]
        }
      ]
    }
  ]
}

content内文页面内容,每一个对象视为一个阅读节点,type类型为节点的类型,通过ParagraphTool.js 文件解析成vue识别的文本节点,attrs为该节点的属性
ParagraphTool.js 中 通过 h 函数进行解析该节点类型,props以及子节点 其中getLableName 获取节点类型的映射
1-如果是H5标签则正常渲染标签,通过getProps给标签添加属性和行内样式,
2-如果是自定义节点(映射对象在pageModule文件夹下index.js),那就直接返回vue组件,组件内props的值是attrs的值
3-其中有几个例外需要特殊处理,如表格等
4-getChildren是获取子节点的方法,由于编辑器原因,子节点肯呢个无限深,所以这里递归调用一直查找到最深层
5-由于其他业务需要(例如单独给文字划线,添加样式等),需要给每个除自定节点外的节点的文字单独的span标签,切给每个标签一个单独的ID,ID命名规则为a+${attrs.id}+文字序号(详见getId方法)
6-需要触发选中文字的事件,所以这里给每个文本节点添加自定义属性sign=selection
7-有些特殊的css属性在JSON里是简写,所以在getStyle函数里做了映射

	 
```

### 7-阅读器控制

```
详见@/store/modules/reader
由于有不同的阅读样式(滑动翻页、左右翻页、真是翻页),所以处理上下翻页的逻辑不同
	1)数据不同,请求书本内容的逻辑为每次请求一个章节的内容,存储为 pageData ,页面实际展示的数据为 currentData ,count记录当前在哪一页横向翻页是通过控制 currentData 的数据实现翻页的功能,滑动翻页时 currentData = pageData ,一次性加载所有页数,因为怕数据太多造成卡顿,所以加载的只是空页面,空页面的溶剂进入可视区,才会加载实际的内容,当前页码的计算是根据页面容器最上端距离浏览器顶部距离的绝对值计算,其中可通过page_y_display_page函数进行滚动到固定页;真实翻页同样 currentData = pageData ,渲染逻辑由真实翻页的组件确定
	2)有阅读模式的区分,所以阅读器左右的菜单是否出现由toolIsHide menuIsHide等两个变量控制
	3)其中自动阅读处理逻辑依然不同 import autoTask from "@/utils/automaticReading.js" 辅助处理自动阅读的工具函数,autoTask.branchTask在上下翻页,获取切换到固定页的时候,负责暂停自动阅读,待加载完成后,执行callback
	4)有键盘左右方向键翻页的功能,同样是在Content.vue内绑定事件,然后调用nextPage 和 lastPage 函数进行上下翻页
```

### 8-选中文字事件、样式处理

```
功能入口在Content.vue文件内
1)获取选中的文字getSelectedText()
2)获取选中的节点 selectionTableBar.elementList
3)添加划线和笔记和底纹等,是一次性获取所有的笔记和划线列表,算出选中区间内的所有文字的ID,然后动态创建style标签,注入样式后添加到body标签内,实现划线.
4)点击添加过笔记的文字的事件,是每次拿选中的文字的ID后,获取dom节点,去给每个文字绑定点击事件去处理
5)百度百科和辞典跳转到第三方网页,品号参数跳转页面就OK

```

### 9-左侧菜单的递归调用

```
左侧菜单实际上最多有6级标题,所以一般情况子集需要调用6次,分别判断;所以在RecursionItem.vue 引用重复组件实现组件递归,这里深层次的点击事件需要主机上传最终到标签的父级,所以这里不能拿事件对象直接处理菜单的选中状态等,需要用key值修改,或者直接修改calss样式
```

