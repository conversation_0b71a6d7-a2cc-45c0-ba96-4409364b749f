<!-- 模块说明 -->
<style lang="scss" scoped>
.header {
  width: 100%;
  height: 48px;
  min-width: 1266px;
  background-color: var(--modelBackgroundColor);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-sizing: border-box;
  color: var(--fontColor);
  .header-center{
    display: flex;
    justify-content: center;
  }
  .header-right,
  .header-left {
    display: flex;
    align-items: center;

    .header-left-home {
      display: flex;
      align-items: center;
      font-weight: 500;
      font-size: 16px;
      line-height: 16px;
      font-style: normal;
      cursor: pointer;
    }
    .header-left-item {
      display: flex;
      align-items: center;
      height: 30px;
      border-radius: 4px;
      font-weight: 400;
      font-size: 16px;
      cursor: pointer;
      transition: 0.3s;
      color: var(--fontColor);
      &:hover {
        color: #0966b4;
        background: var(--hoverBackgroundColor);
      }
    }
    .header-function {
      cursor: pointer;
      position: relative;
      margin-right: 20px;
      .flag {
        width: 5px;
        height: 5px;
        background-color: red;
        border-radius: 50%;
        position: absolute;
        top: 0;
        right: 2px;
      }
      &:hover {
        color: var(--hoverfont);
      }
    }
  }

  .header-left,
  .header-right {
    gap: 24px;
    .el-icon {
      font-size: 28px;
    }
    .header-left-item-icon {
      width: 25px;
      height: 25px;
    }
    .header-tool-label {
      display: none;
    }
    @media screen and (min-width: 1280px) {
      gap: 14px;
      .el-icon {
        font-size: inherit;
      }
      .header-tool-label {
        display: inline-block;
      }
    }
    @media screen and (min-width: 1380px) {
      gap: 24px;
    }
  }

  .header-left,
  .header-right,
  .header-center {
    flex: 1;
  }

  .header-right {
    justify-content: flex-end;
  }
}
</style>

<template>
  <div class="header">
    <section
      class="header-left">
      <div
        class="header-left-home"
        @click="gotoHome()"
        v-if="showReturnToHome">
        <el-icon title="返回主页"><HomeFilled /></el-icon>&nbsp;<span class="header-tool-label"><span style="display: inline-block;">返回</span><span style="display: inline-block;">主页</span></span>
      </div>

      <el-popover :width="380"
        trigger="click"
        :teleported="false">
        <template #reference>
          <div
            class="header-left-item"
            ref="themeRef">
            <img
              src="@/assets/images/readerHeader/theme.png"
              alt="主题"
              title="主题"
              class="header-left-item-icon"
              v-if="store.theme == 'light'" />
            <img
              src="@/assets/images/readerHeader/theme_d.png"
              alt="主题"
              title="主题"
              class="header-left-item-icon"
              v-else />

            &nbsp;<span class="header-tool-label"><span style="display: inline-block;">个性</span><span style="display: inline-block;">主题</span></span>
          </div>
        </template>
        <template #default>
          <PersonalizedTheme
            :simple-preview-mode="simplePreviewMode" />
        </template>
      </el-popover>

      <el-popover :width="450"
        trigger="click"
        :teleported="false"
        ref="styleSettingsPopoverRef">
        <template #reference>
          <div
            class="header-left-item"
            ref="styleRef">
            <img
              src="@/assets/images/readerHeader/styles.png"
              alt="样式设置"
              title="样式设置"
              class="header-left-item-icon"
              v-if="store.theme == 'light'" />
            <img
              src="@/assets/images/readerHeader/styles_d.png"
              alt="样式设置"
              title="样式设置"
              class="header-left-item-icon"
              v-else />
            &nbsp;<span class="header-tool-label"><span style="display: inline-block;">样式</span><span style="display: inline-block;">设置</span></span>
          </div>
        </template>
        <template #default>
          <StyleSettings
            @selectChanged="styleSettingsChangedHandler"
            :simple-preview-mode="simplePreviewMode" />
        </template>
      </el-popover>

      <el-popover :width="390"
        trigger="click"
        :teleported="false"
        ref="readingModePopoverRef">
        <template #reference>
          <div
            class="header-left-item"
            ref="readRef">
            <img
              src="@/assets/images/readerHeader/readingMode.png"
              alt="阅读模式"
              title="阅读模式"
              class="header-left-item-icon"
              v-if="store.theme == 'light'" />
            <img
              src="@/assets/images/readerHeader/readingMode_d.png"
              alt="阅读模式"
              title="阅读模式"
              class="header-left-item-icon"
              v-else />

            &nbsp;<span class="header-tool-label"><span style="display: inline-block;">阅读</span><span style="display: inline-block;">模式</span></span>
          </div>
        </template>
        <template #default>
          <Reading
            @selectChanged="readingModeChangedHandler" />
        </template>
      </el-popover>
      <div ref="aiRef"
        v-if="showAiAssistent">
        <AiTeachingAssistant />
      </div>
      <!-- <div class="header-left-item">
        <img src="@/assets/images/readerHeader/parallel.png" alt="" class="header-left-item-icon" v-if="store.theme == 'light'" />
        <img src="@/assets/images/readerHeader/parallel_d.png" alt="" class="header-left-item-icon" v-else />
        &nbsp;平行阅读
      </div> -->
    </section>

    <section
      class="header-center"
      ref="pageRef">
      <PageTurning
        :show-reading-progress="showReadingProgress" />
    </section>

    <section
      class="header-right"
      v-show="store.reading !== 'absorbed'">
      <el-popover :width="376"
        trigger="click"
        :teleported="false"
        v-if="showBookLines">
        <template #reference>
          <div
            class="header-left-item"
            @click="showDialog('标记列表')"
            ref="markRef">
            <img
              src="@/assets/images/readerHeader/underline.png"
              alt="标记"
              title="标记"
              class="header-left-item-icon"
              v-if="store.theme == 'light'" />
            <img
              src="@/assets/images/readerHeader/underline_d.png"
              alt="标记"
              title="标记"
              class="header-left-item-icon"
              v-else />
            &nbsp;<span class="header-tool-label">标记</span>
          </div>
        </template>
        <template #default>
          <SignList />
        </template>
      </el-popover>

      <el-popover :width="376"
        trigger="click"
        :teleported="false"
        v-if="showBookMarks">
        <template #reference>
          <div
            class="header-left-item"
            @click="showDialog('书签列表')"
            ref="bookMarkRef">
            <img
              src="@/assets/images/readerHeader/bookmark.png"
              alt="书签"
              title="书签"
              class="header-left-item-icon"
              v-if="store.theme == 'light'" />
            <img
              src="@/assets/images/readerHeader/bookmark_d.png"
              alt="书签"
              title="书签"
              class="header-left-item-icon"
              v-else />
            &nbsp;<span class="header-tool-label">书签</span>
          </div>
        </template>
        <template #default>
          <BookmarkList />
        </template>
      </el-popover>

      <!-- <div class="header-left-item btn-disabled">
        <img
          src="@/assets/images/readerHeader/report.png"
          alt=""
          class="header-left-item-icon"
          v-if="store.theme == 'light'"
        />
        <img
          src="@/assets/images/readerHeader/report_d.png"
          alt=""
          class="header-left-item-icon"
          v-else
        />
        &nbsp;学习报告
      </div> -->
      <div>
        <span
          class="header-function"
          v-if="showTourGuide">
          <svg-icon
            iconClass="help"
            @click="showHelp" />
        </span>
        <el-popover
          :width="376"
          trigger="click"
          :teleported="false"
          @show="searchOnShownHandler"
          @hide="searchOnHiddenHandler"
          v-if="showSearch">
          <template
            #reference>
            <span
              class="header-function">
              <svg-icon
                iconClass="search" />
            </span>
          </template>
          <template #default>
            <Search />
          </template>
        </el-popover>

        <span
          class="header-function"
          @click="gotoMessage"
          v-if="showMessageHint">
          <svg-icon
            iconClass="notice" />
          <span class="flag"
            v-if="messageTag"></span>
        </span>
        <span
          class="header-function"
          @click="fullScreen">
          <svg-icon
            iconClass="fullScreen" />
        </span>

        <!-- <span class="header-function" @click="start()">
          <el-icon><Download /></el-icon>
        </span> -->
      </div>
    </section>

    <section
      class="header-right"
      v-show="store.reading == 'absorbed'">
      <!-- <div style="font-size: 14px">
        <el-switch v-model="absorbed" />&nbsp;&nbsp;专注模式
      </div> -->
      <div
        class="header-left-item"
        style="margin: 0 40px"
        @click="signOut">
        <svg-icon
          iconClass="signOut" />&nbsp;退出专注模式
      </div>
    </section>
  </div>

  <AutomaticReadingBox
    v-if="store.reading == READING_MODE.AUTO_READING" />
</template>

<script setup>
import PageTurning from './Header/PageTurning.vue'
import Reading from './Header/Reading.vue'
import StyleSettings from './Header/StyleSettings.vue'
import PersonalizedTheme from './Header/PersonalizedTheme.vue'
import AutomaticReadingBox from './Header/AutomaticReadingBox.vue'
import AiTeachingAssistant from './Header/AiTeachingAssistant.vue'
import Search from './Header/Search.vue'
import BookmarkList from './Header/BookmarkList.vue'
import SignList from './Header/SignList.vue'
import { ref } from 'vue'
// import { Download } from "@element-plus/icons-vue";
import useReader, { READING_MODE } from '@/store/modules/reader'
import { getBookMark, getBookLine } from '@/api/book/reader'
import { unregisterKeyboardEvent, registerKeyboardEvent } from '@/utils/reader'
// import { start } from "./offlineReading/index.js";

const store = useReader()
// const absorbed = ref(true);
const router = useRouter()

const tourStep = ref(false)

// 引导页定义变量
const themeRef = ref()
const styleRef = ref()
const readRef = ref()
const aiRef = ref()
const pageRef = ref()
const markRef = ref()
const bookMarkRef = ref()
const readingModePopoverRef = ref()
const styleSettingsPopoverRef = ref()

const props = defineProps({
  messageTag: {
    type: Boolean,
    default: false
  },
  showMessageHint: {
    type: Boolean,
    default: true
  },
  showReturnToHome: {
    type: Boolean,
    default: true
  },
  showAiAssistent: {
    type: Boolean,
    default: true
  },
  showBookLines: {
    type: Boolean,
    default: true
  },
  showBookMarks: {
    type: Boolean,
    default: true
  },
  showTourGuide: {
    type: Boolean,
    default: true
  },
  showSearch: {
    type: Boolean,
    default: true
  },
  showReadingProgress: {
    type: Boolean,
    default: true
  },
  simplePreviewMode: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['showHelp'])
const showDialog = title => {
  if (title === '书签列表') {
    getBookMark(store.comprehensiveBookData.bookId).then(res => {
      if (res.code === 200) store.bookMarkData = res.data
    })
  } else {
    getBookLine({
      bookId: store.comprehensiveBookData.bookId,
      color: null
    }).then(res => {
      if (res.code === 200) store.setBookLineData(res.data)
    })
  }
}

const gotoMessage = () => {
  router.push('/my-message')
}
function readingModeChangedHandler() {
  readingModePopoverRef.value.hide()
}
function styleSettingsChangedHandler() {
  styleSettingsPopoverRef.value.hide()
}
// watch(
//   () => absorbed.value,
//   (val) => {
//     if (!val) {
//       signOut();
//     }
//   },
//   { deep: true }
// );

// watch(
//   () => store.reading,
//   (val) => {
//     if (val === "absorbed") {
//       absorbed.value = true;
//     }
//   },
//   { deep: true }
// );
function gotoHome() {
  router.push('/')
}
const signOut = () => {
  store.closeAbsorbed()
  store.setReading('reading')
}

const fullScreen = () => {
  if (document.fullscreenElement) {
    //关闭全屏
    if (document.exitFullscreen) {
      document.exitFullscreen()
    } else if (document.mozCancelFullScreen) {
      /* Firefox */
      document.mozCancelFullScreen()
    } else if (document.webkitExitFullscreen) {
      /* Chrome, Safari and Opera */
      document.webkitExitFullscreen()
    } else if (document.msExitFullscreen) {
      /* IE/Edge */
      document.msExitFullscreen()
    }
  } else {
    //打开全屏
    const element = document.documentElement
    if (element.requestFullscreencreen) {
      element.requestFullScreen()
    } else if (element.mozRequestFullScreen) {
      element.mozRequestFullScreen()
    } else if (element.webkitRequestFullScreen) {
      element.webkitRequestFullScreen()
    } else if (element.msRequestFullScreen) {
      element.msRequestFullScreen()
    }
  }
}

const showHelp = () => {
  emits('showHelp', true)
}

function searchOnShownHandler() {
  registerKeyboardEvent(
    'searchInReader',
    e => {
      // 由于阅读器注册了左键和右键进行翻页，当弹窗出现的时候，弹窗注册左键和右键，并停止世界冒泡，用于区分变速器，查看上一页和下一页。
      // 其他弹窗同理
      if (e.keyCode === 39 || e.keyCode === 37) {
        e.cancelBubble = true
      }
    },
    {
      before: 'reader'
    }
  )
}
function searchOnHiddenHandler() {
  unregisterKeyboardEvent('searchInReader')
}

// 暴露给父组件
defineExpose({
  themeRef,
  styleRef,
  readRef,
  aiRef,
  pageRef,
  markRef,
  bookMarkRef
})
</script>
