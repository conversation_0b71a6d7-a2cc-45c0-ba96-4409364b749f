<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2025-01-16 15:39:01
 * @LastEditTime: 2025-02-28 09:23:19
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Header\AiReadingAloud.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- ai朗读 -->
<style lang="scss" scoped>
.AiReadingAloud {
  width: 100%;
  height: 100%;
  padding: 0 160px;
  box-sizing: border-box;

  &>header {
    height: 280px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .soundItem {
      width: 175px;
      height: 70px;
      background: #524FFF;
      box-shadow: 0px 0px 16px 0px rgba(66, 66, 66, 0.53);
      border-radius: 8px;
    }
  }

  &>main {
    .sliderItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 5px 0;

      .titlt {
        width: 40px;
        margin-right: 20px;
      }
    }
  }

  &>footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40px;

    .footerBtn {
      width: 142px;
      height: 40px;
      border-radius: 40px;
      outline-style: none;
      border-color: #524FFF;
      cursor: pointer;
      background-color: rgba(0, 0, 0, 0);
      font-size: 18px;
    }

    .sure {
      background-color: #524FFF;
      color: #fff;
    }
  }
}
</style>

<template>
  <div class="AiReadingAloud">
    <header>
      <div class="soundItem"></div>
      <div class="soundItem"></div>
    </header>
    <main>
      <div class="sliderItem">
        <span class="titlt">语速</span>
        <el-slider show-input size="small" />
      </div>
      <div class="sliderItem">
        <span class="titlt">音量</span>
        <el-slider show-input size="small" />
      </div>
      <div class="sliderItem">
        <span class="titlt">音高</span>
        <el-slider show-input size="small" />
      </div>
    </main>
    <footer>
      <button class="footerBtn">重置</button>
      <button class="footerBtn sure">确定合成</button>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

onMounted(() => { })
</script>
