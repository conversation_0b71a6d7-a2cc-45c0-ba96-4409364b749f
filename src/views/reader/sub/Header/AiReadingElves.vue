<!-- 模块说明 -->
<style>
.AiReadingElves {
  .highlight-keywords {
    color: #0966b4;
    font-weight: 700;
  }
}
</style>
<style lang="scss" scoped>
.AiReadingElves {
  overflow-y: auto;
  & > header {
    padding: 20px;
    display: flex;
    background-color: var(--modelBackgroundColor);
    border-radius: 8px;
    margin-bottom: 5px;
    .text {
      flex: 1;
    }
  }
  .card {
    padding: 20px;
    background-color: var(--modelBackgroundColor);
    border-radius: 8px;
    margin: 5px 0;
    min-height: 100px;
    .matched-key-word {
      cursor: pointer;
    }
  }
}
</style>
<template>
  <div class="AiReadingElves">
    <header>
      <el-input
        prefix-icon="Search"
        v-model="inputKeywords"
        style="width: 100%; margin-right: 5px"
        placeholder="请输入关键词"
        v-on:keyup.enter="enterHandler" />
      <el-button link
        @click="searchInBook"
        v-if="!searchInBookFlag">搜索本书<el-icon>
          <ArrowRightBold />
        </el-icon></el-button>
      <el-button link
        @click="searchInBookFlag = false"
        v-else="searchInBookFlag"><el-icon>
          <CloseBold />
        </el-icon></el-button>
    </header>
    <div class="card"
      v-show="!searchInBookFlag"
      element-loading-text="搜索中..."
      v-loading="searchingInProcess">
      <p>{{ contentExplain }}
      </p>
      <p
        v-if="quotedBooks.length > 0">
        以下是书籍引源</p>
      <ul
        v-if="quotedBooks.length > 0"
        style="list-style: none; padding-left: 0">
        <li
          v-for="(bookItem, index) in quotedBooks"
          :key="index"
          style="color: #0966b4">
          <p>
            引自{{ bookItem.author }}
            {{ bookItem.name }}
          </p>
        </li>
      </ul>
    </div>
    <div class="card"
      v-show="searchInBookFlag"
      v-loading="">
      <div
        v-for="(chapterCompData, index) in chapterGroupQueryList"
        :key="index">
        <p>
          {{ chapterCompData.chapterName }}
          <el-icon
            style="float: right"
            v-if="chapterCompData.free === 2 || chapterCompData.purchased > 0">
            <Lock />
          </el-icon>
        </p>
        <el-card
          style="width: 100%"
          shadow="hover">
          <ul
            style="list-style: none; padding-left: 0">
            <li
              v-for="(item, searchIndex) in chapterCompData.data"
              :key="searchIndex"
              class="matched-key-word"
              @click="goToKeyword(item, chapterCompData)">
              <div
                v-html="searchResultSplitter(item.sentence)">
              </div>
              <el-divider
                v-if="searchIndex !== chapterCompData.data.length - 1" />
            </li>
          </ul>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import { getReadingWithExtendKnowledge } from '@/api/book/reader'
import { getQueryChapterContent } from '@/api/book/reader.js'
import useReader from '@/store/modules/reader'
import { highlightKeyWordSmooth } from '@/utils/reader'
import { generateTextId } from '@/views/reader/sub/Pages/ParagraphTool'
import { getAiAssistantUseCount } from '@/api/openApi/openApi'
const emits = defineEmits(['close-spirit'])
const store = useReader()
const searchingInProcess = ref(false)
const props = defineProps({
  aiProcessSearching: {
    type: Boolean,
    default: false
  },
  translationContent: {
    type: String,
    default: ''
  }
})
const searchInBookFlag = ref(false)
const quotedBooks = ref([])
const extendedPlains = ref([])
const contentExplain = ref('')
const chapterGroupQueryList = ref([])
const inputKeywords = ref('')
function goToKeyword(keywordItem, chapterData) {
  emits('close-spirit')
  const contextParagraph = keywordItem.sentence
  const keywordIndex = contextParagraph.indexOf(inputKeywords.value)
  const targetTextNodeIdArr = generateTextId({
    paraId: keywordItem.textId,
    startIndex: keywordIndex,
    keywordLength: inputKeywords.value.length
  })
  store.jumpToChapter(chapterData.chapterId, keywordItem.pageNumber).then(() => {
    highlightKeyWordSmooth(targetTextNodeIdArr)
  })
}
function searchResultSplitter(sentence) {
  const keywordsContext = sentence.split(inputKeywords.value)
  return keywordsContext.join(`<span class="highlight-keywords">${inputKeywords.value}</span>`)
}

function enterHandler() {
  chapterGroupQueryList.value = []
  if (searchInBookFlag.value) {
    searchInBook()
  } else {
    searchinAI()
  }
}
function searchInBook() {
  searchInBookFlag.value = true
  searchingInProcess.value = true
  const allChapterSearchResult = []
  const tmpValue = store.comprehensiveChapterAndCatalogData.chaptersData.map(chapterCatalogData => {
    return {
      chapterId: chapterCatalogData.chapterId,
      chapterName: chapterCatalogData.chapterName,
      count: 0,
      free: chapterCatalogData.free,
      purchased: chapterCatalogData.hasBuy,
      data: []
    }
  })
  tmpValue.forEach(chapterCatalogData => {
    allChapterSearchResult.push(
      new Promise((resolve, reject) => {
        const request = {
          bookId: store.comprehensiveBookData.bookId,
          chapterId: chapterCatalogData.chapterId,
          keyword: inputKeywords.value
        }
        getQueryChapterContent(request)
          .then(res => {
            chapterCatalogData.data = res.current || []
            resolve(res.current)
          })
          .catch(reject)
      })
    )
  })
  Promise.all(allChapterSearchResult)
    .then(() => {
      chapterGroupQueryList.value = tmpValue.filter(tmpItem => tmpItem.data.length > 0)
    })
    .finally(() => (searchingInProcess.value = false))
}
async function searchinAI() {
  searchingInProcess.value = true
  const { aiExperimentCount } = await getAiAssistantUseCount()
  if (aiExperimentCount > 0) {
    getReadingWithExtendKnowledge(inputKeywords.value)
      .then(result => {
        const aiResult = JSON.parse(result.body.content)
        contentExplain.value = aiResult.explain
        for (let i = 0; i < 3; i++) {
          if (aiResult['extends' + (i + 1)]) {
            extendedPlains.value.push(aiResult['extends' + (i + 1)])
          }
        }
        quotedBooks.value = aiResult.book || []
      })
      .finally(() => (searchingInProcess.value = false))
  } else {
    searchingInProcess.value = false
    ElMessage.error('您的免费次数已用完')
  }
}
function reset() {
  contentExplain.value = ''
  extendedPlains.value = []
  quotedBooks.value = []
  inputKeywords.value = ''
}

watch(
  () => [props.aiProcessSearching, props.translationContent],
  ([nValue4aiSearching, nValue4TranslateText]) => {
    if (!nValue4aiSearching) {
      extendedPlains.value = []
      reset()
      return
    }
    if (!nValue4TranslateText) {
      return
    }
    inputKeywords.value = nValue4TranslateText
    searchinAI()
  },
  { immediate: true }
)
</script>
