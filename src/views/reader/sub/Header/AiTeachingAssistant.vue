<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2025-01-15 13:11:31
 * @LastEditTime: 2025-01-16 16:15:05
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Header\aiTeachingAssistant.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 模块说明 -->
<style lang="scss" scoped>
.AiTeachingAssistant {
  position: relative;
  display: flex;
  align-items: center;
  height: 30px;
  border-radius: 4px;
  font-weight: 400;
  font-size: 16px;
  cursor: pointer;
  transition: 0.3s;
  color: var(--fontColor);
  .AiTeachingAssistant-icon {
    width: 20px;
    height: 20px;
  }
  &:hover {
    color: var(--hoverfont);
    background: var(--hoverBackgroundColor);
  }

  .popover {
    position: absolute;
    width: 270px;

    background-image: url(@/assets/images/readerHeader/AiTeachingAssistantBackground.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    top: 68%;
    left: -90%;
    z-index: 2;
    padding: 20px;
    box-sizing: border-box;
    .popover-main {
      padding: 5px 20px;
      box-sizing: border-box;
      .header {
        height: 47px;
        width: 100%;
        color: #000;
        display: flex;
        align-items: center;
        justify-content: space-between;
        & > span:nth-of-type(1) {
          font-weight: bold;
          font-size: 16px;
          color: #333333;
        }
        & > span:nth-of-type(2) {
          flex: 1;
          text-align: right;
          margin-right: 5px;
          font-size: 12px;
          color: #999999;
        }
        & > span:nth-of-type(3) {
          font-size: 12px;
          color: #ffffff;
          padding: 2px 6px;
          background: linear-gradient(to right, #932bff, #4f51ff);
          border-radius: 60px;
        }
      }
      .content {
        width: 100%;
        font-size: 14px;
        color: #333333;

        .content-item {
          display: flex;
          align-items: center;
          height: 30px;
          font-size: 14px;
          margin-bottom: 7px;
          & > img {
            width: 18px;
            height: 18px;
            margin-right: 8px;
          }
          & > span {
            flex: 1;
            font-size: 12px;
            color: #666666;
            text-align: right;
            padding: 7px 0;
            &:hover {
              color: var(--hoverfont);
            }
          }
          &:hover {
            color: var(--hoverfont);
            background: var(--hoverBackgroundColor);
          }
        }
        .content-item-disabled {
          display: flex;
          align-items: center;
          height: 30px;
          color: #9c9c9c;
          margin-bottom: 7px;
          cursor: no-drop;
          & > img {
            width: 18px;
            height: 18px;
            margin-right: 8px;
          }
        }
      }
    }
  }
}
.dialogMain {
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}
</style>
<style lang="scss">
.AiTeachingAssistant-dialog {
  background-repeat: no-repeat;
  background-color: #f4f5f8;
  border-radius: 8px;
  min-width: 956px;
  .el-dialog__headerbtn {
    margin: 10px;
  }
  .el-dialog__header {
    padding-bottom: 0;
  }
}

.__flex-center__ {
  cursor: pointer;
}
</style>

<template>
  <div
    class="AiTeachingAssistant"
    @click.stop="clickAI">
    <img
      src="@/assets/images/readerHeader/AiTeachingAssistantIcon.png"
      alt=""
      class="AiTeachingAssistant-icon"
      v-if="store.theme == 'light'" />
    <img
      src="@/assets/images/readerHeader/AiTeachingAssistantIcon.png"
      alt=""
      class="AiTeachingAssistant-icon"
      v-else />
    &nbsp;<span><span style="display: inline-block;">AI</span><span style="display: inline-block;">助教</span></span>
    <div class="popover"
      @click.stop="() => {}"
      v-if="isShow">
      <div
        class="popover-main">
        <div class="header">
          <span>AI助教</span>
          <span>{{ `${num}/20次` }}</span>
          <span
            class="__flex-center__"
            v-if="num <= 10"
            @click="toRecharge">去充值</span>
          <span
            class="__flex-center__"
            v-else>限时免费</span>
        </div>
        <main class="content">
          <div
            class="content-item"
            @click="showDialog('AI问答')">
            <img
              src="@/assets/images/readerHeader/wenda.png"
              alt="" />AI问答
          </div>
          <!-- <div class="content-item" @click="showDialog('AI朗读')">
            <img
              src="@/assets/images/readerHeader/langdu.png"
              alt=""
              style="width: 12px; height: 17px"
            />
            AI朗读
            <span>工具栏</span>
          </div> -->
          <!-- <div class="content-item" @click="showDialog('输出语音')">
            <img
              src="@/assets/images/readerHeader/yuyinjiaohu.png"
              alt=""
              style="width: 14px; height: 15px"
            />输出语音
          </div> -->
          <!-- @click="showDialog('阅读精灵')" -->
          <div
            class="content-item-disabled"
            title="智能检索，答疑解惑">
            <img
              src="@/assets/images/readerHeader/jingling.png"
              alt="智能检索，答疑解惑" />阅读精灵
          </div>
          <!-- @click="showDialog('AI翻译')" -->
          <div
            class="content-item-disabled"
            title="智能翻译，精准高效">
            <img
              src="@/assets/images/readerHeader/fanyi.png"
              alt="智能翻译，精准高效" />AI翻译
          </div>
        </main>
      </div>
    </div>
  </div>

  <el-dialog
    v-model="dialogData.isShow"
    class="AiTeachingAssistant-dialog"
    @close="closeHandler">
    <template #title>
      <div style="
          font-weight: 500;
          font-size: 16px;
          color: #333333;
          padding: 10px;
          text-align: center;
        ">
        {{ dialogData.title }}
      </div>
    </template>
    <main class="dialogMain">
      <component
        :is="dialogData.components"
        :translationContent="dialogData.translationContent"
        :ai-process-searching="dialogData.isShow"
        @close-spirit="closeSpiritHandler">
      </component>
    </main>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, reactive } from 'vue'
import useReader from '@/store/modules/reader'
import AiQA from './AiQA.vue'
import AiReadingAloud from './AiReadingAloud.vue'
import AiTranslation from './AiTranslation.vue'
import AiReadingElves from './AiReadingElves.vue'

import { getAiAssistantUseCount } from '@/api/openApi/openApi'
// import { copyText } from "@/utils/index.js";
const store = useReader()
const isShow = ref(false)
const num = ref(10)
const { proxy } = getCurrentInstance()
const dialogData = reactive({
  isShow: false,
  title: '',
  components: null,
  translationContent: '' //翻译的内容
})
// AI助教
const clickAI = () => {
  isShow.value = true
  getAiAssistantUseCountHandler()
}
function closeSpiritHandler() {
  dialogData.isShow = false
}
function closeHandler() {
  dialogData.isShow = false
  dialogData.translationContent = ''
}
const componentsOption = {
  AI问答: AiQA,
  AI朗读: AiReadingAloud,
  AI翻译: AiTranslation,
  阅读精灵: AiReadingElves
}
const showDialog = (v, selectedText) => {
  dialogData.translationContent = selectedText
  dialogData.isShow = true
  dialogData.title = v
  dialogData.components = componentsOption[v]
  documentClick()
}
const documentClick = () => {
  if (isShow.value) isShow.value = false
}
onMounted(() => {
  document.addEventListener('click', documentClick)
  getAiAssistantUseCountHandler()
  store.aiDialog = showDialog
})
onBeforeUnmount(() => {
  document.removeEventListener('click', documentClick)
})

const getAiAssistantUseCountHandler = async () => {
  const { aiExperimentCount } = await getAiAssistantUseCount()
  num.value = aiExperimentCount
}

const toRecharge = () => {
  proxy.$modal.msgError('暂未开放')
}
// getReadingWithExtendKnowledge
</script>
