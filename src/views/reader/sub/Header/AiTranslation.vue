<!-- ai翻译 -->
<style lang="scss" scoped>
.AiTranslation {
  width: 100%;
  height: 100%;
  .AiTranslation-main {
    width: 100%;
    height: calc(100% - 60px);
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0px 0px 16px 0px rgba(230, 237, 255, 0.53);
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    .AiTranslation-main-right,
    .AiTranslation-main-left {
      flex: 1;
      width: 100%;
      padding: 10px 20px;
      box-sizing: border-box;
      .header {
        width: 100%;
        height: 40px;
        font-size: 14px;
        color: #333333;
      }
      .content {
        height: 300px;
        line-height: 23px;
        .contentTextarea {
          width: 100%;
          .no-border-input {
            :v-deep(.el-input__inner) {
              border: none; /* 或者 border: 0; */
              box-shadow: none; /* 去掉可能的阴影 */
              outline: none; /* 去掉聚焦时的轮廓线 */
            }
          }
        }
      }
    }
    .AiTranslation-main-solid {
      height: calc(100% - 50px);
      border-left: 1px dashed #979797;
      margin-top: 50px;
    }
  }
  .AiTranslation-footer {
    position: relative;
    .operation {
      width: 113px;
      height: 26px;
      background-color: rgba(255, 255, 255, 0.5);
      border-radius: 10px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      position: absolute;
      right: 0;
      top: -34px;
      & > img {
        width: 12px;
        height: 12px;
      }
    }
    .explain {
      margin-top: 40px;
      text-align: center;
      font-size: 10px;
      color: #999999;
    }
  }
}
</style>

<template>
  <div class="AiTranslation">
    <div
      class="AiTranslation-main">
      <div
        class="AiTranslation-main-left">
        <div class="header">
          <el-select
            v-model="fromLanguage"
            placeholder="Select"
            style="width: 123px"
            size="small">
            <el-option
              v-for="item in options"
              :key="item.parameter"
              :label="item.language"
              :value="item.parameter" />
          </el-select>
          <el-icon
            style="margin: 5px 10px">
            <Switch />
          </el-icon>
          <el-select
            v-model="toLanguage"
            placeholder="Select"
            style="width: 123px"
            size="small">
            <el-option
              v-for="item in options"
              :key="item.parameter"
              :label="item.language"
              :value="item.parameter" />
          </el-select>

          <el-button
            type="primary"
            @click="handleChange"
            size="small"
            style="margin-left: 20px"
            :icon="Aim">AI大模型翻译</el-button>
        </div>
        <div class="content"
          v-if="translationContent">
          {{ translationContent }}
        </div>
        <div class="content"
          v-else>
          <input
            v-model="inputValue"
            style="
              width: 100%;
              border: none;
              outline: none;
              background: rgba(255, 255, 255, 0.7);
            " autosize
            placeholder="请输入内容"
            class="no-border-input" />
        </div>
      </div>
      <div
        class="AiTranslation-main-solid">
      </div>
      <div
        class="AiTranslation-main-right">
        <div class="header">
          结果页</div>
        <div class="content"
          v-loading="loading"
          v-if="resultText">
          {{ resultText }}
        </div>
        <div class="content"
          v-else>
          <el-empty
            description="暂无翻译结果" />
        </div>
      </div>
    </div>
    <div
      class="AiTranslation-footer">
      <div class="operation">
        <el-tooltip
          v-if="typeZ.includes(1)"
          class="box-item"
          effect="light"
          content="喜欢"
          placement="top-start">
          <img
            src="@/assets/images/readerAI/zan_1.png"
            alt=""
            @click="likeFun(1)" />
        </el-tooltip>
        <el-tooltip
          v-if="typeZ.includes(2)"
          class="box-item"
          effect="light"
          content="喜欢"
          placement="top-start">
          <img
            src="@/assets/images/readerAI/zan_1_1.png"
            alt=""
            @click="likeFun(2)" />
        </el-tooltip>

        <el-tooltip
          v-if="typeZ.includes(3)"
          class="box-item"
          effect="light"
          content="不喜欢"
          placement="top-start">
          <img
            src="@/assets/images/readerAI/zan_2.png"
            alt=""
            @click="likeFun(3)" />
        </el-tooltip>
        <el-tooltip
          v-if="typeZ.includes(4)"
          class="box-item"
          effect="light"
          content="不喜欢"
          placement="top-start">
          <img
            src="@/assets/images/readerAI/zan_2_2.png"
            alt=""
            @click="likeFun(4)" />
        </el-tooltip>
        <img
          src="@/assets/images/readerAI/fuzhi.png"
          alt=""
          style="cursor: pointer"
          @click="copyText" />
      </div>
      <div class="explain">
        服务生成的所有内容均由人工智能生成，其生成内容的准确性和完整性无法保证，不代表我们的态度或观点
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, onUnmounted, watch } from 'vue'
import { chartAi, getLanguageList, getAiAssistantUseCount } from '@/api/openApi/openApi'

import { Switch, Aim } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
const props = defineProps({
  translationContent: {
    type: String,
    default: ''
  }
})
const fromLanguage = ref('cn')
const toLanguage = ref('en')
const typeZ = ref([1, 3])
const options = ref([])
const resultText = ref('')
const loading = ref(false)
const inputValue = ref('')
const handleChange = async () => {
  let translationContentValue = ''
  if (props.translationContent) {
    translationContentValue = props.translationContent
  } else {
    translationContentValue = inputValue.value
  }

  loading.value = true
  const { aiExperimentCount } = await getAiAssistantUseCount()
  if (aiExperimentCount > 0) {
    chartAi({
      ability: 23,
      question: translationContentValue,
      fromLanguage: fromLanguage.value,
      toLanguage: toLanguage.value,
      checkBlackWhite: true,
      userType: 0
    }).then(res => {
      if (res.data) {
        resultText.value = res.data.result.trans_result.dst
        loading.value = false
      } else {
        ElMessage.error(res);
      }
    })
  } else {
    loading.value = false
    ElMessage.error('您的免费次数已用完')
  }
}

const getList = () => {
  getLanguageList().then(res => {
    console.log(res)
    if (res.code == 200) {
      options.value = res.data
    }
  })
}

const copyText = async () => {
  try {
    await navigator.clipboard.writeText(resultText.value)
    ElMessage({
      message: '复制成功',
      type: 'success'
    })
  } catch (error) {
    ElMessage({
      message: '复制失败',
      type: 'error'
    })
  }
}
onMounted(() => {
  getList()
  resultText.value = ''
  inputValue.value = ''
})

const likeFun = type => {
  if (type == 1) {
    typeZ.value = [2, 3]
  }
  if (type == 3) {
    typeZ.value = [1, 4]
  }
  ElMessage({
    message: '感谢反馈，我们会继续优化',
    type: 'success'
  })
}

watch(
  () => props.translationContent,
  (oldValue, newValue) => {
    if (oldValue != newValue) {
      resultText.value = ''
      fromLanguage.value = 'cn'
      toLanguage.value = 'en'
    }
  }
)
</script>
