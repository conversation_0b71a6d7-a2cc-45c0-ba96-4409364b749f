<!-- 模块说明 -->
<style lang="scss" scoped>
.AutomaticReadingBox {
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  .item {
    background: #000000;
    opacity: 0.7;
    height: 40px;
    box-sizing: border-box;
    margin: 5px;
    border-radius: 40px;
    & > span {
      font-size: 26px;
      padding: 0 5px;
      &:hover {
        color: var(--hoverfont);
      }
      cursor: pointer;
    }
    .check {
      color: var(--hoverfont);
    }
  }
  .item:nth-child(1) {
    padding: 10px 10px 10px 12px;
  }
  .item:nth-child(2) {
    padding: 10px 5px 10px 5px;
  }
  .item:nth-child(3) {
    padding: 10px 11px 10px 11px;
  }
  .auto-reading-stop {
    width: 20px;
    height: 20px;
    // filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
    filter: brightness(0) invert(1);
  }
}
</style>

<template>
  <DragBox :startOption="startOption" :margins='20' localName="__automaticReading__">
    <div class="AutomaticReadingBox">
      <section class="item __flex-center__">
        自动阅读中：
        <el-dropdown trigger="click">
          <span style="color: #fff; cursor: pointer">
            {{ readingSpeed.title }}
            <el-icon class="el-icon--right">
              <arrow-down />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="ele in speed" :key="ele.value" @click="editSpeed(ele)">{{ ele.title }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </section>
      <section class="item __flex-center__">
        <span class="__flex-center__ check" @click="setautoStatus(autoStatus === 'stop' ? 'start' : 'stop')">
          <el-icon v-if="autoStatus === 'start'"><VideoPause /></el-icon>
          <el-icon v-else><VideoPlay /></el-icon>
        </span>
      </section>
      <section class="item __flex-center__">
        <el-icon @click="stopAutoReading"><Close /></el-icon>
      </section>
    </div>
  </DragBox>
</template>

<script setup>
import { ref, watch } from 'vue'
import DragBox from '@/components/DragDialog/DragBox.vue'
import useReader, { READING_MODE } from '@/store/modules/reader'
import useAutoReading, { AUTO_READING_RUNNING_STATUS } from '@/store/modules/autoReading'

const store = useReader()
const autoReadingStore = useAutoReading()

const startOption = { x: 600, y: 70 }
const speed = [
  { title: '慢速', value: 'slow' },
  { title: '中速', value: 'in' },
  { title: '快速', value: 'fast' },
]
const autoStatus = ref('start')
const setautoStatus = (val) => {
  autoStatus.value = val
  if (val === 'stop') {
    autoReadingStore.pause(true)
  } else {
    autoReadingStore.start(readingSpeed.value)
  }
}
const readingSpeed = ref({ title: '中速', value: 'in' })
const editSpeed = (val) => {
  readingSpeed.value = val
  autoReadingStore.autoTask.branchTask((resolve) => {
    resolve(val)
  })
}
function stopAutoReading() {
  autoReadingStore.destruction()
  store.setReading(READING_MODE.GENERAL)
}

onMounted(() => {
  autoStatus.value = 'start'
  autoReadingStore.init(readingSpeed.value, {
    pageFlippingMethod: store.pageFlippingMethod
  })
})

watch(
  () => [store.reading, store.pageFlippingMethod],
  ([nValue4ReadingMode, nValue4PageFlippingMethod]) => {
    if (!nValue4PageFlippingMethod || !nValue4ReadingMode) {
      return
    }

    if (nValue4ReadingMode === 'automaticReading') {
      autoStatus.value = 'start'
      autoReadingStore.init(readingSpeed.value, {
        pageFlippingMethod: nValue4PageFlippingMethod
      })
    } else {
      autoStatus.value = 'stop'
      autoReadingStore.destruction()
    }
  }
)
watch(
  () => autoReadingStore.readingInProgress,
  (nValue) => {
    if (nValue === AUTO_READING_RUNNING_STATUS.PAUSED) {
      autoStatus.value = 'stop'
    } else {
      autoStatus.value = 'start'
    }
  }
)
</script>
