<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2025-01-17 14:19:53
 * @LastEditTime: 2025-01-18 11:20:47
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Header\BookmarkList.vue
 * @Description 书签列表
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<style lang="scss" scoped>
.BookmarkList {
  width: 350px;
  & > nav {
    font-weight: 400;
    font-size: 16px;
    padding: 10px 0;
    color: var(--fontColor);
  }
  & > header {
    margin: 14px 0;
  }
  & > main {
    overflow-y: auto;
    max-height: calc(100vh - 160px);
    .main-item {
      max-height: 120px;
      border-radius: 3px;
      border: 1px solid #dddddd;
      background-color: var(--modelBackgroundColor);
      padding: 10px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      margin-bottom: 10px;
      .title {
        cursor: pointer;
        font-size: 14px;
      }
      .conent {
        cursor: pointer;
        font-size: 14px;
        margin: 10px 0;
        line-height: 30px;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      & > footer {
        display: flex;
        justify-content: space-between;
        color: #999999;
        .left {
          font-size: 12px;
        }
        .icon {
          cursor: pointer;
          transition: 0.3s;
          &:hover {
            color: var(--hoverfont);
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="BookmarkList">
    <nav>书签列表</nav>
    <!--  暂时去掉 -->
    <!-- <header>
      <el-select
        v-model="selectValue"
        placeholder="请选择章节"
        :clearable="true"
        :teleported="false"
      >
        <el-option
          v-for="item in optionsComputed"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </header> -->
    <main>
      <div
        class="main-item"
        v-for="(ele, index) in bookmarkList"
        :key="ele.markId"
      >
        <div class="title" @click="navToPageByPageIndexInBook(ele)">
          {{ ele.chapterName }}
        </div>
        <div class="conent" @click="navToPageByPageIndexInBook(ele)">
          {{ ele.pageContent }}
        </div>
        <footer>
          <div class="left">
            位置：{{
              store.getCumulatedPageCntInPreviousChapters(ele.chapterId) +
              ele.pageNumber
            }}页
          </div>
          <el-popconfirm
            v-model:visible="ele._showPopconfirm"
            title="确定要删除吗？"
            confirm-button-text="确定"
            cancel-button-text="取消"
            @confirm="handleConfirm($event, ele)"
            @cancel="handleCancel($event, ele)"
          >
            <template #reference>
              <el-icon class="icon" @click="ele._showPopConfirm = true"
                ><Delete
              /></el-icon>
            </template>
          </el-popconfirm>
        </footer>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import useReader from "@/store/modules/reader";
import { removeBookMark, getBookMark } from "@/api/book/reader";
import { sortBookmarkList } from "@/utils/reader";
import { ElMessage } from "element-plus";

function handleCancel(event, ele) {
  event.preventDefault();
  event.stopPropagation();
  event.cancelBubble = false;
  // ele._showPopConfirm = false
}
function handleConfirm(event, ele) {
  event.stopPropagation();
  delBookMark(ele);
}
const store = useReader();
const selectValue = ref("");
const optionsComputed = computed(() => {
  const catalogList = store.comprehensiveChapterAndCatalogData.chaptersData.map(
    (chapterItem) => {
      return {
        ...chapterItem,
        value: chapterItem.chapterId,
        label: chapterItem.chapterName,
      };
    }
  );
  catalogList.unshift({
    value: "",
    label: "全部章节",
  });
  return catalogList;
});
const bookmarkList = ref([]);

watch(
  () => [
    store.bookMarkData,
    store.comprehensiveChapterAndCatalogData,
    selectValue,
  ],
  (nvalue) => {
    let filteredBookmarkData = [];

    if (nvalue[0]?.length > 0) {
      // 容错代码
      filteredBookmarkData = nvalue[0];
    }
    if (nvalue[1]?.chaptersData?.length <= 0) {
      // 容错代码
      bookmarkList.value = filteredBookmarkData;
      return;
    }
    const catalogDataList = nvalue[1].chaptersData ?? [];
    if (selectValue.value === "") {
      filteredBookmarkData = sortBookmarkList(
        filteredBookmarkData,
        catalogDataList
      );
    } else {
      filteredBookmarkData = filteredBookmarkData.filter(
        (bookmark) => bookmark.chapterId === selectValue.value
      );
      filteredBookmarkData = sortBookmarkList(
        filteredBookmarkData,
        catalogDataList
      );
    }
    bookmarkList.value = filteredBookmarkData;
  },
  { deep: true }
);

// const selectChange = (e) => {
//   if (e) {
//     bookmarkList.value = store.bookMarkData.filter((ele) => ele.chapterName === e)
//   } else {
//     bookmarkList.value = store.bookMarkData
//   }
// }
/**获取书签列表*/
const getBookMarks = () => {
  getBookMark(store.comprehensiveBookData.bookId).then((res) => {
    if (res.code === 200) store.bookMarkData = res.data;
  });
};

/**删除书签*/
const delBookMark = async (v) => {
  const res = await removeBookMark(v.markId);
  if (res.code === 200) {
    ElMessage.success("删除书签成功！");
    getBookMarks();
  } else {
    ElMessage.error("操作失败，请检查后重试！");
  }
};

/**跳转页面*/
const navToPageByPageIndexInBook = (bookmarkItem) => {
  store.jumpToChapter(
    bookmarkItem.chapterId,
    Number.parseInt(bookmarkItem.pageNumber)
  );
};
</script>
