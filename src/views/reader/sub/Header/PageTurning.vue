<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-16 09:54:38
 * @LastEditTime: 2025-01-15 09:39:02
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Header\PageTurning.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 分页跳转 -->
<style lang="scss" scoped>
.PageTurning {
  display: flex;
  align-items: center;
  .PageTurning-input {
    display: flex;
    align-items: center;
  }
  .progress-label {
    margin: 0 10px;
    font-size: 12px;
  }
  .progress {
    position: relative;
    width: 176px;
  }
}
</style>

<template>
  <div class="PageTurning">
    <el-button link
      :disabled="store.isFirstChapter()"
      @click="store.jumpToPreviousChapter"
      title="上一章">
      <svg-icon
        iconClass="previousSong" />
    </el-button>
    <el-button link
      :icon="ArrowLeftBold"
      :disabled="store.comprehensiveBookData.currentPageIndex === 1"
      @click="store.lastPage"
      title="上一页" />

    <div
      class="PageTurning-input"
      @click="getFocus">
      &nbsp;&nbsp;
      <el-form
        v-show="forms.inputStatus"
        style="height: 32px; margin: 0 5px">
        <el-form-item>
          <el-input
            v-model="forms.num"
            style="width: 60px"
            @blur="blur"
            ref="iptRefs"
            @keyup.enter="enter" />
        </el-form-item>
        <el-form-item
          v-show="false">
          <!-- 多添加一个input阻止表单默认提交事件 -->
          <el-input />
        </el-form-item>
      </el-form>

      <span
        v-show="!forms.inputStatus"><u>{{ store.comprehensiveBookData.currentPageIndex }}</u></span>
      <span>/</span>
      <span>{{ store.comprehensiveBookData.totalPages }}</span>
      &nbsp;&nbsp;
    </div>

    <el-button link
      :icon="ArrowRightBold"
      :disabled="
        store.comprehensiveBookData.currentPageIndex ===
        store.comprehensiveBookData.totalPages
      "
      @click="store.nextPage"
      title="下一页" />
    <el-button link
      :disabled="store.isLastChapter()"
      @click="store.jumpToNextChapter"
      title="下一章">
      <svg-icon
        iconClass="nextSong" />
    </el-button>

    <div
      class="progress-label"
      v-if="showReadingProgress">
      阅读进度</div>
    <div class="progress"
      v-if="showReadingProgress">
      <el-progress
        :percentage="percentage"
        color="#0966b4">
        <span
          slot="suffix">{{ Math.round(percentage) }}%</span>
      </el-progress>
    </div>
  </div>
</template>

<script setup>
import { ArrowLeftBold, ArrowRightBold } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { reactive, ref, defineProps } from 'vue'
import useReader from '@/store/modules/reader'
// import { PAGE_TURNING_ERROR_MAPPER } from "@/utils/reader";
// import SpeedOfProgress from './SpeedOfProgress.vue'

const props = defineProps({
  showReadingProgress: {
    type: Boolean,
    default: true
  }
})

const store = useReader()
const iptRefs = ref(null)

const forms = reactive({
  inputStatus: false,
  num: ''
})
const blur = () => {
  forms.inputStatus = false
}
const getFocus = () => {
  forms.inputStatus = true
  forms.num = store.comprehensiveBookData.currentPageIndex
  iptRefs.value.focus()
}
const enter = () => {
  const val = forms.num
  if (!/^[1-9]\d*$/.test(val)) {
    forms.num = store.comprehensiveBookData.currentPageIndex
    return ElMessage.error('页码必须为数字')
  }
  if (val <= 0) {
    forms.num = store.comprehensiveBookData.currentPageIndex
    return ElMessage.error('页码必须大于0')
  }
  if (val > store.comprehensiveBookData.totalPages) {
    forms.num = store.comprehensiveBookData.currentPageIndex
    return ElMessage.error('页码不能大于总页数')
  }
  store.jumpToPage(val)
  forms.inputStatus = false
}

const percentage = computed(() => {
  return (store.comprehensiveBookData.currentPageIndex / store.comprehensiveBookData.totalPages) * 100
})
</script>
