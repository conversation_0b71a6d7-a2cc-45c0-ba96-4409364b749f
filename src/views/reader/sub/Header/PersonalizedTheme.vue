<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-23 10:29:55
 * @LastEditTime: 2024-12-26 09:30:05
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\PersonalizedTheme.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 个性主题 -->
<style lang="scss" scoped>
.PersonalizedTheme {
  .PersonalizedTheme-item {
    margin: 20px 0;
    .PersonalizedTheme-title {
      height: 100%;
      font-weight: bold;
    }
    .PersonalizedTheme-background {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      border: 1px solid #666;
      cursor: pointer;
    }
  }
}
</style>

<template>
  <div
    class="PersonalizedTheme">
    <div
      class="PersonalizedTheme-item">
      <el-row>
        <el-col :span="4">
          <div
            class="PersonalizedTheme-title __flex-center__">
            主题色：
          </div>
        </el-col>
        <el-col :span="10">
          <div
            class="__flex-center__">
            <el-button link
              :type="store.theme === 'light' ? 'primary' : ''"
              @click="setTheme('light')">白天模式</el-button>
          </div>
        </el-col>
        <el-col :span="10">
          <div
            class="__flex-center__">
            <el-button link
              :type="store.theme === 'dark' ? 'primary' : ''"
              @click="setTheme('dark')">黑夜模式</el-button>
          </div>
        </el-col>
      </el-row>
    </div>
    <div
      class="PersonalizedTheme-item">
      <el-row>
        <el-col :span="4">
          <div
            class="PersonalizedTheme-title __flex-center__">
            背景色：
          </div>
        </el-col>
        <el-col :span="4"
          v-for="ele in backgroundList"
          :key="ele">
          <div
            class="PersonalizedTheme-background"
            :style="`background-color:${ele}`"
            @click="tabPageTheme(ele)">
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import useReader from '@/store/modules/reader'
import { saveBookConfig } from '@/api/book/reader'
import { getToken } from '@/utils/auth'
const props = defineProps({
  simplePreviewMode: {
    type: Boolean,
    default: false
  }
})
const store = useReader()
const backgroundList = ['#fff', '#F7E9CF', '#DEF3E2', '#DEEEF3', '#F3DEDE']
function doSaveConfig(data) {
  if (!getToken()) return
  if (!props.simplePreviewMode) {
    saveBookConfig(data).then(res => {
      if (res.code !== 200) {
        ElMessage.error(res.msg)
      }
    })
  }
}
const setTheme = val => {
  store.theme = val
  // 只要切换主题模式，背景色变为白色
  const data = { configId: store.configId, theme: val, bgColor: '#fff' }
  doSaveConfig(data)

  store.themeBackground = '#fff'
}

const tabPageTheme = val => {
  if (store.theme === 'dark') return ElMessage.error('黑夜模式不允许切换背景色!')
  store.themeBackground = val
  const data = { configId: store.configId, bgColor: val }
  doSaveConfig(data)
}
</script>
