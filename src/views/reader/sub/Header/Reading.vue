<!-- 阅读模式 -->
<style lang="scss" scoped></style>

<template>
  <span
    style="font-weight: bold">阅读模式：</span>
  <el-button round
    v-for="ele in readingMode"
    :key="ele.type"
    :type="ele.type === store.reading ? 'primary' : ''"
    @click="tabMode(ele.type)">{{ ele.modeName }}</el-button>
</template>

<script setup>
import { defineEmits, defineProps } from 'vue'
import useReader, { READING_MODE } from '@/store/modules/reader'
import useAutoReading from '@/store/modules/autoReading'
import { ElMessage } from 'element-plus'
import { saveBookConfig } from '@/api/book/reader'
import { getToken } from '@/utils/auth'
const props = defineProps({
  simplePreviewMode: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['selectChanged'])
const store = useReader()
const autoReadingStore = useAutoReading()

function doSaveConfig(data) {
  if (!getToken()) return
  saveBookConfig(data).then(res => {
    if (res.code !== 200) {
      ElMessage.error(res.msg)
    }
  })
}
const readingMode = [
  {
    modeName: '阅读模式',
    type: 'reading'
  },
  {
    modeName: '专注模式',
    type: 'absorbed'
  },
  {
    modeName: '自动阅读',
    type: 'automaticReading'
  }
]

const tabMode = type => {
  store.setReading(type)
  if (type === READING_MODE.GENERAL) {
    store.closeAbsorbed()
    autoReadingStore.pause()
    autoReadingStore.destruction()
  } else if (type === READING_MODE.ABSORBED) {
    autoReadingStore.pause()
    autoReadingStore.destruction()
  } else {
    store.closeAbsorbed()
  }
  if (props.simplePreviewMode) {
    const data = { configId: store.configId, readMode: type }
    doSaveConfig(data)
  }
  emits('selectChanged', type)
}

watch(
  () => store.reading,
  nValue => {
    if (nValue) {
      tabMode(nValue)
    }
  }
)
</script>
