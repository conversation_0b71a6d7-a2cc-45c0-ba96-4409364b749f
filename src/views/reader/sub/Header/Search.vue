<!-- 阅读模式 -->
<style lang="scss" scoped>
.main {
  padding: 20px 10px;
  .search_title {
    margin: 0 0 10px;
    font-size: 16px;
    font-weight: 600;
  }
  .history_title {
    margin-top: 20px;
    font-size: 14px;
    font-weight: 600;
  }
  .history_container {
    overflow-y: auto;
    .el-collapse {
      border-top: none;
    }
    height: calc(100vh - 260px);
    ::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
    scrollbar-width: none; /* Firefox 64+ */
    -ms-overflow-style: none; /* IE/Edge */

    .item {
      padding: 10px 0;
      .sentence {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        line-height: 25px;
        padding: 0 0 0 16px;
        &:hover {
          color: #0966b4;
          cursor: pointer;
        }
      }
      .page {
        margin-top: 5px;
        font-size: 12px;
        color: #999;
        text-align: right;
      }
    }
  }

  .btn {
    width: 100%;
    strong {
      padding: 0 5px;
    }
  }
}
::v-deep .el-collapse-item__header {
  line-height: 23px !important;
}
</style>

<template>
  <div
    class="main search-in-reader">
    <div class="search_title">
      搜索</div>
    <el-input
      clearable
      v-model="searcherText"
      @keyup.enter="search"
      @clear="clearSearchHandler"
      style="max-width: 600px"
      placeholder="请输入搜索内容"
      class="input-with-select">
      <template #append>
        <el-button
          :icon="Search"
          @click="search" />
      </template>
    </el-input>

    <div
      v-if="chapterGroupQueryList.length == 0">
      <el-empty
        :image-size="100" />
    </div>
    <div v-else
      class="history_container">
      <el-collapse
        v-model="activeNames"
        @change="handleChange"
        v-if="(chapterGroupQueryList && chapterGroupQueryList.length > 0)"
        accordion>
        <el-collapse-item
          :title="`${currentChapterData.chapterId === searchChapterGroupData.chapterId ? '(当前章)':''} ${searchChapterGroupData.chapterName} (${searchChapterGroupData.count}个搜索结果)`"
          :name="searchChapterGroupData.chapterId"
          v-for="(searchChapterGroupData) in chapterGroupQueryList">
          <div
            v-for="(searchedKeyParagraph) in searchChapterGroupData.data"
            class="item">
            <div
              class="sentence"
              @click="goToRelevantParagraph(searchChapterGroupData.chapterId, searchedKeyParagraph.pageNumber, searchedKeyParagraph)">
              {{ searchedKeyParagraph.sentence }}
            </div>
            <div class="page">
              所在页码：<b>{{ store.getCumulatedPageCntInPreviousChapters(searchChapterGroupData.chapterId) + searchedKeyParagraph.pageNumber }}</b>
            </div>
          </div>
          <el-empty
            :image-size="100"
            v-if="searchChapterGroupData.data.length == 0" />
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue'
import { getQueryChapterContent } from '@/api/book/reader.js'
import { highlightKeyWordSmooth, sortSearchedChapterGroupList } from '@/utils/reader'
import useReader from '@/store/modules/reader'
import useAutoReading from '@/store/modules/autoReading'
import { generateTextId } from '@/views/reader/sub/Pages/ParagraphTool'
import { ElMessage } from 'element-plus'
import { onMounted, onBeforeMount } from 'vue'

const searcherText = ref('')
const store = useReader()
const autoReadingStore = useAutoReading()
const currentChapterData = ref(null)
const chapterGroupQueryList = ref([])

const activeNames = ref('')
const handleChange = expandedChapterId => {
  if (expandedChapterId) {
    getDataList(store.comprehensiveBookData.bookId, expandedChapterId, searcherText.value)
  }
}

const previousKeyWords = []
const getDataList = async (bookId, chapterId, keyword) => {
  const request = {
    bookId,
    chapterId,
    keyword
  }
  const res = await getQueryChapterContent(request)
  activeNames.value = chapterId
  if (res.code === 200) {
    if (previousKeyWords[previousKeyWords.length - 1] !== keyword) {
      previousKeyWords.push(keyword)
    }
    let targetChapterGroupItem = chapterGroupQueryList.value.find(chapterGroupItem => chapterGroupItem.chapterId === chapterId)
    if (!targetChapterGroupItem) {
      targetChapterGroupItem = {
        chapterId,
        chapterName: currentChapterData.value.chapterName,
        count: res.current.length,
        data: []
      }
      chapterGroupQueryList.value.push(targetChapterGroupItem)
    }
    targetChapterGroupItem.data = res.current || []
    res.others?.forEach(otherChapterItem => {
      let targetChapterGroupItemOther = chapterGroupQueryList.value.find(item => item.chapterId === otherChapterItem._id)
      if (!targetChapterGroupItemOther) {
        targetChapterGroupItemOther = {
          chapterId: otherChapterItem._id,
          chapterName: otherChapterItem.chapterName,
          count: otherChapterItem.count,
          data: []
        }
        chapterGroupQueryList.value.push(targetChapterGroupItemOther)
      }
    })
    chapterGroupQueryList.value = sortSearchedChapterGroupList(
      chapterGroupQueryList.value,
      store.comprehensiveChapterAndCatalogData.chaptersData
    )
    // console.log('chapterGroupQueryList:', chapterGroupQueryList.value)
  } else {
    ElMessage.error('搜索失败, 请稍后尝试')
  }
}

function goToRelevantParagraph(jumpToChapterId, pageIndexInChapter, searchItem) {
  const contextParagraph = searchItem.sentence
  const keywordIndex = contextParagraph.indexOf(searcherText.value)
  const targetTextNodeId = generateTextId({
    paraId: searchItem.textId,
    startIndex: keywordIndex,
    keywordLength: searcherText.value.length
  })
  store.jumpToChapter(jumpToChapterId, pageIndexInChapter).then(() => {
    highlightKeyWordSmooth(targetTextNodeId)
  })
}

const search = () => {
  if (!searcherText.value) {
    return ElMessage.error('请输入搜索内容')
  }
  // 重新搜索关键字时，重置搜索结果
  chapterGroupQueryList.value = []
  // 如果搜索了关键字，自动阅读先暂停
  autoReadingStore.pause()
  getDataList(store.comprehensiveBookData.bookId, currentChapterData.value.chapterId, searcherText.value)
}

watch(
  () => store.chapterId,
  (nValue, oValue) => {
    if (nValue) {
      // searcherText.value = ''
      // chapterGroupQueryList.value = []
      currentChapterData.value = store.getChapterCatalogDataByChapterId(nValue)
    }
  }
)

watch(
  () => store.comprehensiveBookData.bookId,
  (nValue, oValue) => {
    // 主教材更换副教材时候重置搜索结果
    chapterGroupQueryList.value = []
  }
)

onMounted(() => {
  // console.log('onMounted')
})
onBeforeMount(() => {
  // console.log('onBeforeMount')
})

function clearSearchHandler() {
  searcherText.value = ''
  chapterGroupQueryList.value = []
}
</script>
