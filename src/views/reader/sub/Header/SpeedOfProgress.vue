<!-- 该文件貌似没用到 -->
<!-- 全书进度 -->
<style lang="scss" scoped>
.SpeedOfProgress {
  width: 100%;
  height: 4px;
  background-color: var(--speedOfProgressColor);
  position: relative;
  transition: 0.3s;
  .SpeedOfProgress-main {
    position: absolute;
    height: 4px;
    left: 0;
    width: 0;
    background-color: #0966b4;
    transition: 1.5s;
  }
  &:hover {
    box-shadow: var(--boxShadow);
  }
}
</style>

<template>
  <el-tooltip class="box-item" :effect="store.theme" content="已阅读33.33%" placement="bottom">
    <div class="SpeedOfProgress">
      <div class="SpeedOfProgress-main" :style="`width:${speedOfProgress || 0}%`"></div>
    </div>
  </el-tooltip>
</template>

<script setup>
import { computed } from 'vue'
import useReader from '@/store/modules/reader'
const store = useReader()
const speedOfProgress = computed(() => {
  return ((store.comprehensiveBookData.currentPageIndex + 1) / store.pageNumber) * 100 || 0
})
</script>
