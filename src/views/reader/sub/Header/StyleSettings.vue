<!-- 样式设置 -->
<style lang="scss" scoped>
.StyleSettings {
  .StyleSettings-title {
    font-weight: bold;
    height: 100%;
  }

  .StyleSettings-item {
    margin: 10px 0;

    .item-ipt {
      display: flex;

      .ipt {
        margin-left: 15px;
      }

      .slider {
        width: 140px;
      }
    }
  }

  .StyleSettings-btn {
    width: 87px;
  }
}

.lineHeightStyle {
  width: 30px;
  list-style: none;
  border: 0;
  background-color: rgba(0, 0, 0, 0);
  margin-right: 5px;
  color: #f7f7f7;
}
</style>

<template>
  <div class="StyleSettings">
    <div class="StyleSettings-item">
      <el-row>
        <el-col :span="5">
          <div class="StyleSettings-title __flex-center__">字体：</div>
        </el-col>
        <el-col :span="6"><el-button round class="StyleSettings-btn"
            :type="styleConfig['font-family'] === '0' ? 'primary' : ''" @click="setFamily('0')">默认</el-button></el-col>
        <el-col :span="6"><el-button round class="StyleSettings-btn"
            :type="styleConfig['font-family'] === 'SimHei' ? 'primary' : ''"
            @click="setFamily('SimHei')">非衬线字体</el-button></el-col>
        <!-- font-family: "SimHei", "黑体"; -->
        <el-col :span="6"><el-button round class="StyleSettings-btn"
            :type="styleConfig['font-family'] === 'SimSun' ? 'primary' : ''"
            @click="setFamily('SimSun')">衬线字体</el-button></el-col>
        <!-- font-family: "SimSun", "宋体", serif; -->
      </el-row>
    </div>
    <div class="StyleSettings-item">
      <el-row>
        <el-col :span="5">
          <div class="StyleSettings-title __flex-center__">字号：</div>
        </el-col>
        <el-col :span="6"><el-button round class="StyleSettings-btn"
            :type="styleConfig['font-size'] == '-1px' ? 'primary' : ''" @click="setSize(-1)">默认</el-button></el-col>
        <el-col :span="12">
          <div class="item-ipt">
            <!-- <el-slider v-model="fontSize" :min="14" :max="24" @input="change" class="slider" />
            <el-input-number v-model="fontSize" :min="14" :max="24" class="ipt" @change="change" /> -->
            <el-select @change="change" v-model="fontSize" placeholder="自定义字号" style="width: 200px">
              <el-option v-for="item in fontSizeList" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="StyleSettings-item">
      <el-row>
        <el-col :span="5">
          <div class="StyleSettings-title __flex-center__">行距：</div>
        </el-col>
        <el-col :span="6"><el-button round class="StyleSettings-btn"
            :type="styleConfig['line-height'] == -1 ? 'primary' : ''" @click="setLineHeight(-1)">默认</el-button></el-col>
        <el-col :span="6"><el-button round class="StyleSettings-btn"
            :type="styleConfig['line-height'] === 1.8 ? 'primary' : ''"
            @click="setLineHeight(1.8)">1.8倍</el-button></el-col>
        <el-col :span="6"><el-button round class="StyleSettings-btn"
            :type="styleConfig['line-height'] !== 1.8 && styleConfig['line-height'] != -1 ? 'primary' : ''"
            @click="setLineHeight(lineHeight)">
            <input class="lineHeightStyle" v-model="lineHeight" @blur="lineHeightBlur" />
            倍</el-button></el-col>
      </el-row>
    </div>
    <!-- <div class="StyleSettings-item">
      <el-row>
        <el-col :span="6"><div class="StyleSettings-title __flex-center__">布局：</div></el-col>
        <el-col :span="9"><el-button round class="StyleSettings-btn" :type="store.flex === 'one' ? 'primary' : ''" @click="setFlex('one')">单栏</el-button></el-col>
        <el-col :span="9"><el-button round class="StyleSettings-btn" :type="store.flex === 'two' ? 'primary' : ''" @click="setFlex('two')">双栏</el-button></el-col>
      </el-row>
    </div> -->
    <div class="StyleSettings-item">
      <el-row>
        <el-col :span="5">
          <div class="StyleSettings-title __flex-center__">翻页方式：</div>
        </el-col>
        <el-col :span="6"><el-button round class="StyleSettings-btn"
            :type="store.pageFlippingMethod === 'y' ? 'primary' : ''" @click="tabsPageTurning('y')"
            :disabled="store.flex === 'two'">
            上下滑动
          </el-button>
        </el-col>
        <!-- <el-col :span="6"
          ><el-button round class="StyleSettings-btn" :type="store.pageFlippingMethod === 'x' ? 'primary' : ''" @click="tabsPageTurning('x')">横向翻页</el-button></el-col
        > -->
        <el-col :span="6"><el-button round class="StyleSettings-btn"
            :type="store.pageFlippingMethod === 'r' ? 'primary' : ''"
            @click="tabsPageTurning('r')">真实翻页</el-button></el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { computed,watchEffect, ref, onMounted, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import useReader, { READING_MODE } from '@/store/modules/reader'
import useAutoReading from '@/store/modules/autoReading'
import { saveBookConfig } from '@/api/book/reader'
import { getToken } from '@/utils/auth'

const props = defineProps({
  simplePreviewMode: {
    type: Boolean,
    default: false
  }
})

const store = useReader()
const autoReadingStore = useAutoReading()
const emits = defineEmits(['selectChanged'])

const styleConfig = computed(() => store.styleSetting)
const fontSize = ref()
const lineHeight = ref(2)
const fontSizeList = ref(Array.from({ length: 24 - 14 + 1 }, (_, idx) => idx + 14)
)
// const { proxy } = getCurrentInstance()
// const setFlex = (val) => {
//   store.setFlex(val)
//   const data = { configId: store.configId, columnQuantity: val == 'one' ? 1 : 2 }
//   doSaveConfig(data)
// }
// 设置字体
const setFamily = family => {
  store.styleSetting['font-family'] = family
  const data = { configId: store.configId, fontFamily: family }
  doSaveConfig(data)
}
// 设置字体大小为默认
const setSize = size => {
  store.styleSetting['font-size'] = size + 'px'
  const data = { configId: store.configId, fontSize: size }
  doSaveConfig(data)
}
function doSaveConfig (data) {
  // console.log('doSaveConfig', data)
  if (!getToken()) return
  if (!props.simplePreviewMode) {
    saveBookConfig(data).then(res => {
      if (res.code !== 200) {
        ElMessage.error(res.msg)
      }
    })
  }
}
// 设置字体大小
const change = val => {
  store.styleSetting['font-size'] = val + 'px'
  const data = { configId: store.configId, fontSize: val }
  doSaveConfig(data)
}
// 设置行高
const setLineHeight = num => {
  if ((/^\d+(\.\d+)?$/.test(num) && num - 0 !== 0) || num == -1) {
    store.styleSetting['line-height'] = num
    const data = { configId: store.configId, lineHeight: num }
    doSaveConfig(data)
  }
}

const lineHeightBlur = e => {
  const val = parseFloat(e.target.value)

  if (/^\d+(\.\d+)?$/.test(val) && val - 0 !== 0) {
    if (val > 3 || val < 1) {
      lineHeight.value = 2
      return ElMessage.error('请输入1-3之间的数值')
    }
    setLineHeight(val - 0)
  } else {
    setTimeout(() => {
      if (styleConfig.value['line-height'] !== 1.5 && styleConfig.value['line-height'] !== 1.8) {
        ElMessage.error('请输入1-3之间的数值')
        lineHeight.value = 2
      }
    }, 100)
  }
}

const tabsPageTurning = val => {
  // 切换翻页方式的时候，需要关停自动阅读。因为目前计算当前页码的方式，依赖于页面容器内所有页的DOM节点，需要计算他们的高度，而上下滑动、真实翻页形式对DOM的处理相差很大。只能等待DOM处理完毕后，继续自动阅读，目前代价比较大
  // nexTick就是为了切换翻页方式后，DOM处理完毕，才更新store数据
  autoReadingStore.destruction()
  store.setPageFlippingMethod(val)
  store.setReading(READING_MODE.GENERAL)
  const data = { configId: store.configId, pageFlippingMethod: val }
  doSaveConfig(data)
  emits('selectChanged')
}
// onMounted(() => {
//   fontSize.value = styleConfig.value['font-size'].replace('px', '') - 0
// })

watchEffect(() => {
  fontSize.value = parseFloat(styleConfig.value['font-size']) == -1 ? '' : parseFloat(styleConfig.value['font-size'])
})
</script>
