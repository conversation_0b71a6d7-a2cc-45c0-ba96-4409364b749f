<!-- 左侧菜单 -->
<style lang="scss" scoped>
.Menus {
  width: 300px;
  height: calc(100vh - 58px);
  position: fixed;
  left: 18px;
  z-index: 1;
  background-color: var(--pageBackgroundColor);
  .Menus-bookName {
    margin-bottom: 22px;
    margin-top: 18px;
  }

  .Menus-tabs {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 34px;
    font-weight: 400;
    font-size: 14px;
    color: var(--fontColor);
    background-color: var(--selectMenuTabs);
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    .Menus-tabs-item {
      flex: 1;
      height: 100%;
      cursor: pointer;
      transition: 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;
      .catalogueIcon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 14px;
        height: 16px;
        background: url("@/assets/images/catalogueIcon.svg") no-repeat;
        background-size: 100%;
        margin-right: 5px;
        margin-top: 2px;
      }
      .knowledgeIcon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 14px;
        height: 16px;
        background: url("@/assets/images/knowledgeIcon.svg") no-repeat;
        background-size: 100%;
        margin-right: 5px;
        margin-top: 2px;
      }
    }
    .select {
      background-color: var(--modelBackgroundColor);
      font-weight: 500;
      font-size: 16px;
      color: var(--fontColor);
      line-height: 16px;
    }
    .triangle-catalogue {
      position: absolute;
      width: 0;
      height: 0;
      border-right: 20px solid transparent; /* 调整这个值可以改变三角形的水平大小 */
      border-bottom: 35px solid var(--modelBackgroundColor); /* 调整这个值可以改变三角形的竖直大小 */
      left: 50%;
    }
    .triangle-graph {
      position: absolute;
      width: 0;
      height: 0;
      border-left: 20px solid transparent; /* 调整这个值可以改变三角形的水平大小 */
      border-bottom: 35px solid var(--modelBackgroundColor); /* 调整这个值可以改变三角形的竖直大小 */
      left: 50%;
      transform: translateX(-100%);
    }
  }

  .Menus-main {
    border-radius: 0 0 8px 8px;
    background-color: var(--modelBackgroundColor);
    height: calc(100% - 93px);
    padding: 20px;
    box-sizing: border-box;
    box-shadow: var(--boxShadow);
  }
}
.menu-box {
  width: 300px;
  position: absolute;
  z-index: 1;
  transition: 0.3s;
}
.Menus-show {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid #eaeaea;
  position: absolute;
  left: 300px;
  top: 20vh;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: var(--modelBackgroundColor);
  cursor: pointer;
  font-size: 20px;
  padding: 3px;
  box-sizing: border-box;
}
</style>
<template>
  <div
    class="menu-box"
    :style="{
      transform: store.menuIsHide ? 'translateX(-318px)' : 'translateX(0)',
    }"
  >
    <div class="Menus">
      <div class="Menus-bookName">
        <el-dropdown trigger="click" :teleported="false">
          <span
            style="
              color: var(--fontColor);
              cursor: pointer;
              font-size: 16px;
              font-weight: bold;
            "
          >
            {{ store.comprehensiveBookData.bookName }}
            <el-icon class="el-icon--right" v-if="showRelatedBooks">
              <arrow-down />
            </el-icon>
          </span>
          <template #dropdown v-if="showRelatedBooks">
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="ele in relatedTextbooksList"
                :key="ele.bookId"
                @click="bookChanged(ele, $event)"
                >{{ ele.bookName }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <div class="Menus-tabs" ref="menusTabsRef">
        <div
          :class="`Menus-tabs-item __flex-center__ ${selectTabs === 'catalogue' ? 'select' : ''}`"
          @click="tabTabs('catalogue')"
        >
          <i class="catalogueIcon"></i>
          目录导航
        </div>
        <div class="triangle-catalogue" v-if="showKnowledgeGraph"></div>
        <div class="triangle-graph" v-if="showKnowledgeGraph"></div>
        <div
          v-if="showKnowledgeGraph"
          :class="`Menus-tabs-item __flex-center__ ${selectTabs === 'graph' ? 'select' : ''}`"
          @click="tabTabs('graph')"
        >
          <i class="knowledgeIcon"></i>
          知识图谱导航
        </div>
      </div>

      <div class="Menus-main">
        <MenusContent v-if="selectTabs === 'catalogue'" ref="menusContentRef" :init-catalog-id="initCatalogId" />
        <span v-else>努力开发中</span>
      </div>
    </div>
    <div class="Menus-show" @click="store.menuIsHide = !store.menuIsHide">
      <el-icon v-if="store.menuIsHide"><DArrowRight /></el-icon>
      <el-icon v-else><DArrowLeft /></el-icon>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, defineProps } from "vue";
import { DArrowRight, DArrowLeft } from "@element-plus/icons-vue";
import MenusContent from "./MenusContent/index.vue";
import { getBooks } from "@/api/book/reader";
import useReader from "@/store/modules/reader";
import { useRouter } from "vue-router";

const props = defineProps({
  initCatalogId: {
    type: String,
    default: ""
  },
  showRelatedBooks: {
    type: Boolean,
    default: true
  },
  showKnowledgeGraph: {
    type: Boolean,
    default: true
  }
})

const router = useRouter();

const store = useReader();
const relatedTextbooksList = computed(() => {
  return store.relatedBooks.map(book => {
    return {
      ...book,
      value: book.bookId,
      label: book.bookName
    }
  })
})
const menusTabsRef = ref(null);
const menusContentRef = ref(null);
const selectTabs = ref("catalogue"); //catalogue || graph
const tabTabs = (type) => {
  selectTabs.value = type;
};
const getRelatedTextbooks = async () => {
  const res = await getBooks(store.comprehensiveBookData.bookId);
  if (res.code === 200) {
    store.setRelatedBooks(res.data)
    if (!store.comprehensiveBookData.bookName) {
      const targetBook = res.data.find(bookItem => bookItem.bookId === store.comprehensiveBookData.bookId)
      if (targetBook) {
        store.setCommonData({
          bookName: targetBook.bookName
        })
      }
    }
  }
};
function bookChanged(bookValue, $event) {
  if (store.comprehensiveBookData.bookId === bookValue.bookId) {
    return;
  }
  store.deauthorizeToTheBook();

  router.replace({
    path: "/reader",
    query: {
      k: bookValue.bookId,
    },
  });
}
watch(
  () => store.comprehensiveBookData.bookId,
  (val) => {
    if (val) {
      getRelatedTextbooks();
    }
  }
);

// onMounted(() => {
//   console.log("menusContentRef", menusContentRef.value.menuStatusRef);
// });

defineExpose({
  menusTabsRef,
  menusContentRef,
});
</script>
