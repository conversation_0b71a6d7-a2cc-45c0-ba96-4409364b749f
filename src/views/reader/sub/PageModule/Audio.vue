<!-- 模块说明 -->
<style lang="less" scoped>
.audio {
  margin: 20px 0 0;
  width: 100%;
  height: auto;

  &:hover {
    box-shadow: var(--umo-shadow);
  }
  .audio-play {
    width: 100%;
    height: 50px;
  }
}
.audio-name {
  padding: 5px 0;
  text-align: center;
  font-size: 1.125rem;
  color: #333;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 超出容器部分的文本隐藏 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
}
</style>

<template>
  <div class="audio" :id="id">
    <div class="audio-main" id="__audio__">
      <audio
        class="audio-play"
        oncontextmenu="return false"
        :src="src"
        preload="auto"
        controls
        controlsList="nodownload"
        crossorigin="anonymous"
        @loadedmetadata="onloadedmetadata"
      ></audio>
      <div class="audio-name __ellipsis" :style="{ width: width + 'px' }">
        {{ audioTitle }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, ref } from "vue";

const props = defineProps({
  height: String,
  src: String,
  checkTabs: String,
  nodeAlign: String,
  audioTitle: String,
  id: String,
});

let duration = ref("");
function secondsToMinutesAndSeconds(seconds) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = (seconds % 60).toFixed(0);
  return `${minutes}:${remainingSeconds}`;
}

const onloadedmetadata = (e) => {
  duration.value = secondsToMinutesAndSeconds(e.target.duration);
};
</script>
