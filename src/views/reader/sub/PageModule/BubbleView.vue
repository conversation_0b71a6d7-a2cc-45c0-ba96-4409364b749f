<style lang="less" scoped>
.slotCss {
  font-size: 14px;
  line-height: 25px;
  position: relative;
  top: 10px;
  left: -20px;
  
  .arrow {
    position: absolute;
    bottom: -3px;
    left: 20px;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid red;
  }
}

.umo-popup__arrow {
  bottom: -3px;
}

.bubble-main {
  text-indent: 0;
  margin-right: 0px;
}

.bubble-url {
  text-align: right;
}
.bubbleIcon {
  display: inline-block;
  min-width: 16px;
  min-height: 16px;
  background: url("@/assets/images/bubble-icon.svg") no-repeat;
  background-size: 100% 100%;
}
</style>

<template>
  
  <span class="bubble-main">
    <el-tooltip
      placement="top-start"
      effect="customized"
      :show-arrow="false"
      style="border: none"
      :offset="bubbleType == '1' ? (bubbleTitle.length > 10 ? -15 : 10) : 15"
    >
      <span
      
      >
     <!-- // :style="`border-bottom:${bubbleType == '2' ? 'none' : '1px solid #333;'};`" -->
        <span>{{ bubbleType == "2" ? null : bubbleTitle }}</span>

        <Twitch
          v-if="bubbleType == '2' && bubbleIconSelect == 'twitch'"
          :color="bubbleBgColor"
          :size="20"
          class="bubble-icon"
        />
        <CommentAltDots
          v-if="bubbleType == '2' && bubbleIconSelect == 'commentAltDots'"
          :color="bubbleBgColor"
          :size="20"
          class="bubble-icon"
        />

        <SearchAlt
          v-if="bubbleType === '2' && bubbleIconSelect === 'search-alt'"
          :color="bubbleBgColor"
          class="bubble-icon"
          :size="20"
        />

        <Interactive
          v-if="bubbleType === '2' && bubbleIconSelect === 'interactive'"
          :color="bubbleBgColor"
          class="bubble-icon"
          :size="20"
        />

        <Editbubble
          v-if="bubbleType === '2' && bubbleIconSelect === 'editbubble'"
          :color="bubbleBgColor"
          class="bubble-icon"
          :size="20"
        />

        <CommentAltDots
          v-if="bubbleIconSelect === 'comment-alt-dots'"
          :color="bubbleBgColor"
          class="bubble-icon"
          :size="20"
        />
      </span>

      <template #content>
        <div class="slotCss">
          <div
            :style="`background:${bubbleBackgroundColor}; white-space: pre-line;  color:${bubbleFontColor};padding: 6px 12px; position: relative; border-radius: 4px; top:-20px;user-select: none;${bubbleContent.length > 500 ? 'width:800px' : ''};  `"
          
            >
            <div
              class="arrow"
              :style="{ borderTop: '5px solid ' + bubbleBackgroundColor }"
            ></div>
            
            <div v-if="bubbleContentSelect == '1'">
              {{ bubbleContent }}
            </div>
            <div v-else>
              <img :src="bubbleContentImage" />
            </div>
           
          
          <div class="bubble-url" v-show="bubbleUrl">
            <a :href="bubbleUrl" target="_blank">
              <i class="urlIcon"></i>
            </a>
          </div>
        </div>
        </div>
      </template>
    </el-tooltip>
  </span>
</template>

<script setup>
import { ref, defineProps } from "vue";
import Twitch from "./bubbleIconTemplate/twitch.vue";
import CommentAltDots from "./bubbleIconTemplate/commentAltDots.vue";
import SearchAlt from "./bubbleIconTemplate/searchAlt.vue";
import Interactive from "./bubbleIconTemplate/interactive.vue";
import Editbubble from "./bubbleIconTemplate/editbubble.vue";
const {
  id,
  bubbleTitle,
  bubbleContent,
  bubbleUrl,
  bubbleBackgroundColor,
  bubbleFontColor,
  bubbleIconSelect,
  bubbleBgColor,
  bubbleContentImage,
  bubbleContentSelect
} = defineProps({
  id: {
    type: String,
    default: "",
  },
  bubbleTitle: {
    type: String,
    default: "",
  },
  bubbleType: {
    type: String,
    default: "1",
  },
  bubbleContent: {
    type: String,
    default: "",
  },
  bubbleUrl: {
    type: String,
    default: "",
  },
  bubbleBackgroundColor: {
    type: String,
    default: "#333",
  },
  bubbleFontColor: {
    type: String,
    default: "#fff",
  },
  bubbleIconSelect: {
    type: String,
    default: "",
  },
  bubbleBgColor: {
    type: String,
    default: "#333",
  },
  color:{
    type: String,
    default: "#333",
  },
  bubbleContentSelect: {
    type: String,
    default: "1",
  },
  bubbleContentImage:{
    type: String,
    default: "",
  }

});

// const backgroundColor = ref("rgb(0, 0, 0)");
// const fontColor = ref("rgb(255, 255, 255)");
</script>

<style scoped>
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  border: none;
  background: none;
  color: #333;
}

.el-popper.is-customized .el-popper__arrow::before {
  background: none;
  right: 0;
}

.urlIcon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("@/assets/images/bubbleLinkIcon.svg") no-repeat;
  background-size: 100% 100%;
}

.bubble-icon {
  display: inline-block;
  width: 18px;
  aspect-ratio: 1 / 1; /* 保持宽高比 */
}
</style>
