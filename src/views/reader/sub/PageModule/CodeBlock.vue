<template>
  <div
    class="code-bg"
    :style="{
      background: theme == 'dark' ? '#2d2d2d !important' : '#f5f2f0 !important',
    }"
  >
    <div class="language-top">
      <div
        class="title"
        :style="{
          color: theme == 'dark' ? '#fff' : '#666',
        }"
      >
        代码块语言：{{ language }}
      </div>
      <div class="btn">
        <el-button v-if="runState" class="code-btn" @click="handleClick">
          运行</el-button
        >
      </div>
    </div>
    <div ref="monacoContainer" class="monaco-editor-container"></div>
  </div>
  <el-dialog v-model="dialogVisible" title="代码运行结果" width="50%">
    <div v-if="prismLanguage == 'html'">
      <iframe
        ref="myIframe"
        :srcdoc="resultContent"
        style="width: 100%; height: 700px; border: none"
      ></iframe>
    </div>
    <div v-else v-loading="loading">
      {{ resultContent }}
    </div>
  </el-dialog>
</template>

<script setup>
import QuestionHint from "@/views/reader/sub/PageModule/question-hint/questionHint";
import { defineProps, ref, onMounted, watch } from "vue";
import * as monaco from "monaco-editor";
import { chartAi } from "@/api/openApi/openApi";
const props = defineProps({
  margin: Object,
  theme: String,
  code: String,
  language: String,
  runState: {
    type: Boolean,
    default: true,
  },
  wordWrap: {
    type: Boolean,
    default: false,
  },
  lineNumbers: {
    type: Boolean,
    default: true,
  },
  prismLanguage: String,
});
const dialogVisible = ref(false);
const resultContent = ref("");
const loading = ref(false);
const myIframe = ref(false);
const monacoContainer = ref(null);
let editorInstance = null;
const handleClick = () => {
  if (props.code) {
    if (props.language != "html") {
      loading.value = true;
      chartAi({
        ability: 25,
        question: props.code,
        developmentLanguage: props.language,
      }).then((res) => {
        loading.value = false;
        resultContent.value = res.body.content;
      });
    } else {
      resultContent.value = props.code;
    }
    dialogVisible.value = true;
  }
};
onMounted(() => {
  const lineNumbers = props.lineNumbers ? "on" : "off";
  const wordWrap = props.wordWrap ? "on" : "off";
  editorInstance = monaco.editor.create(monacoContainer.value, {
    value: props.code,
    language: props.language,
    theme: props.theme === "dark" ? "vs-dark" : "vs",
    readOnly: true,
    automaticLayout: true,
    fontSize: 14,
    minimap: { enabled: false },
    lineNumbers: lineNumbers,
    wordWrap,
    scrollBeyondLastLine: false,
    overviewRulerBorder: false, // 禁用概述栏边框
    overviewRulerLanes: 0, // 禁用概述栏
    contextmenu: false, // 右键菜单
  });
  // 高度自适应
  const updateHeight = () => {
    const lineCount = editorInstance.getModel().getLineCount();
    const lineHeight = editorInstance.getOption(
      monaco.editor.EditorOption.lineHeight
    );
    monacoContainer.value.style.height = lineCount * lineHeight + 20 + "px";
    editorInstance.layout();
  };
  updateHeight();
  editorInstance.onDidChangeModelContent(updateHeight);
});
watch(
  () => [props.code, props.language, props.theme],
  ([newCode, newLang, newTheme]) => {
    if (editorInstance) {
      editorInstance.setValue(newCode);
      monaco.editor.setTheme(newTheme === "dark" ? "vs-dark" : "vs");
      monaco.editor.setModelLanguage(editorInstance.getModel(), newLang);
    }
  }
);
</script>

<style lang="less" scoped>
.monaco-editor {
  width: 100% !important;
  height: 100% !important;
  ::v-deep(.margin) {
    background-color: #fff !important;
  }
}
/deep/ .monaco-editor .cursor {
  display: none !important;
}
.line-numbers {
  overflow-x: auto;
  padding: 0 1em 1em 1em !important;
  margin-top: -2px;
  pre {
    background-color: transparent !important;
  }
}
.code-bg {
  position: relative;
  padding-bottom: 1px;
  border-radius: 4px;
  padding: 20px 0;
  margin: 20px 0;
  .language-top {
    z-index: 999;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 99%;
    .title {
      color: #fff;
      margin-right: 20px;
    }
  }
  .monaco-editor-container {
    margin: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: calc(100% - 20px); /* 考虑左右 margin */
    max-height: 400px;
  }
}
</style>
