<style lang="less" scoped>
.File {
  width: 100%;
  height: 55px;
  background-image: url(@/assets/images/readerResourcesIcon/modelBackground.png);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-sizing: border-box;

  margin-top: 16px;
}
.collapse-main {
  display: flex;
  align-items: center;

  .icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    background: url("@/assets/icons/svg/fileNew.svg") no-repeat;
    background-size: 100% 100%;
  }

  .iconDark {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    background: url("@/assets/icons/svg/fileDark.svg") no-repeat;
    background-size: 100% 100%;
  }
  .title {
    font-size: 16px;
    text-align: left;
    padding:0 10px;
  }
  .name {
    flex: 1;
    width: 500px;
    white-space: nowrap; /* 禁止文本换行 */
    overflow: hidden; /* 隐藏超出容器的内容 */
    text-overflow: ellipsis;
    font-size: 16px;
  }
}

.__name__ {
  cursor: pointer;
  font-size: 16px;
  min-width: 36px;
}
</style>

<template>
  <div
    class="File __orderTemplateBgUrl__"
    v-if="getFileIcon(url, true) !== 'ppt'"
  >
    <div class="collapse-main">
     
      <div :class="templateStyle.theme=='dark'?'icon':'iconDark'"></div>
      <div
        class="title"
        :style="{ color: templateStyle.theme == 'dark' ? '#333' : '#fff' }"
      >
        文件
      </div>
      <div class="name"   :style="{ color: templateStyle.theme == 'dark' ? '#333' : '#fff' }">
        <el-tooltip :content="name">{{ name }}</el-tooltip>
      </div>
    </div>
    <div class="__name__"  @click="preview"    :style="{ color: templateStyle.theme == 'dark' ? '#333' : '#fff' }">打开</div>
  </div>
  <el-collapse v-model="activeNames" v-else>
    <el-collapse-item class="video-parent" name="1">
      <template #title>
        <div class="collapse-title">
          <img
            :src="getFileIcon(url)"
            alt=""
            style="width: 17px; height: 17px"
          />
          <span class="name">{{ name }}</span>
        </div>
      </template>
      <div>
        <iframe
          :src="getPreviewFileUrl(url)"
          frameborder="0"
          width="770px"
          height="500px"
          style="transform: translateY(-47px)"
        ></iframe></div
    ></el-collapse-item>
  </el-collapse>
</template>

<script setup>
import { DocumentChecked } from "@element-plus/icons-vue";
import { defineProps } from "vue";
import { getFileIcon, getPreviewFileUrl } from "@/utils/index.js";
import usePreview from "@/store/modules/preview.js";
import useReader from "@/store/modules/reader";
const previewStore = usePreview();
const props = defineProps({
  type: String,
  url: String,
  name: String,
  nodeAlign: String,
  size: Number,
});
const activeNames = ref(["1"]);
const store = useReader();
const templateStyle = computed(() => {
  const obj = {};
  let tmpStyle = store.templateStyle;
  obj.theme = tmpStyle?.theme;
  return obj;
});

const preview = () => {
  previewStore.setData({ url: props.url, title: props.name });
};
</script>
