<!-- 模块说明 -->
<style lang="scss" scoped>
.image-title {
  text-align: center;
  font-size: 14px;
  color: #666666;
  
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

<template>
  <div
    :id="id"
    :style="{
      display: 'flex',
      justifyContent: nodeAlign,
      flexDirection: 'column',
      alignItems: nodeAlign,
      marginTop:'16px'
    }"
  >
    <div>
      <el-image
        :src="src"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        :preview-src-list="[src]"
        :initial-index="4"
        :style="{
          transform:
            flipX || flipY
              ? `rotateX(${flipX ? '180' : '0'}deg) rotateY(${flipY ? '180' : '0'}deg)`
              : 'none',
          width: width + 'px',
          height: height + 'px',
        }"
      />
      <div style="text-align: center;margin:5px 0;">
        <text v-if="isShowNo == 1" style="margin-right: 20px">{{
          number
        }}</text>
        <text v-if="isShowImageTitle == '1'">{{
          imageTitle || "标题图片"
        }}</text>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";
import usePreview from "@/store/modules/preview.js";
import useReader from "@/store/modules/reader.js";
const previewStore = usePreview();
const store = useReader();

const props = defineProps({
  src: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
  height: {
    type: String,
    default: "",
  },
  width: {
    type: String,
    default: "",
  },
  name: {
    type: String,
    default: "",
  },
  nodeAlign: {
    type: String,
    default: "center",
  },
  isShowImageTitle: {
    type: String,
    default: "0",
  },
  isShowNo: {
    type: String,
    default: "0",
  },
  linkAddress: {
    type: String,
    default: "",
  },
  imageTitle: {
    type: String,
    default: "",
  },
  flipX: {
    type: Boolean,
    default: false,
  },
  flipY: {
    type: Boolean,
    default: false,
  },
  number: {
    type: String,
    default: "",
  },
});

const toLine = () => {
  if (props.linkAddress) {
    window.open(props.linkAddress);
  }
};

async function handleClick() {
  const params = {
    bookId: store.comprehensiveBookData.bookId,
    resourceType: 0,
    chapterId: undefined,
    fileType: "1",
  };
  const res = await getBookResource(params);
  if (res.code === 200) {
    const result = res.data;
    let imageResources = [];
    result.forEach((item) => {
      imageResources = imageResources.concat(item.bookResourcesList);
    });
    previewStore.setPreviewImages(imageResources);
    nextTick(() => {
      previewStore.setCurrentImage(props.src);
    });
  }
}
</script>
