<!-- 画廊 -->
<style lang="scss" scoped>
.ImageGallery {
  width: 98%;
  column-gap: 5px; /* 列间距 */
  margin: 0 auto;
  padding: 5px;
  display: flex;
  .item {
    flex: 1;
    margin: 5px auto;
    text-align: center;
    break-inside: avoid;
    overflow: hidden;
  }
  .imgName {
    text-align: center;
    font-size: 14px;
    color: #555;
    margin-bottom: 10px;
    line-height: 20px;
  }
}
.title {
  text-align: center;

  margin-top: 20px;
}

.bgTitle {
  background-color: rgba(0, 0, 0, 0.3);
  width: 100%;
  min-height: 20px;
  padding: 10px 0;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 999;
  line-height: 24px;
  
  color: #fff;
  .txt {
    text-align: center;
    width: 100%;
  }
}
</style>

<template>
  <el-card style="margin: 20px 0">
    <div v-if="isSwiperTitle === '1'">
      <el-carousel
        autoplay
        indicator-position="outside"
        style="height: 300px"
        :interval="intervalNumber"
      >
        <el-carousel-item
          v-for="(item, index) in imgList"
          :key="item"
          style="height: 300px"
        >
          <div>
            <el-image
              :src="item.src"
              @click="clickImgPreview(item.src)"
              fit="contain"
              style="width: 100%; height: 300px"
            />
          </div>
          <div class="bgTitle" v-if="isShowImgTitle === '1'">
            <div class="txt">
              {{ /\.[^.]+$/.test(item.imageTitle) ? item.imageTitle.replace(/\.[^/.]+$/, '') : item.imageTitle }}
          </div>
          </div>
        </el-carousel-item>
      </el-carousel>

      <!-- 轮播预览 el-image组件本身的预览会因为放在轮播中定位失效，所以采用预览组件外置的方法 -->
      <el-image-viewer
        v-if="showPreview"
        :url-list="srcList"
        show-progress
        @close="showPreview = false"
      />
    </div>
    <div
      v-else
      class="ImageGallery"
      :style="`grid-template-columns: repeat(${columns}, 1fr);`"
    >
      <div class="item" v-for="(item, index) in columnContents" :key="index">
        <div v-for="ele in item" :key="ele.src">
          <el-image :src="ele.src" :preview-src-list="[ele.src]"  :hide-on-click-modal="true"/>
          <div
            class="imgName"
            v-if="isShowImgTitle === '1' || isShowNo === '1'"
          >
          {{ /\.[^.]+$/.test(ele.imageTitle) ? ele.imageTitle.replace(/\.[^/.]+$/, '') : ele.imageTitle }}
          
        
          </div>
        </div>
      </div>
    </div>
    <!--    <div class="title">-->
    <!--      -->
    <!--    </div>-->
    <div class="title"  v-if="isShowNo == '1' || isShowGalleryTitle == '1'">
      <span v-if="isShowNo == '1'" style="margin-right: 10px">
        {{ number }}
      </span>
      <span v-if="isShowGalleryTitle == '1'"> {{ galleryTitle }} </span>
    </div>
  </el-card>
</template>

<script setup>
const props = defineProps({
  galleryTitle: String, //画廊标题
  isShowNo: {
    type: String,
    default: "1",
  }, // 是否显示图号 0否1是  默认显示
  isShowGalleryTitle: {
    type: String,
    default: "1",
  }, // 是否显示画廊标题 0否1是 默认显示
  isShowImgTitle: {
    type: String,
    default: "1",
  }, // 是否显示图片标题 0否1是 默认显示
  isSwiperTitle: {
    typeof: String,
    default: "0",
  }, // 是否显示轮播标题 0否1是 默认显示
  imgList: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Number,
    default: 1,
  }, //画廊几列
  number: {
    type: String,
    default: 1,
  },
  interval: {
    type: Number,
    default: 5,
  },
});

const showPreview = ref(false);
const srcList = ref([]);

// 轮播预览
const clickImgPreview = (src) => {
  showPreview.value = true;
  srcList.value = [src];
};

const intervalNumber = computed(() => {
  return props.interval * 1000;
});

const columnContents = computed(() => {
  let arr = Array.from({ length: props.columns }, () => []);
  if (props.isSwiperTitle === "1") {
  } else {
    props.imgList.forEach((image, index) => {
      const columnIndex = index % props.columns; // 按列计算分配
      arr[columnIndex].push(image);
    });
  }
  return arr;
});
</script>
