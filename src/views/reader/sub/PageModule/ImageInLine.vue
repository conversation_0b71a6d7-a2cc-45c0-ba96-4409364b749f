<!-- 模块说明 -->
<style lang="scss" scoped>
.ImageInLine {
  display: inline-flex;
  width: 24px;
  height: 24px;
  position: relative;
  top: 5px;
  margin-right: 5px;
  .el-image {
    display: inline-flex;
    /* overflow: hidden; */
    position: relative;
  }
}

.imageContent {
  text-align: left;
  padding: 20px 0;
  line-height: 24px;
}

.imageSrc {
  text-align: center;
  padding: 10px 0;
}

.imageTitle {
  text-align: center;
}
</style>

<template>
  <div :id="id" class="ImageInLine" @click="openDialog">
    <el-image :src="defaultSrc" style="width: 24px; height: 24px" />
  </div>

  <el-dialog v-model="openVisible" title="行内图片预览" width="50%" center>
    <div class="imageContent">
      {{ imgDescribe }}
    </div>

    <div class="imageSrc">
      <el-image :src="src" />
    </div>
    <div class="imageTitle" :style="{ color: linkAddress ? '#409EFF' : '' }">
      <a :href="linkAddress" target="_blank"> {{ imageTitle }}</a>
    </div>
  </el-dialog>
</template>

<script setup>
import { defineProps } from "vue";
import defaultSrc from "@/assets/images/imgInline.svg";
const props = defineProps({
  src: String,
  id: String,
  previewtype: String,
  name: String,
  imgDescribe: String,
  linkAddress: String,
  imageTitle: String,
});
const openVisible = ref(false);

const openDialog = () => {
  openVisible.value = true;
};
</script>
