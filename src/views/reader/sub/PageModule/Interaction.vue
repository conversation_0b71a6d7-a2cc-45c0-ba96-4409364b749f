<!-- 投票 -->
<style lang="scss" scoped>
.Interaction {
  .content {
    text-align: center;
  }
}
</style>

<template>
  <el-card header="投票">
    <div class="Interaction">
      <div class="content">{{ themeContent }}</div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, defineProps } from 'vue'
const props = defineProps({
  voteList: Array,
  interactionType: Number,
  themeContent: String,
})

onMounted(() => {})
</script>
