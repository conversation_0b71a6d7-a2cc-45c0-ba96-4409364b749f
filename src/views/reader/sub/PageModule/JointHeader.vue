<template>
  <div
    class="headerbg"
    :style="style"
  >
    <div class="header-title">
      <slot></slot>
    </div>
  </div>
</template>
<script setup>
import { defineProps } from "vue";
import useReader from "@/store/modules/reader";

const store = useReader();
const props = defineProps({
  jointHeaderUrl: {
    type: String,
    default: null,
  },
  jointHeight: {
    type: Number,
    default: null,
  },
});

const style = computed(() => {
  let template = store.templateStyle;
  const style = {
    background: `url(${template?.jointHeaderUrl}) no-repeat`,
    backgroundSize: 'cover',
    height: `${template?.jointHeight / 2.5}px`,
    color: template?.jointFontColor,
  }
  if (props.jointHeaderUrl != null) {
    style.background = `url(${props.jointHeaderUrl}) no-repeat`
  }
  if (props.jointHeight != null) {
    style.height = `${props.jointHeight / 2.5}px`
  }
  return style
})

</script>
<style lang="less" scoped>
.headerbg {
  width: 100%;
  display: flex;
  align-items: center;
  margin: 10px 0;
  .header-title {
    width: 100%;
    outline: none;
    ::v-deep(p) {
      margin: 0 !important;
    }
  }
}
</style>
