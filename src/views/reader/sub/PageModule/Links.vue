<!-- 模块说明 -->
<style lang="less" scoped>
.links {
  display: inline;
  align-items: center;
  text-indent: 0;
  .title {
    cursor: pointer;
    color: #0966b4;
    word-break: break-all;
    white-space: normal;
    //.linkIcon-bg {
    //  display: inline-flex;
    //  align-items: center;
      //.linkIcon {
      //  width: 20px;
      //  height: 20px;
      //  background: url("@/assets/images/lintIcon.svg") no-repeat;
      //  display: inline-flex;
      //  align-items: center;
      //  background-size: cover;
      //}
    //}
    .linkIcon-bg::after {
      content: '';
      display: inline-flex;
      width: 20px;
      height: 20px;
      margin-top: -5px;
      vertical-align: middle;
      background-image: url("@/assets/images/lintIcon.svg");
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      pointer-events: none;
    }
  }
}
</style>

<template>
  <div class="links">
    <u class="title" @click="openLink">
<!--      <div v-if="plateType === 'titleIcon' && type !== 'websiteLink'">-->
<!--        {{ title }}-->
<!--      </div>-->
      <span :href="href" class="linkIcon-bg">
        {{ title }}
      </span>
    </u>
  </div>
</template>

<script setup>
import { nextTick } from "vue";
import { Link } from "@element-plus/icons-vue";
import useReader from "@/store/modules/reader";
import usePreview from "@/store/modules/preview.js";
const previewStore = usePreview();
const props = defineProps({
  plateType: String,
  type: String,
  title: String,
  href: String,
  attrs: Object,
});
const bookReadingStore = useReader();
const openLink = () => {
  switch (props.type) {
    case "chaptersInThisBook":
      bookReadingStore.jumpToChapter(props.href);
      break;

    case "websiteLink":
      window.open(props.href, "_blank");
      break;

    case "crossReferencing":
      // 需要验证
      bookReadingStore.jumpToChapter(props.href, props.attrs.pageNumber);
      const dom = document.getElementById(props.attrs.id);
      if (dom) {
        nextTick(() => {
          dom.scrollIntoView({ behavior: "smooth" });
        });
      }
      break;

    case "resourceLibrary":
      previewStore.setData({ url: props.href, title: props.attrs.name });
      break;

    default:
      break;
  }
};
</script>
