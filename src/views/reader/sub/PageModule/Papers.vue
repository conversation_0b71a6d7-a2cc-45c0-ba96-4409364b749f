<!-- 模块说明 -->
<style lang="scss" scoped></style>

<template>
  <Papers_1 :paperInfo="paperInfo" :chapterId="store.chapterId" v-if="paperInfo.paperType === 1" />
  <Papers_2 :paperInfo="paperInfo" v-else />
</template>

<script setup>
import { defineProps } from 'vue'
import Papers_1 from './QuestionsItem/Papers_1.vue'
import Papers_2 from './QuestionsItem/Papers_2.vue'
import useReader from '@/store/modules/reader'
const store = useReader()
const props = defineProps({
  paperInfo: Object,
})
</script>
