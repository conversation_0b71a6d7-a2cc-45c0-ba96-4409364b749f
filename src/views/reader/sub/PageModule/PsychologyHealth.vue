
<template>
  <psychology_1 :psychologyHealth="psychologyHealth" :chapterId="store.chapterId"></psychology_1>
</template>
<script setup>
import {defineProps} from 'vue'
import useReader from '@/store/modules/reader.js'
import Psychology_1 from "@/views/reader/sub/PageModule/PsychologyHealth/Psychology_1.vue";

const store = useReader()
const props = defineProps({
  psychologyHealth: Object,
})



</script>

<style scoped lang="scss">

</style>
