<style lang="scss" scoped>
.questionType {
  ::v-deep(img){
      max-width: 100%;
    }
  .stem {
    color: #333;
    margin-bottom: 20px;

    ::v-deep(img) {
      max-width: 100% !important;
    }
  }

  .questionContent {
    color: #333;

    ::v-deep(img) {
      max-width: 100%;
    }
  }

  .answerRadio {
    display: inline-block;
    width: 100%;
    margin-bottom: 14px;



    /* 调整 el-radio-group 的宽度以适应长标签 */
    .el-radio {
      white-space: normal;
      /* 允许标签内容换行 */
      height: auto;
      // max-width: 620px; /* 设置最大宽度 */
      // word-break: break-word; /* 允许在单词内换行 */
      // display: flex;
    }

    /* 调整标签的样式 */
    .el-radio__label {
      font-size: 18px;
      /* 设置字体大小 */
    }
  }

  .answer {
    display: flex;
    align-items: flex-start;

    .answerKey {
      width: 30px;
      display: flex;
      align-items: flex-start;
    }

    .answerContent {
      width: 100%;
      overflow: hidden;
      font-size: 1.125rem;
      color: #333;

      * {
        margin: 0;
      }

      ::v-deep(p) {
        display: block;
        line-height: 28px;

        img {
          max-width: 30% !important;
        }
      }
    }
  }

  .analysis {
  

    .analysisBg {
      padding: 10px 23px;
      background: linear-gradient(180deg, #f7f7f7 0%, #f7f7f7 100%);
    
      
      .analysisItem {
        display: flex;
        align-items: center;
        font-size: 14px;

        ::v-deep(img) {
            max-width: 100% !important;
          }

        .label {
          margin: 0 16px;
        }
      }
    }
  }

  .line {
    margin: 10px 0;
    border: 1px solid #e5e6e7;
  }

  .footer {
    text-align: center;
  }
}
</style>

<template>
  <section class="questionType">
    <div class="questionContent" v-if="data.questionRemark != '<p><br></p>'" v-html="data.questionRemark"></div>
    <div class="stem" v-html="data.questionContent"></div>

    <el-radio-group v-model="status.answer" :disabled="status.status">
      <div class="answerRadio" v-for="(ele, i) in data.options" :key="i">
        <el-radio :value="OPTION[i]">
          <div class="answer">
            <div class="answerContent" v-html="ele.optionContent" :id="ele.optionId"></div>
          </div>
        </el-radio>
      </div>
    </el-radio-group>

    <div class="analysis" v-show="status.status">
      <div class="analysisBg">
        <div class="analysisItem">正确答案：{{ status.rightKey }}</div>
        <div class="analysisItem">
          我的答案：{{ status.answer }}
          &nbsp;
          <el-icon color="#70cd79" v-if="status.rightKey === status.answer">
            <CircleCheckFilled />
          </el-icon>
          <el-icon color="#f57878" v-else>
            <CircleCloseFilled />
          </el-icon>
        </div>
      </div>

      <question-hint v-if="status.status" :analysis="data.analysis"
        :sectionReferToData="data.sectionReferTo"></question-hint>
    </div>
    <hr class="line" />
    <div class="footer" v-if="footer">
      <el-button @click="redo" size="large" style="width: 100px; height: 36px">重做</el-button>
      <el-button type="primary" @click="submit" :disabled="status.status" size="large" color="#0966b4"
        style="width: 100px; height: 36px">提交</el-button>
    </div>
  </section>
</template>

<script setup>
import QuestionHint from "@/views/reader/sub/PageModule/question-hint/questionHint";
import { reactive, defineProps, defineEmits, onMounted } from "vue";
import { CircleCheckFilled, CircleCloseFilled } from "@element-plus/icons-vue";
import { addQuestionAnswer } from "@/api/book/reader";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["submitTool"]);
const { OPTION, data, statusq } = defineProps({
  OPTION: Array,
  data: Object,
  statusq: {
    type: Boolean,
    default: true,
  },
  footer: {
    type: Boolean,
    default: true,
  },
  questionTypeShow: {
    type: String,
    default: "1",
  },
});
const status = reactive({
  answer: "",
  status: false,
  rightKey: "", //正确答案合集
});
const redo = () => {
  status.answer = "";
  status.status = false;
};
const judgeOptions = ["正确", "错误"];

const submit = async () => {
  if (!status.answer) return proxy.$message.error("请选择答案后提交");

  data.options.forEach((ele, i) => {
    //判断题  将A、B、C、D 转换为正确、错误
    if (ele.rightFlag) {
      if (data.questionType == 7) {
        status.rightKey = judgeOptions[i];
      } else {
        status.rightKey = OPTION[i];
      }
    }
  });
  //判断题  将A、B、C、D 转换为正确、错误
  if (data.questionType == 7) {
    status.answer = judgeOptions[0];
  }
  const params = {
    ...data.params,
    answerContent: status.answer,
    score: status.rightKey === status.answer ? 100 : 0,
  };
  const res = await addQuestionAnswer(params);
  if (res.code !== 200) return proxy.$message.error("网络错误情稍后重试");
  status.status = true;
};

const validation = () => !!status.answer;
const getData = () => {
  data.options.forEach((ele, i) => {
    if (ele.rightFlag) {
      status.rightKey = OPTION[i];
    }
  });
  return {
    dtbBookQuestionAnswer: {
      answerContent: status.answer,
      questionId: data.questionId,
      score: status.rightKey === status.answer ? 100 : 0,
    },
    score: status.rightKey === status.answer ? data.questionScore : 0, //分值
  };
};
const onAnalysis = () => {
  getData();
  status.status = true;
};

const getHeight = (id) => {
  const ele = document.getElementById(id);
  if (ele) {
    return ele.clientHeight;
  }
};

onMounted(() => {
  emit("submitTool", {
    validation,
    getData,
    onAnalysis,
  });
  if (data.defaultValue || !statusq) {
    status.answer = data.defaultValue;
    onAnalysis();
  }
});
</script>
