<!-- 多选 -->
<style lang="scss" scoped>
.questionType {
  color:#333;
  ::v-deep(img){
      max-width: 100%;
    }
  .questionContent{
   ::v-deep(img){
      max-width: 100%;
    }
  }
  .stem {
    color:#333;
    margin-bottom: 20px;
    ::v-deep(img){
      max-width: 100%;
    }
  }

  :deep(.el-checkbox:last-of-type) {
    height: auto;
     margin-bottom: 15px;
  }
  .answer {
    ::v-deep(.el-checkbox){
      height: auto;
    }
    .content {
      margin-left: 20px;
      width: 100%;
      font-size: 1.125rem;
      white-space: normal;
      word-break: break-word; /* 允许在单词内换行 */
     
      ::v-deep(p){
        padding:0;
        margin:0;
      }
      ::v-deep(img){
        max-width: 30%;
      }
    }
  }
  .analysis {
    .analysisBg {
      padding: 8px 23px;
      background: linear-gradient(180deg, #f7f7f7 0%, #f7f7f7 100%);
      margin: 20px 0;
      .analysisItem {
        display: flex;
        align-items: flex-start;
        margin: 10px 0;
     
        .label {
          margin-right: 25px;
          font-weight: bold;
        }
      }
    }
  }
  .line {
    margin: 10px 0;
    border: 1px solid #e5e6e7;
  }
  .footer {
    text-align: center;
  }
}
</style>

<template>
  <section class="questionType">
    <div class="questionContent" v-if="data.questionRemark !='<p><br></p>'" v-html="data.questionRemark"></div>
    <div class="stem" v-html="data.questionContent"></div>
    <el-checkbox-group v-model="status.answer" :disabled="status.status">
      <div>
        <div v-for="(ele, i) in data.options" :key="i">
          <el-checkbox :value="OPTION[i]">
            <section class="answer">
             
              <div v-html="ele.optionContent" class="content"></div>
            </section>
          </el-checkbox>
        </div>
      </div>
    </el-checkbox-group>
    <div class="analysis" v-show="status.status">
      <div class="analysisBg">
        <div class="analysisItem">
          <span class="label">正确答案：</span>{{ status.rightKey }}
        </div>
        <div class="analysisItem">
          <span class="label"> 我的答案：</span>{{ status.myAnswer }}
          &nbsp;
          <el-icon color="#70cd79" v-if="status.rightKey === status.myAnswer"
            ><CircleCheckFilled
          /></el-icon>
          <el-icon color="#f57878" v-else><CircleCloseFilled /></el-icon>
        </div>
      </div>

      <question-hint
        v-if="status.status"
        :analysis="data.analysis"
        :sectionReferToData="data.sectionReferTo"
      ></question-hint>
    </div>
    <hr class="line" />
    <div class="footer" v-if="footer">
      <el-button
        @click="redo"
        :disabled="!status.status"
        size="large"
        style="width: 100px; height: 36px"
        >重做</el-button
      >
      <el-button
        type="primary"
        @click="submit"
        :disabled="status.status"
        size="large"
        color="#0966b4"
        style="width: 100px; height: 36px"
        >提交</el-button
      >
    </div>
  </section>
</template>

<script setup>
import QuestionHint from "@/views/reader/sub/PageModule/question-hint/questionHint";
import { reactive, defineProps, defineEmits, onMounted } from "vue";
import { CircleCheckFilled, CircleCloseFilled } from "@element-plus/icons-vue";
import { addQuestionAnswer } from "@/api/book/reader";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["submitTool"]);
const { OPTION, data, statusq } = defineProps({
  OPTION: Array,
  data: Object,
  statusq: {
    type: Boolean,
    default: true,
  },
  footer: {
    type: Boolean,
    default: true,
  },
});
const status = reactive({
  answer: [],
  status: false,
  rightKey: "", //正确答案合集
  myAnswer: "", //我的答案
});
const redo = () => {
  status.answer = [];
  status.status = false;
  status.rightKey = "";
  status.myAnswer = "";
};
const submit = async () => {
  status.answer.sort();
  status.myAnswer = status.answer.toString();
  if (!status.answer?.length) return proxy.$message.error("请选择答案后提交");
  const arr = [];
  data.options.forEach((ele, i) => {
    if (ele.rightFlag) {
      arr.push(OPTION[i]);
    }
  });
  status.rightKey = arr.toString();
  const params = {
    ...data.params,
    answerContent: status.myAnswer,
    score: status.rightKey === status.myAnswer ? 100 : 0,
  };
  const res = await addQuestionAnswer(params);
  if (res.code !== 200) return proxy.$message.error("网络错误情稍后重试");
  status.status = true;
};

const validation = () => {
  status.answer.sort();
  status.myAnswer = status.answer.toString();
  return !!status.answer?.length;
};
const getData = () => {
  status.answer.sort();
  status.myAnswer = status.answer.toString();
  const arr = [];
  data.options.forEach((ele, i) => {
    if (ele.rightFlag) {
      arr.push(OPTION[i]);
    }
  });
  status.rightKey = arr.toString();
  return {
    dtbBookQuestionAnswer: {
      answerContent: status.myAnswer,
      questionId: data.questionId,
      score: status.rightKey === status.myAnswer ? 100 : 0,
    },
    score: status.rightKey === status.myAnswer ? data.questionScore : 0, //分值
  };
};
const onAnalysis = () => {
  getData();
  status.status = true;
};

onMounted(() => {
  emit("submitTool", {
    validation,
    getData,
    onAnalysis,
  });
  if (data.defaultValue || !statusq) {
    status.answer = data.defaultValue.split(",");
    onAnalysis();
  }
});
</script>
