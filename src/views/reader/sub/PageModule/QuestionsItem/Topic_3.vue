<!-- 填空 -->
<style lang="scss" scoped>
.questionType {
  color: #333;

  ::v-deep(img) {
    max-width: 100%;
  }

  .stem {
    margin-bottom: 20px;

    :v-deep(span) {
      background-color: transparent !important;
    }
  }

  ::v-deep(p) {
    padding: 0;
    margin: 0;
  }

  ::v-deep(img) {
    max-width: 100%;
    height: auto;
  }

  .answer {
    display: flex;
    align-items: center;
  }

  .analysis {
    line-height: 30px;
    .answerbg{
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 5px;
       
      }

    .analysisItem {
      display: flex;
      align-items: center;
    
     
      .label {
        font-size: 15px;
        color: #333;
        display: flex;
        width: 90px;
        font-weight: bold;
      }

      ::v-deep(img) {
        max-width: 100%;
      }

      .answerTxt {
        flex: 1;
        font-size: 18px;
        word-break: break-all;
        color:#666;
      }
    }
  }

  .line {
    margin: 10px 0;
    border: 1px solid #e5e6e7;
  }


  ::v-deep(.el-tabs__item.is-active){
    color:#0966B4;
  }
  ::v-deep(.el-tabs__active-bar){
    background: #0966B4;
  }

  .footer {
    text-align: center;
    button{
      margin-right:10px;
    }
  }
}
</style>
<style lang="scss">
.questionType {
  .question-input_wrapper {
    input {
      outline-style: none;
      border: none;
      border-bottom: 1px solid #000;
      max-width: 200px;
      padding: 0 10px;
      font-size: 18px;
      color: #333;
      background-color: transparent;
      text-align: center;
    }
  }
}
</style>
<template>
  <section class="questionType">
    <div class="questionContent" v-if="data.questionRemark != '<p><br></p>'" v-html="data.questionRemark"></div>
    <div class="stem" v-html="status.questionContent"></div>
    <div class="analysis" v-if="status.status">
      <div class="answerbg">
        <div class="analysisItem">
        <span class="label">正确答案：</span><span class="answerTxt" v-html="status.rightKey"></span>
      </div>
      <div class="analysisItem">
        <span class="label">我的答案：</span><span class="answerTxt">{{ status.myAnswer
        }}<el-icon color="#70cd79" v-if="isCorrectAnswer" style="margin-left:10px;">
            <CircleCheckFilled />
          </el-icon>
          <el-icon color="#f57878" v-else style="margin-left:10px;">
            <CircleCloseFilled />
          </el-icon></span>
      </div>
      </div>
     

      <question-hint v-if="status.status" :analysis="data.analysis"
        :sectionReferToData="data.sectionReferTo"></question-hint>
    </div>

    <hr class="line" />
    <div class="footer" v-if="footer">
      <el-button @click="redo" :disabled="!status.status" size="large" style="width: 100px; height: 36px">重做</el-button>
      <el-button type="primary" @click="submit" :disabled="status.status" size="large" color="#0966b4"
        style="width: 100px; height: 36px">提交</el-button>
    </div>
    <ImageView ref="imageViewRef" />
  </section>
</template>

<script setup>
import { reactive, defineProps, onMounted, defineEmits, nextTick } from "vue";
import { CircleCheckFilled, CircleCloseFilled } from "@element-plus/icons-vue";
import { uuid } from "@/utils/index";
import { addQuestionAnswer } from "@/api/book/reader";
import QuestionHint from "@/views/reader/sub/PageModule/question-hint/questionHint";
import ImageView from "@/components/imageView/index.vue"
const dividingCharSize = 10;
const imageViewRef = ref(null)
const { proxy } = getCurrentInstance();
const emit = defineEmits(["submitTool"]);
const { data, statusq, questionTypeShow } = defineProps({
  data: Object,
  statusq: {
    type: Boolean,
    default: true,
  },
  footer: {
    type: Boolean,
    default: true,
  },
  questionTypeShow: {
    type: String,
  },
});
const isCorrectAnswer = computed(() => {
  if (data.disorder != 2) {
    return status.rightKey === status.myAnswer;
  } else {
    let myAnswerTmp = status.myAnswer;
    status.rightKey.split(",").forEach((righKeyItem) => {
      const tmpIndex = myAnswerTmp.indexOf(righKeyItem);
      if (tmpIndex > -1) {
        myAnswerTmp =
          myAnswerTmp.substring(0, tmpIndex) +
          myAnswerTmp.substring(tmpIndex + righKeyItem.length);
        myAnswerTmp = myAnswerTmp.replace(/^,|,$/g, "");
      }
    });
    if (!myAnswerTmp) {
      return true;
    }
    return false;
  }
});

const status = reactive({
  answer: [],
  status: false,
  rightKey: "", //正确答案
  rightCollection: [], //正确答案合集
  myAnswer: "", //我的答案
  questionContent: "",
});
const redo = () => {
  status.answer = [];
  status.status = false;
  status.myAnswer = "";
  analysisStr();
};

/**
 * 解析匹配占位符
 */
const matchFillInTheBlank = (str) => {
  const obj = {
    count: 0,
    answer: [],
    status: false,
    newStr: "",
    countT: "",
  };
  for (const val of str) {
    if (obj.status) {
      if (val === "#") {
        obj.count++;
        if (obj.count === 6) {
          const inputId = `__input-${uuid()}__`;
          obj.status = false;
          obj.count = 0;
          obj.answer.push({ idName: inputId, value: obj.countT });
          obj.newStr += `<span class="question-input_wrapper"><input id='${inputId}' size="${dividingCharSize}"/></span>`;
          obj.countT = "";
        }
      } else {
        obj.countT += val;
      }
    } else {
      if (val === "#") {
        obj.count++;
        if (obj.count === 3) obj.status = true;
      } else {
        obj.count = 0;
        obj.newStr += val;
      }
    }
  }
  return { str: obj.newStr, answer: obj.answer };
};

/**
 * 根据input输入的字符数动态修改
 */
const editIptWidth = () => {
  status.rightCollection.forEach((ele) => {
    const element = document.getElementById(ele.idName);
    if (element) {
      element.oninput = function (e) {
        if (dividingCharSize > this.value.length) {
          this.setAttribute("size", dividingCharSize);
          return;
        }
        if (dividingCharSize * 3 > this.value.length) {
          this.setAttribute("size", this.value.length);
        }
        // if (e.target.value.length > ele.value.length) {
        //   element.style.width = e.target.value.length * 16 + 10 + "px";
        // } else {
        //   element.style.width = ele.value.length * 16 + "px";
        // }
      };
    }
  });
};

const submit = async () => {
  status.rightCollection.forEach((ele) => {
    const iptDom = document.getElementById(ele.idName);
    if (iptDom?.value) {
      status.answer.push(iptDom.value.trim());
      iptDom.disabled = true;
    }
  });

  const isHtmlTag = /<\/?[\w\s="/'':;.-]+>/gi;

  status.myAnswer = status.answer.toString();
  if (!status.answer?.length || isHtmlTag.test(status.myAnswer)) {
    proxy.$message.error("请填写答案格式不对");
    redo();
    return false;
  }

  const params = {
    ...data.params,
    answerContent: status.myAnswer,
    score: status.rightKey === status.myAnswer ? 100 : 0,
  };
  const res = await addQuestionAnswer(params);
  if (res.code !== 200) {
    status.rightCollection.forEach((ele) => {
      const iptDom = document.getElementById(ele.idName);
      if (iptDom) {
        iptDom.disabled = false;
      }
    });
    return proxy.$message.error("网络错误情稍后重试");
  }
  status.status = true;
};

const analysisStr = () => {
  const { str, answer } = matchFillInTheBlank(data.questionContent);
  status.questionContent = str;
  status.rightKey = answer.map((ele) => ele.value).toString();
  status.rightCollection = answer;
};

const validation = () => {
  status.answer = []
  status.rightCollection.forEach((ele) => {
    const iptDom = document.getElementById(ele.idName);
    if (iptDom.value) {
      status.answer.push(iptDom.value.trim());
      iptDom.disabled = true;
    }
  });
  return !!status.answer?.length;
};

const getData = () => {
  status.myAnswer = status.answer.toString();
  return {
    dtbBookQuestionAnswer: {
      answerContent: status.myAnswer,
      questionId: data.questionId,
      score: status.rightKey === status.myAnswer ? 100 : 0,
    },
    score: status.rightKey === status.myAnswer ? data.questionScore : 0, //分值
  };
};
const onAnalysis = () => {
  getData();
  status.status = true;
};
const openViewer = (value) => {
  const getValue = sessionStorage.getItem('v')
  if (getValue != '1') {
    sessionStorage.setItem('v', '1')
    imageViewRef.value.openView(value)
  }
}
// 页面初始化
onMounted(() => {
  analysisStr();
  emit("submitTool", {
    validation,
    getData,
    onAnalysis,
  });
  nextTick(() => {
    editIptWidth();
    if (data.defaultValue || !statusq) {
      // 什么场景运行?
      const _a = data.defaultValue.split(",");
      status.answer = _a;
      status.rightCollection.forEach((ele, i) => {
        const iptDom = document.getElementById(ele.idName);
        iptDom.value = _a[i];
        iptDom.disabled = true;
        iptDom.style.width = _a[i] * 16 + 10 + "px";
      });
      onAnalysis();
    }
    const imgElements = document.querySelectorAll('.questionType img')

    imgElements.forEach((img) => {
      img.addEventListener('click', () => openViewer(img.src))
    })

  });
});
</script>
