<!-- 排序 -->
<style lang="scss" scoped>
.questionType {
  color: #333;
  ::v-deep(img){
      max-width: 100%;
    }
  .questionContent {
    ::v-deep(img) {
      max-width: 100%;
    }

  }

  .stem {
    margin: 10px 0;

    ::v-deep(img) {
      max-width: 100%;
    }
  }

  .answer {
    position: relative;

    .maskLayer {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      cursor: not-allowed;
    }

    .answerItem {
      min-height: 32px;
      line-height: 32px;
      padding: 0 16px;
      box-sizing: border-box;
      border-radius: 4px;
      background: linear-gradient(0deg, #ffffff 0%, #f7f7f7 100%);
      border: 1px solid #e5e6e7;
      margin-bottom: 10px;
      overflow: hidden;
      display: flex;
      justify-items: flex-start;
      cursor: move;

      ::v-deep(img) {
        max-width: 100%;
      }

      li {
        list-style: none;
        width: 100%;
        overflow: hidden;

        ::v-deep(p) {
          margin: 16px 0;
        }
      }
    }
  }

  .analysis {
    .analysisItem {
      display: flex;
      // align-items: center;
      margin: 10px 0;
     

      .label {
        margin: 0 16px;
      }

      list-style: none;

      .analysisItem-label {
        width: 80px;
      }

      p {
        ::v-deep(img) {
          width: 100% !important;
        }
      }

      .analysisItem-content {
        flex: 1;
        width: 100%;
        padding: 0 20px;

        li {
          list-style: none;
          width: 100%;
          overflow: hidden;

         
        }
      }
    }
  }

  .footer {
    text-align: center;
  }
}
</style>

<template>
  <section class="questionType">
    <div class="questionContent" v-if="data.questionRemark != '<p><br></p>'" v-html="data.questionRemark"></div>
    <div class="stem" v-html="data.questionContent"></div>
    <div class="answer">
      <VueDraggable v-model="status.myAnswer" item-key="id" tag="transition-group" :animation="150">
        <div v-for="(ele, i) in status.myAnswer" :key="i" class="answerItem">
          <li v-html="ele.optionContent"></li>
        </div>
      </VueDraggable>
      <div class="maskLayer" v-show="status.status"></div>
    </div>
    <div class="analysis" v-show="status.status">
      <div class="analysisItem-label">我的答案：</div>
      <div class="analysisItem">
        <div class="analysisItem-content">
          <div v-for="ele in status.myAnswer">
            <li v-html="ele.optionContent"></li>
          </div>
        </div>
        &nbsp;
        <el-icon color="#70cd79" v-if="status.rightKey === status.answer">
          <CircleCheckFilled />
        </el-icon>
        <el-icon color="#f57878" v-else>
          <CircleCloseFilled />
        </el-icon>
      </div>
      <div class="analysisItem-label">正确答案：</div>
      <div class="analysisItem">
        <div class="analysisItem-content">
          <div v-for="ele in data.options">
            <li v-html="ele.optionContent"></li>
          </div>
        </div>
      </div>
      <question-hint v-if="status.status" :analysis="data.analysis"
        :sectionReferToData="data.sectionReferTo"></question-hint>
    </div>
    <hr class="line" />
    <div class="footer" v-if="footer">
      <el-button @click="redo" :disabled="!status.status" size="large" style="width: 100px; height: 36px">重做</el-button>
      <el-button type="primary" @click="submit" :disabled="status.status" size="large" color="#0966b4"
        style="width: 100px; height: 36px">提交</el-button>
    </div>
  </section>
</template>

<script setup>
import { reactive, defineProps, defineEmits, onMounted } from "vue";
import { CircleCheckFilled, CircleCloseFilled } from "@element-plus/icons-vue";
import { VueDraggable } from "vue-draggable-plus";
import { addQuestionAnswer } from "@/api/book/reader";
import QuestionHint from "@/views/reader/sub/PageModule/question-hint/questionHint";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["submitTool"]);
const { data } = defineProps({
  OPTION: Array,
  data: Object,
  footer: {
    type: Boolean,
    default: true,
  },
});
const status = reactive({
  answer: "",
  status: false,
  rightKey: "", //正确答案
  myAnswer: [], //我的答案合集
});

/**
 * 随机打乱数组
 */
function shuffleArray(array) {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1)); // 随机选择一个索引
    [array[i], array[j]] = [array[j], array[i]]; // 交换元素
  }
  return array;
}

function init() {
  let arr = data.options.map((ele, i) => ({ ...ele, id: i }));
  status.myAnswer = shuffleArray(arr);
}

const redo = () => {
  status.answer = "";
  status.rightKey = "";
  status.status = false;
  init();
};

const submit = async () => {
  status.rightKey = data.options.map((ele, i) => i).toString();
  status.answer = status.myAnswer.map((ele) => ele.id).toString();
  if (!status.answer) return proxy.$message.error("请正确排序后提交");
  const params = {
    ...data.params,
    answerContent: status.myAnswer.map((ele) => ele.optionContent).toString(),
    score: status.rightKey === status.answer ? 100 : 0,
  };
  const res = await addQuestionAnswer(params);
  if (res.code !== 200) return proxy.$message.error("网络错误情稍后重试");
  status.status = true;
};

const validation = () => {
  status.rightKey = data.options.map((ele, i) => i).toString();
  status.answer = status.myAnswer.map((ele) => ele.id).toString();
  return !!status.answer;
};
const getData = () => {
  return {
    dtbBookQuestionAnswer: {
      answerContent: status.myAnswer.map((ele) => ele.optionContent).toString(),
      questionId: data.questionId,
      score: status.rightKey === status.answer ? 100 : 0,
    },
    score: status.rightKey === status.answer ? data.questionScore : 0, //分值
  };
};
const onAnalysis = () => {
  getData();
  status.status = true;
};

onMounted(() => {
  init();
  emit("submitTool", {
    validation,
    getData,
    onAnalysis,
  });
  if (data.defaultValue) {
    status.myAnswer = data.defaultValue
      .split(",")
      .map((ele) => ({ optionContent: ele }));
    // 和简答同理，在不更改大结构且兼容历史数据的情况下，选择进行文本比对
    let arr = data.options.map((e) => e.optionContent).join(",");
    status.rightKey = extractText(arr);
    status.answer = data.defaultValue;
    onAnalysis();
  }
});
//解析正确答案富文本
const extractText = (htmlString) => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlString, "text/html");
  return doc.body.textContent.trim() || "";
};
</script>
