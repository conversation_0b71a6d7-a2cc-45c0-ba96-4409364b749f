<!-- 6简答 -->
<style lang="scss" scoped>
.questionType {
  color:#333;
  ::v-deep(img){
      max-width: 100%;
    }
  .questionContent{
    ::v-deep(img) {
          max-width: 100%;
        }
  }
  .stem {
    margin: 10px 0;
    ::v-deep(img) {
          max-width: 100%;
        }
  }
  .answer {
    margin-bottom: 15px;
    .judge_answer_forme {
      float: right;
      margin-top: 5px;
    }
    ::v-deep(.el-textarea__inner){
      font-size: 18px;
    }
  }
  .analysis {
    .analysisItem {
      display: flex;
      align-items: center;
      margin: 10px 0;
     ::v-deep(ol) li{
        list-style-type: decimal;
      }
      ::v-deep(ul) li{
        list-style-type: disc;
      }
      .label {
        margin: 0 16px;
      }
      ::v-deep(img) {
          max-width: 100%;
        }
    }

    .question-hint{
        ::v-deep(img) {
          max-width: 100%;
        }
      }
  }
  .footer {
    margin-top: 10px;
    text-align: right;
  }
}
</style>

<template>
  <section class="questionType">
    <div class="questionContent" v-if="data.questionRemark !='<p><br></p>'" v-html="data.questionRemark"></div>
    <div class="stem" v-html="data.questionContent"></div>
    <div class="answer">
      <el-input
        type="textarea"
        v-model="status.answer"
        :disabled="status.status"
        :autosize="{ minRows: 8 }"
      />
      <div v-if="status.status" class="judge_answer_forme">
        <el-icon color="#70cd79" v-if="status.rightKey === status.answer">
          <CircleCheckFilled />
        </el-icon>
        <el-icon color="#f57878" v-else>
          <CircleCloseFilled />
        </el-icon>
      </div>
    </div>
    <div class="analysis" v-show="status.status">
      <div>正确答案：</div>
      <div class="analysisItem">
        <div class="label"></div>
        <div v-html="data.rightAnswer"></div>
      </div>
      <question-hint
        v-if="status.status"
        :analysis="data.analysis"
        :sectionReferToData="data.sectionReferTo"
      ></question-hint>
    </div>
    <div class="footer" v-if="footer">
      <el-button
        @click="redo"
        :disabled="!status.status"
        style="width: 100px; height: 36px"
        >重做</el-button
      >
      <el-button
        type="primary"
        @click="submit"
        :disabled="status.status"
        color="#0966b4"
        style="width: 100px; height: 36px"
        >提交</el-button
      >
    </div>
  </section>
</template>

<script setup>
import { reactive, defineProps, defineEmits, onMounted } from "vue";
import { addQuestionAnswer } from "@/api/book/reader";
import QuestionHint from "@/views/reader/sub/PageModule/question-hint/questionHint";

const { proxy } = getCurrentInstance();
const emit = defineEmits(["submitTool"]);
const { data, statusq } = defineProps({
  data: Object,
  footer: {
    type: Boolean,
    default: true,
  },
  statusq: {
    type: Boolean,
    default: true,
  },
});
const status = reactive({
  answer: "",
  status: false,
  rightKey: "", //正确答案合集
});

const redo = () => {
  status.answer = "";
  status.status = false;
  status.rightKey = "";
};

const submit = async () => {
  if (!status.answer) return proxy.$message.error("请填写答案后提交");
  const params = {
    ...data.params,
    answerContent: status.answer.trim(),
    score: 0,
  };
  const res = await addQuestionAnswer(params);
  if (res.code !== 200) return proxy.$message.error("网络错误情稍后重试");
  status.status = true;
};

const validation = () => !!status.answer;
const getData = () => ({
  dtbBookQuestionAnswer: {
    answerContent: status.answer.trim(),
    questionId: data.questionId,
    score: status.rightKey === status.answer.trim() ? 100 : 0,
  },
  score: status.rightKey === status.answer.trim() ? data.questionScore : 0, //分值
});
const onAnalysis = () => {
  // 提交后操作
  if (data?.rightAnswer) {
    status.rightKey = extractText(data.rightAnswer);
  }
  getData();
  status.status = true;
};
onMounted(() => {
  //首次进入
  if (data?.rightAnswer) {
    status.rightKey = extractText(data.rightAnswer);
  }
  emit("submitTool", {
    validation,
    getData,
    onAnalysis,
  });
  if (data.defaultValue || !statusq) {
    status.answer = data.defaultValue;
    onAnalysis();
  }
});
//解析正确答案富文本
const extractText = (htmlString) => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlString, "text/html");
  return doc.body.textContent.trim() || "";
};
</script>
