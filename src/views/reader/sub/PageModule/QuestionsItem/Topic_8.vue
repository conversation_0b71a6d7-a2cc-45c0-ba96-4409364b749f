<!-- 8编程 -->
<style lang="scss" scoped>
.questionType {
  color:#333;
  ::v-deep(img){
      max-width: 100%;
    }
  .questionContent{
    ::v-deep(img) {
          max-width: 100%;
        }
  }
  .stem {
    margin: 10px 0;
    
        ::v-deep(img) {
          max-width: 100%;
        }
    
  }
  .answer {
    margin-bottom: 15px;
  }
  .analysis {
    .analysisItem {
      display: flex;
      align-items: center;
      margin: 10px 0;
      .label {
        margin: 0 16px;
      }
    }
    .question-hint{
        ::v-deep(img) {
          max-width: 100%;
        }
      }
  }
  .line {
    margin: 10px 0;
    border: 1px solid #e5e6e7;
  }
  .footer {
    text-align: center;
    button{
      margin-right:10px;
    }
  }
}
</style>

<template>
  <section class="questionType">
    <div class="questionContent" v-if="data.questionRemark !='<p><br></p>'" v-html="data.questionRemark"></div>
    <div class="stem" v-html="data.questionContent"></div>
    <div class="answer"></div>
    <hr />
    <div class="footer" v-if="footer">
      <el-button
        type="primary"
        @click="submit"
        :disabled="status.status"
        size="large"
        color="#0966b4"
        style="width: 100px; height: 36px"
        >运行</el-button
      >
    </div>
  </section>
</template>

<script setup>
import { reactive, defineProps, defineEmits, onMounted } from "vue";

const { proxy } = getCurrentInstance();

const emit = defineEmits(["submitTool"]);
const { data } = defineProps({
  data: Object,
  footer: {
    type: Boolean,
    default: true,
  },
});
const status = reactive({
  answer: "",
  status: false,
  rightKey: "", //正确答案合集
});

const redo = () => {
  status.answer = "";
  status.status = false;
  status.rightKey = "";
};
const questionsData = ref({});
const submit = async () => {
  questionsData.value = data;
  sessionStorage.setItem("questionsData", JSON.stringify(questionsData.value));
  window.open(
    "/runCode?bookId=" +
      questionsData?.value.params.bookId +
      "&chapterId=" +
      questionsData?.value.params.chapterId +
      "&bookQuestionId=" +
      questionsData?.value.params.bookQuestionId +
      "&userQuestionId=" +
      questionsData?.value.params.userQuestionId +
      "&language=" +
      questionsData?.value.language,
    "_blank"
  );
};

const validation = () => !!status.answer;
const getData = () => ({
  dtbBookQuestionAnswer: {
    answerContent: status.answer,
    questionId: data.questionId,
    score: 0,
  },
  score: 0, //分值
});
const onAnalysis = () => {
  getData();
  status.status = true;
};
onMounted(() => {
  emit("submitTool", {
    validation,
    getData,
    onAnalysis,
  });
  if (data.defaultValue) {
    status.answer = data.defaultValue;
    onAnalysis();
  }
});
</script>
