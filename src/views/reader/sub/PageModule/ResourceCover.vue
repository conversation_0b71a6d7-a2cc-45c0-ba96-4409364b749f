<style lang="less" scoped>
.rc-3d {
  position: relative;
margin-top: 16px;
  width: 100%;
  height: 392px;
  background-image: url("@/assets/resources/bg/3d-bg.png");
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  .text {
    font-size: 36px;
    line-height: 52px;
    color: #333;
    width: 70%;
    margin: -20px auto 0;
    text-align: center;
  }
}

.rc-av {
  position: relative;
  margin-top: 16px;
  width: 100%;
  height: 392px;
  background-image: url("@/assets/resources/bg/vr-bg.png");
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  .text {
    font-size: 36px;
    line-height: 52px;
    color: #333;
    width: 70%;
    margin: -20px auto 0;
    text-align: center;
  }
}

.rc-game {
  position: relative;
  margin-top:16px;
  width: 100%;
  height: 392px;
  background-image: url("@/assets/resources/bg/game-bg.png");
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  .text {
    font-size: 36px;
    line-height: 52px;
    color: #333;
    width: 70%;
    margin: -20px auto 0;
    text-align: center;
  }
}
.ResourceCover {
  width: 100%;
  height: 55px;
  background-image: url("@/assets/images/readerResourcesIcon/modelBackground.png");
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-sizing: border-box;
  font-size: 16px;
  margin-top:16px;
}
.collapse-main {
  display: flex;
  align-items: center;
  width: 100%;
  .icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
   
    & > img {
      width: 100%;
      height: 100%;
    }
  }
  .title {
    padding: 0 10px;
    font-size: 16px;
    text-align: left;
  }
  .name {
    flex: 1;
   
    text-align: left;
    white-space: nowrap; /* 禁止文本换行 */
    overflow: hidden; /* 隐藏超出容器的内容 */
    text-overflow: ellipsis;
    font-size: 16px;
    width: 500px;
  }
}
.__name__ {
  cursor: pointer;
}

.linkbtn {
  margin-right: 60px;
}

.extended-reading {
  border: none;
  margin: 20px 0;
  background-image: url("@/assets/images/readerResourcesIcon/modelBackground.png");

  .el-collapse-item__wrap {
    border-bottom: none;
  }
}

.extended-reading-content {
  margin: 10px 0 0;
  padding: 16px;
  word-wrap: break-word;

  p:first-child {
    margin-top: 0;
  }

  ::v-deep(a) {
    color: #409eff !important;
    text-decoration: underline;
    display: inline-flex;
    align-items: center;
  }
  ::v-deep(a::after) {
    content: "";
    display: inline-flex;
    width: 16px;
    height: 16px;
    background: url(@/assets/icons/svg/linkIcon.svg) no-repeat;
    background-size: 100% 100%;
    margin-left: 2px;
  }

  ::v-deep(pre) {
    margin: 20px 0;
    padding: 10px;
    background-color: #333;
    code {
      background: transparent;
    }
  }
  ::v-deep(code) {
    padding: 5px;
    margin: 10px 0;
    color: #fff;
    background-color: #f1f1f1;
  }
  ::v-deep(blockquote) {
    border-left: 3px solid #0966b4;
    padding: 10px;
    background-color: #f0f0f0;
    margin: 20px 0;
  }
  ::v-deep(table) {
    margin: 20px 0;
    padding: 5px;
    border-collapse: collapse;
    th {
      padding: 5px;
      background-color: #f1f1f1;
      border: 1px solid #f1f1f1;
    }
    td {
      padding: 5px;
      border: 1px solid #f1f1f1;
    }
  }
}

.video-parent {
  margin: 10px 0;
  .el-collapse-item__header {
    background-image: url("@/assets/images/readerResourcesIcon/modelBackground.png");
  }
  .el-collapse-item__content {
    padding: 0;
  }
  .collapse-main {
    display: flex;
    align-items: center;
    padding: 0 20px;
    width: 100%;
    .icon {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      margin-right: 10px;
      & > img {
        width: 100%;
        height: 100%;
      }
    }
    .title {
      width: 70px;
      font-size: 16px;
      text-align: left;
    }
    .name {
      flex: 1;
      font-size: 16px;
      padding-left: 10px;
    }
  }
}
</style>
<template>
  <section :id="id">
    <!-- 游戏 -->
    <div v-if="rcType == '0'" class="rc-3d" @click="previewResource">
      <div class="text">
        {{ title }}
      </div>
    </div>
    <div
      v-else-if="rcType == '1' || rcType == '2'"
      class="rc-av"
      @click="previewResource"
    >
      <div class="text">
        {{ title }}
      </div>
    </div>
    <div v-else-if="rcType == '4'" class="rc-game" @click="previewResource">
      <div class="text">
        {{ title }}
      </div>
    </div>
    <!-- <el-collapse v-if="rcType === 4" v-model="game" style="margin: 10px 0">
      <el-collapse-item class="video-parent __orderTemplateBgUrl__" name="1">
        <template #title>
          <div class="collapse-main" style="padding-left: 20px">
            <div class="icon">
              <img
                :src="
                  templateStyle.theme == 'light'
                    ? ResourceCoverName(rcType).lighticon
                    : ResourceCoverName(rcType).darkicon
                "
              />
            </div>
            <div
              class="title"
              :style="{
                color: templateStyle.theme == 'light' ? '#fff' : '#333',
              }"
            >
              {{ ResourceCoverName(rcType).name }}
            </div>
            <div class="name" v-if="showTitle">
              <el-tooltip :content="title">{{ title }}</el-tooltip>
            </div>
          </div>
        </template>
        <div>
          <iframe
            :src="url"
            frameborder="0"
            width="100%"
            height="400px"
          ></iframe></div
      ></el-collapse-item>
    </el-collapse> -->
    <!-- 扩展阅读 -->

    <div
      class="ResourceCover __orderTemplateBgUrl__"
      v-else-if="rcType === 3 || rcType === 5 || rcType === 7"
    >
      <div class="collapse-main">
        <div class="icon">
          <img
            :src="
              templateStyle.theme == 'light'
                ? ResourceCoverName(rcType).lighticon
                : ResourceCoverName(rcType).darkicon
            "
          />
        </div>
        <div
          class="title"
          :style="{
            color: templateStyle.theme == 'light' ? '#fff' : '#333',
          }"
        >
          {{ ResourceCoverName(rcType).name }}
        </div>

        <div class="name" v-if="showTitle" :style="{
            color: templateStyle.theme == 'light' ? '#fff' : '#333',
          }">
          <el-tooltip :content="title">{{ title }}</el-tooltip>
        </div>
      </div>
      <div
      
        @click="previewResource"
        :style="{
          color: templateStyle.theme == 'light' ? '#fff' : '#333',
          cursor: 'pointer',
          fontSize: '16px',
          width: '100px',
          textAlign: 'right',
          minWidth: '40px',
          marginRight:'3px'
        }"
      >
        打开
      </div>
    </div>
    <el-dialog
      v-model="resoureDetailDialogVisibility"
      :append-to-body="true"
      :title="title"
      width="1100px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div style="height: 700px; margin: 10px 0">
        <iframe :src="url" frameborder="0" width="100%" height="100%"></iframe>
      </div>
    </el-dialog>
  </section>
</template>

<script setup>
import threeD from "@/assets/images/readerResourcesIcon/3D.png";
import ARVR from "@/assets/images/readerResourcesIcon/ARVR.png";
import virtuakl from "@/assets/images/readerResourcesIcon/virtuakl.png";
import gameIcon from "@/assets/images/readerResourcesIcon/game.png";
import ResourceCover from "@/assets/images/readerResourcesIcon/ResourceCover.png";
import further from "@/assets/images/readerResourcesIcon/further.png";
import practicalTraining from "@/assets/images/readerResourcesIcon/practicalTraining.png";
import testPaper from "@/assets/images/readerResourcesIcon/testPaper.png";
import zuoye from "@/assets/images/readerResourcesIcon/zuoye.png";
// 游戏
import lightGame from "@/assets/resources/light/game.svg";
import drakGame from "@/assets/resources/dark/game.svg";
// 拓展阅读
import lightRead from "@/assets/resources/light/read.svg";
import drakRead from "@/assets/resources/dark/read.svg";
// 3d
import light3d from "@/assets/resources/light/3d.svg";
import drak3d from "@/assets/resources/dark/3d.svg";
// AR
import lightAR from "@/assets/resources/light/arvr.svg";
import drakAR from "@/assets/resources/dark/arvr.svg";

// 虚拟仿真
import lightVirtual from "@/assets/resources/light/virtual.svg";
import drakVirtual from "@/assets/resources/dark/virtual.svg";
// 教学资源
import lightTeach from "@/assets/resources/light/teach.svg";
import drakTeach from "@/assets/resources/dark/teach.svg";
// 实训
import lightPractical from "@/assets/resources/light/practical.svg";
import drakPractical from "@/assets/resources/dark/practical.svg";

import { ref, computed, onMounted } from "vue";
import usePreview from "@/store/modules/preview.js";
import useReader from "@/store/modules/reader";
const store = useReader();
const previewStore = usePreview();
const resoureDetailDialogVisibility = ref(false);
const readingContentRef = ref(null);
const props = defineProps({
  url: String,
  title: {
    type: String,
  },
  showTitle: {
    default: true,
    type: Boolean,
  },
  readingContent: {
    default: "",
    type: String,
  },
  rcType: Number,
  id: String,
  bgSrc: String,
});

const { rcType, showTitle, title, url, readingContent, bgSrc } = props;
const activeResourceName = ref("");
if (rcType === 6) {
  activeResourceName.value = "1";
}

onMounted(() => {
  if (readingContentRef.value) {
    const imgList = readingContentRef.value.querySelectorAll("img");
    imgList.forEach((item) => {
      item.style.maxWidth = "100%";
    });
  }
});
const ResourceCoverIcon = [
  threeD,
  ARVR,
  ARVR,
  virtuakl,
  gameIcon,
  ResourceCover,
  further,
  practicalTraining,
  testPaper,
  zuoye,
];
const getResourceCoverIcon = (i) => {
  return ResourceCoverIcon[i];
};

const game = ref(["1"]);
/**
 * rcType
 * 0 --->3d模型
 * 1 --->AR
 * 2 --->VR
 * 3 --->虚拟仿真
 * 4 --->游戏
 * 5 --->教学资源
 * 6 --->扩展阅读
 * 7 --->实训
 * 8 --->试卷
 * 9 --->作业
 */

const ResourceCoverName = computed(() => {
  return function (rcType) {
    switch (rcType) {
      case 0:
        return { name: "3D模型", lighticon: light3d, darkicon: drak3d };
      case 1:
        return { name: "AR", lighticon: lightAR, darkicon: drakAR };
      case 2:
        return { name: "AR", lighticon: lightAR, darkicon: drakAR };
      case 3:
        return {
          name: "虚拟仿真",
          lighticon: lightVirtual,
          darkicon: drakVirtual,
        };
      case 4:
        return { name: "游戏", lighticon: lightGame, darkicon: drakGame };
      case 5:
        return { name: "教学资源", lighticon: lightTeach, darkicon: drakTeach };
      case 6:
        return { name: "拓展阅读", lighticon: lightRead, darkicon: drakRead };
      case 7:
        return {
          name: "实训",
          lighticon: lightPractical,
          darkicon: drakPractical,
        };
      case 8:
        return { name: "试卷", lighticon: lightGame, darkicon: drakGame };
      case 9:
        return { name: "作业", lighticon: lightGame, darkicon: drakGame };
      default:
        break;
    }
  };
});

function previewResource() {
  switch (rcType) {
    case 0:
      // previewStore.originalPreview({ url, title });
      resoureDetailDialogVisibility.value = true;
      break;
    case 1:
      resoureDetailDialogVisibility.value = true;
      break;
    case 2:
      resoureDetailDialogVisibility.value = true;
      break;
    case 4:
      resoureDetailDialogVisibility.value = true;
      break;
    case 7:
      window.open(url, "_blank");
      break;

    case 8:
      previewStore.originalPreview({ url, title });
      break;

    default:
      window.open(url, "_blank");
  }
  // previewStore.setData({ url, title })
  // previewStore.originalPreview({ url, title })
}

const templateStyle = computed(() => {
  const obj = {};
  let tmpStyle = store.templateStyle;
  obj.theme = tmpStyle?.theme;
  obj.orderTemplateBgUrl = tmpStyle?.orderTemplateBgUrl;
  return obj;
});
</script>
