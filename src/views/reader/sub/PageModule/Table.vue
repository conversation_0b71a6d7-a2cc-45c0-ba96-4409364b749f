<!-- 表格 -->
<style lang="scss" scoped>
.Tablebase {
  margin: 15px 0;
}

::v-deep(.bellCss) {
  width: 100%;
  table {
    margin: 15px 0;
  }
}
</style>

<template>
  <table>
    <tr>
      <td></td>
    </tr>
  </table>
</template>

<script setup>
import { defineProps, computed } from "vue";

const props = defineProps({
  name: String,
  id: String,
  isShowTitle: Boolean,
  templateId: String,
  content: Array,
});
const tableClassName = computed(() => {
  if (!props.templateId) {
    return "__tables_default";
  } else {
    return `__tables_${props.templateId}`;
  }
});
</script>
