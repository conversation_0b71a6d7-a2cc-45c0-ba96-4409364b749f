<template>
    <div class="backgroundImg_bg" :id="id" :style="`height:${height}px;margin-top:0.75em`">
        <img :src="src" class="node-view-image" :style="`width:${orangeWidth}px;height:${height}px;`" />
        <div :style="styled">
            <slot  />
        </div>
       

    </div>
</template>
<script setup>
import { ref, computed } from 'vue'
const props = defineProps({
    id: String,
    src: String,
    width: [String, Number],
    height: [String, Number],
    paddingTop: [String, Number],
    paddingLeft: [String, Number],
    paddingRight: [String, Number],
    paddingBottom: [String, Number],
})

const styled = computed(() => {
    return `padding:${props.paddingTop}px ${props.paddingRight}px ${props.paddingBottom}px ${props.paddingLeft}px`
       
   
})


const orangeWidth = computed(() => {
    return Number(props.width)>697?697:Number(props.width)
})

</script>
<style scoped lang="less">
.backgroundImg_bg {
    width: 100%;
    height: 100%;
    position: relative;
    z-index:1;
    *{
        margin:0;
    }
    .node-view-image {
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
    }
   ::v-deep(p){
        margin:0;
        padding:0;
    }

}
</style>