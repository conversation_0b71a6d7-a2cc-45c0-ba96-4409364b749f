<template>
  <div class="umo-node-view__content" :style="styled">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { borderTopWidth } from "html2canvas/dist/types/css/property-descriptors/border-width";
import { ref, computed } from "vue";
const props = defineProps({
  id: String,
  backgroundImage: String,
  backgroundColor: String,
  backgroundPosition: String,
  backgroundSize: String,
  backgroundRepeat: String,
  borderWidth: [Number, String],
  borderStyle: String,
  borderColor: String,
  borderRadius: String,
  paddingTop: [Number, String],
  paddingRight: [Number, String],
  paddingBottom: [Number, String],
  paddingLeft: [Number, String],
  fitView: Boolean,
  borderRadiusTopLeft: [Number, String],
  borderRadiusTopRight: [Number, String],
  borderRadiusBottomLeft: [Number, String],
  borderRadiusBottomRight: [Number, String],
  borderTopWidth: [Number, String],
  borderRightWidth: [Number, String],
  borderBottomWidth: [Number, String],
  borderLeftWidth: [Number, String],
  borderTopStyle: String,
  borderRightStyle: String,
  borderBottomStyle: String,
  borderLeftStyle: String,
  borderTopColor: String,
  borderRightColor: String,
  borderBottomColor: String,
  borderLeftColor: String,
});

const styled = computed(() => {
  return {
    background: `url(${props.backgroundImage}) ${props.backgroundColor}`,
    "background-size": props.backgroundSize || "auto",
    "background-position": props.backgroundPosition || "center",
    "background-repeat": props.backgroundRepeat || "no-repeat",
    border: `${props.borderWidth || 0}px ${props.borderStyle || "solid"} ${props.borderColor || "transparent"}`,
    "border-radius": `${props.borderRadius}px` || "0",
    "padding-left": `${props.paddingLeft}px` || "0",
    "padding-right": `${props.paddingRight}px` || "0",
    "padding-top": `${props.paddingTop}px` || "0",
    "padding-bottom": `${props.paddingBottom}px` || "0",
    overflow: "hidden",

    "border-top-left-radius": `${props.borderRadiusTopLeft}px` || "0",
    "border-top-right-radius": `${props.borderRadiusTopRight}px` || "0",
    "border-bottom-left-radius": `${props.borderRadiusBottomLeft}px` || "0",
    "border-bottom-right-radius": `${props.borderRadiusBottomRight}px` || "0",
    "border-top-width": `${props.borderTopWidth}px` || "0",
    "border-right-width": `${props.borderRightWidth}px` || "0",
    "border-bottom-width": `${props.borderBottomWidth}px` || "0",
    "border-left-width": `${props.borderLeftWidth}px` || "0",
    "border-top-style": props.borderTopStyle || "solid",
    "border-right-style": props.borderRightStyle || "solid",
    "border-bottom-style": props.borderBottomStyle || "solid",
    "border-left-style": props.borderLeftStyle || "solid",
    "border-top-color": props.borderTopColor || "transparent",
    "border-right-color": props.borderRightColor || "transparent",
    "border-bottom-color": props.borderBottomColor || "transparent",
    "border-left-color": props.borderLeftColor || "transparent",
    "margin-top": props.fitView ? "0px" : "16px",
  };
});
</script>
<style scoped lang="scss">
.umo-node-view__content {
  ::v-deep(.Papers){
    margin-top:0px;
    margin-bottom: 16px;
  }
  ::v-deep(section){
    margin-bottom: 16px;
  }
  ::v-deep(p) {
    margin-bottom: 16px;
    word-break: break-all;
  }
  ::v-deep(.__orderTemplateBgUrl__){
    margin-bottom: 16px;
  }
  ::v-deep(.code-bg) {
    margin-bottom: 16px;
  }
  ::v-deep(.column-item) {
    margin: 0 !important;
    padding: 0;
  }
  ::v-deep(.umo-node-view-extended-reading .extended-reading-template-bg .extended-left .extended-name){
    width: 100%;
  }
  ::v-deep(.video-head-title){
    width: 70px;
  }
  ::v-deep(.collapse-main .left .name){
    width: 180px;
  }
  ::v-deep(.File){
    margin-top: 0;
  }

  ::v-deep(.ResourceCover){
    margin-top: 0;
    margin-bottom: 16px;
  }
  ::v-deep(.collapse-main){
    margin-top: 0;
  }
  ::v-deep(.videoMain){
    margin-top: 0;
    margin-bottom: 16px;
  }
ol {
    list-style-position: inside;
    padding:0;
    margin:0;
  }

  ::v-deep(*):last-child{
    margin-bottom: 0;
  }

}


</style>
