<template>
   <svg
    id="Layer_1"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    x="0px"
    y="0px"
    viewBox="0 0 453.5 453.5"
    style="enable-background: new 0 0 453.5 453.5"
    xml:space="preserve"
    :fill="currentColor"
    :stroke="currentColor"
    stroke-width="0"
    width="1em"
    height="1em"
  >
    <path
      d="M417.6,44.7c-2.1-6.3-7.3-11.1-13.8-12.7c-3.4-0.8-83.7-20.7-177.6-20.7S52,31.2,48.6,32.1c-6.4,1.6-11.6,6.4-13.8,12.7
	c-1,2.8-23.5,70.3-23.5,160.8c0,93.7,22.7,158.4,23.7,161.1c2.2,6.3,7.5,11,14.1,12.5c2.6,0.6,61,13.8,116.4,18.1
	c17.8,19.4,47.5,44,62.4,43.9c11.9-0.9,42.4-24.9,59.2-43.9c55.3-4.3,113.8-17.5,116.4-18.1c6.5-1.5,11.9-6.3,14.1-12.6
	c1-2.8,23.6-68.6,23.6-161C441.1,115,418.6,47.5,417.6,44.7L417.6,44.7z M383.9,343.4c-20.6,4.2-66.7,13-107.7,15.5
	c-5.8,0.4-11.1,3.2-14.6,7.9c-9,12.2-25.5,25.3-35.3,32.2c-9.8-6.9-26.3-20-35.3-32.2c-3.4-4.7-8.8-7.6-14.6-7.9
	c-41-2.5-87.1-11.2-107.7-15.5c-6-20.7-18.2-71.7-18.2-137.8c0-64.5,12.5-116.8,18.4-138.1c24.3-5.3,86.6-17.1,157.4-17.1
	s133.1,11.8,157.5,17.1c5.9,21.2,18.4,73.6,18.4,138C402.1,271.8,389.8,322.5,383.9,343.4L383.9,343.4z M246.3,206.8
	c0,10.8-8.8,19.5-19.5,19.5s-19.5-8.8-19.5-19.5s8.8-19.5,19.5-19.5S246.3,196,246.3,206.8z M324.7,207.3c0,10.8-8.8,19.5-19.5,19.5
	s-19.5-8.8-19.5-19.5s8.8-19.5,19.5-19.5S324.7,196.5,324.7,207.3z M167.7,206.8c0,10.8-8.8,19.5-19.5,19.5s-19.5-8.8-19.5-19.5
	s8.8-19.5,19.5-19.5S167.7,196,167.7,206.8z"
    />
  </svg>
</template>
<script setup>
import { ref } from 'vue'
const props = defineProps({
  color: {
    type: String,
    default: '#000000',
  }
})
const currentColor = ref(props.color)
</script>
