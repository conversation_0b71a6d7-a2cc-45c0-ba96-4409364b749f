<template>
    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="26" height="26" fill="none" />
        <path
            d="M13 2C6.9256 2 2 6.9256 2 13C2 19.0744 6.9256 24 13 24C19.0744 24 24 19.0744 24 13C24 6.9256 19.077 2 13 2ZM18.2483 13.506L11.5027 20.2516C10.9477 20.8066 10.0002 20.4142 10.0002 19.6294V6.97982C10.0002 6.22342 10.8909 5.81812 11.4614 6.31636L18.2096 12.2178C18.5891 12.556 18.6097 13.1446 18.2483 13.506Z"
            :fill="currentColor" />
    </svg>

</template>
<script setup>
import { ref } from 'vue'
const props = defineProps({
    color: {
        type: String,
        default: '#000000',
    },

})
const currentColor = ref(props.color)
watch(() => props.color, (newVal) => {
    currentColor.value = newVal
})
</script>