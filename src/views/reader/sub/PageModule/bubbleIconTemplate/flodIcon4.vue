<template>
    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="26" height="26" fill="none" />
        <path d="M11.1705 3H2L8.25329 13L2 23H11.1705L17.4238 13L11.1705 3Z" :fill="currentColor" />
        <path d="M17.7468 3H13.7979L20.0511 13L13.7979 23H17.7468L24.0001 13L17.7468 3Z" :fill="currentColor" />
    </svg>
</template>
<script setup>
import { ref } from 'vue'
const props = defineProps({
    color: {
        type: String,
        default: '#000000',
    },

})
const currentColor = ref(props.color)
watch(() => props.color, (newVal) => {
    currentColor.value = newVal
})
</script>