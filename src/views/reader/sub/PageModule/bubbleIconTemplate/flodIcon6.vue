<template>


    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="26" height="26" fill="none" />
        <path d="M14.5423 24L11.2515 20.5385L18.4182 13L11.2515 5.46154L14.5423 2L24.9999 13L14.5423 24Z"
            :fill="currentColor" />
        <path d="M4.43874 9.3834L1 13.0005L4.43874 16.6176L7.87748 13.0005L4.43874 9.3834Z" :fill="currentColor" />
        <path d="M11.4553 9.3834L8.0166 13.0005L11.4553 16.6176L14.8941 13.0005L11.4553 9.3834Z" :fill="currentColor" />
    </svg>

</template>
<script setup>
import { ref } from 'vue'
const props = defineProps({
    color: {
        type: String,
        default: '#000000',
    },

})
const currentColor = ref(props.color)
watch(() => props.color, (newVal) => {
    currentColor.value = newVal
})
</script>