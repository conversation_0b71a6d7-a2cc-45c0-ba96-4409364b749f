<template>
  <svg
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    class="responsive-svg"
  >
    <g clip-path="url(#clip0_518_6728)">
      <path
        d="M15.1566 6.00132C15.7017 6.00132 16.1462 6.45116 16.1462 7.00154C16.1462 7.55193 15.7017 8.00176 15.1566 8.00176H7.25536C6.71026 8.00176 6.26572 7.55193 6.26572 7.00154C6.26572 6.45116 6.71026 6.00132 7.25536 6.00132H15.1566ZM22.772 23.7089C22.3857 24.1006 21.7612 24.1006 21.3749 23.7089L18.9987 21.301C18.2948 21.7561 17.4745 21.9996 16.6437 21.9996C14.1881 21.9996 12.1982 19.9832 12.1982 17.5012C12.1982 15.0192 14.1881 13.0029 16.6437 13.0029C19.0992 13.0029 21.0891 15.0192 21.0891 17.5012C21.0891 18.348 20.8457 19.1735 20.3958 19.888L22.772 22.2959C23.1583 22.6875 23.1583 23.3173 22.772 23.7089ZM16.6384 20.0044C18.0038 20.0044 19.1098 18.8825 19.1098 17.5065C19.1098 16.1252 18.0038 15.0086 16.6384 15.0086C15.273 15.0086 14.1669 16.1305 14.1669 17.5065C14.1722 18.8825 15.2783 20.0044 16.6384 20.0044ZM12.193 21.9996H6.26572C4.63044 21.9996 3.30211 20.6553 3.30211 18.9989V5.0011C3.30211 3.34465 4.63044 2.00044 6.26572 2.00044H18.1202C18.6653 2.00044 19.1098 2.45028 19.1098 3.00066V11.0024C19.1098 11.5528 19.5544 12.0026 20.0995 12.0026C20.6446 12.0026 21.0891 11.5528 21.0891 11.0024V3.00066C21.0891 1.34421 19.7608 0 18.1255 0H6.27101C3.54025 0.00529217 1.33342 2.23859 1.32812 5.0011V19.0042C1.33342 21.7667 3.54025 24 6.26572 24.0053H12.193C12.738 24.0053 13.1826 23.5555 13.1826 23.0051C13.1826 22.4494 12.738 21.9996 12.193 21.9996Z"
        :fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_518_6728">
        <rect width="24" height="24" :fill="currentColor" />
      </clipPath>
    </defs>
  </svg>
</template>
<script setup>
import { ref } from 'vue'
const props = defineProps({
  color: {
    type: String,
    default: '#000000',
  }
  
})
const currentColor = ref(props.color)
</script>
