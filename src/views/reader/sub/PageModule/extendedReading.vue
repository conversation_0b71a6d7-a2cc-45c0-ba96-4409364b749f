<template>
  <div :id="id" ref="containerRef" class="umo-node-view">
    <div class="umo-node-view-extended-reading">
      <div
        class="extended-reading-template-bg __orderTemplateBgUrl__"
        style="height: 55px;background-size: cover;"
        @click="toggle"
      >
     
        <div class="extended-left">
          <div class="extended-icon">
            <img
              :src="templateStyle?.theme === 'light' ? daskReadLight : daskRead"
              alt=""
            />
          </div>
          <div class="extended-name" :style="{color:templateStyle?.theme === 'light' ? '#fff' : '#333'}">
            <el-tooltip effect="dark" :content="name" placement="top">
              {{ name }}
            </el-tooltip>
          </div>
        </div>
        <div class="extended-right" :style="{color:templateStyle?.theme === 'light' ? '#fff' : '#333'}">
          {{ toggleVisible ? "收起" : "展开" }}
        </div>
      </div>

      <div v-if="toggleVisible" class="extended-reading-template-content">
        <slot />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import daskReadLight from "@/assets/resources/light/read.svg";
import daskRead from "@/assets/resources/dark/read.svg";
import useReader from "@/store/modules/reader";
const props = defineProps({
  id: String,
  name: String,
  isExpand: Boolean,
});
const store = useReader();
const toggleVisible = ref(props.isExpand);
const toggle = () => {
  toggleVisible.value = !toggleVisible.value;
};
const templateStyle = computed(() => {
  const obj = {};
  let tmpStyle = store.templateStyle;
  obj.theme = tmpStyle?.theme;
  return obj;
});
</script>

<style lang="scss" scoped>
.umo-node-view {
  background-color: transparent;
  margin-top:16px;
}
.umo-node-view-extended-reading {
  position: relative;
  line-height: 30px;
  cursor: pointer;
  width: 100%;
  .icon {
    position: absolute;
    right: 10px;
    top: 14px;
    z-index: 99;
    font-size: 24px;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: #fff;
    padding: 5px;
    width: 18px;
    height: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .extended-reading-template-bg {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    .extended-left {
      display: flex;
      align-items: center;
      .extended-icon {
        width: 24px;
        height: 24px;
        img {
          width: 100%;
          height: 100%;
        }
      }

      .extended-title {
        
        font-size: 16px;
        font-weight: 400;
      }

      .extended-name {
        color: #333;
        margin-left: 10px;
        font-size: 16px;
        white-space: nowrap; /* 确保文本在一行内显示 */
        overflow: hidden; /* 超出容器部分的文本隐藏 */
        text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
      }
    }

    .extended-right {
      margin-right:3px;
      font-size: 16px;
      cursor: pointer;
    }
  }
  .extended-reading-template-content {
    background-color: #f6f6f6;
    border: 1px solid #ebebeb;
    padding: 20px 16px;
    width: 100%;
    min-height: 100px;
    box-sizing: border-box;
    font-size: 18px;
    color: #333;
    word-break: break-all;
    white-space: pre-wrap;
    overflow: hidden;
    line-height: 1.6;
  }
}
</style>
