import Links from './Links.vue'
import IimageLayout from './IimageLayout.vue'
import ImageInLine from './ImageInLine.vue'
import Video from './Video.vue'
import File from './File.vue'
import Audio from './Audio.vue'
import CodeBlock from './CodeBlock.vue'
import DividingLine from './DividingLine.vue'
import BubbleView from './BubbleView.vue'
import ResourceCover from './ResourceCover.vue'
import ImageGallery from './ImageGallery.vue'
import Interaction from './Interaction.vue'
import ChapterHeader from './ChapterHeader.vue'
import JointHeader from './JointHeader.vue'
import Questions from './Questions.vue'
import Papers from './Papers.vue'
import PsychologyHealth from './PsychologyHealth.vue'
import TablePlus from './TablePlus.vue'
import PaperWrapping from './PaperWrapping.vue'
import Image from  './Image.vue'
import LayoutColumn from './layuotColumn.vue'
import ColumnItem from './columnItem.vue'
import Table from './Table.vue'
import ImageIcon from './ImageIcon.vue'
import ExtendedReading from './extendedReading.vue'
import audioHanlin from './audioHanlin.vue'
import Fold from './fold.vue'
import blockView from './blockView.vue'
import surround from './surround.vue'
import formulaInLine from './formulaInLine.vue'
import backgroundImg from './backgroundImg.vue'
/**
 * key : components
 * key 需要与extensions文件夹下index.ts 配置自定义组件文件内name type保持一致
 * */
export default {
  links: Links,
  imageLayout: IimageLayout,
  imageInLine: ImageInLine,
  imageIcon: ImageInLine,
  video: Video,
  file: File,
  audio: Audio,
  codeBlock: CodeBlock,
  horizontalRule: DividingLine,
  bubbleInline: BubbleView,
  resourceCover: ResourceCover,
  imageGallery: ImageGallery,
  interaction: Interaction,
  chapterHeader: ChapterHeader,
  jointHeader: JointHeader,
  questions: Questions,
  papers: Papers,
  psychologyHealth: PsychologyHealth,
  tablePlus: TablePlus,
  paperWrapping: PaperWrapping,
  image: Image,
  layoutColumn: LayoutColumn,
  columnItem: ColumnItem,
  table:Table,
  imageIcon: ImageIcon,
  extendedReading: ExtendedReading,
  audioHanlin:audioHanlin,
  fold:Fold,
  blockView:blockView,
  surround:surround,
  formulaInLine:formulaInLine,
  backgroundImg:backgroundImg
}
