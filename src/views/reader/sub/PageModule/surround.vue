<template>
  <div class="surround-main">
    <div :class="basicFloat" :style="`${basicPadding}`">
      <img :src="imageUrl" :style="`width:${width}px;height:${height}px`" />
      <div class="basic-title" :style="{ color: color,width:width+'px'}">
       <div class="basic-title-number" v-if="isShowNo=='1'">
        {{ number }}
       </div>
       <div v-if="isShowImageTitle=='1'">
        {{ text }}
       </div>
        
      </div>
    </div>
    <p style="word-wrap: break-word;">
      <slot></slot>
    </p>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";

const props = defineProps({
  surroundType: {
    type: String,
    default: "",
  },
  imageUrl: {
    type: String,
    default: "",
  },
  width: {
    type: [Number, String],
    default: "",
  },
  height: {
    type: [Number, String],
    default: "",
  },
  spacing: {
    type: [Number, String],
    default: "",
  },
  text: {
    type: String,
    default: "",
  },
  color: {
    type: String,
    default: "",
  },
  isShowNo:{
    type: String,
    default: "1",
  },
  number:{
    type: String,
    default: "",
  },
  isShowImageTitle:{
    type: String,
    default: "1",
  }
});

const basicFloat = computed(() => {
  switch (props.surroundType) {
    case "rightTop":
      return "basic-float-right";
    case "leftTop":
      return "basic-float-left";
    default:
      return "basic-float-left";
  }
});

const basicPadding = computed(() => {
  switch (props.surroundType) {
    case "rightTop":
      return `margin-left:${props.spacing}px;margin-bottom:${props.spacing}px;`;
    case "leftTop":
      return `margin-right:${props.spacing}px;margin-bottom:${props.spacing}px;`;
    default:
      return "basic-padding";
  }
});
</script>

<style lang="scss" scoped>
.surround-main {
  width: 100%;
  overflow: hidden;
  margin-top:20px;
  min-height: 150px;
  .basic-float {
    float: left;
    background: #eee;
    margin-right: 20px;
    margin-bottom: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    .img {
      width: 100%;
      height: 100%;
    }
  }

  .basic-float-right {
    float: right;
    shape-outside: margin-box; /* 可选：改善文字环绕效果 */
    clip-path: inset(0 0 0 0);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    .basic-title {
      padding: 10px 0;
      color: #333;
      word-break: break-all;
      display: flex;
      font-size: 18px;
      .basic-title-number{
        margin-right: 5px;
        width: 40px;
        flex-shrink: 0;
      }
     
    }
  }

  .basic-float-left {
    float: left;
    shape-outside: margin-box; /* 可选：改善文字环绕效果 */
    clip-path: inset(0 0 0 0);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    .basic-title {
      padding: 10px 0;
      color: #333;
      word-break: break-all;
    }
  }
}
</style>
