<!-- 模块说明 -->
<style lang="scss" scoped>
.Bookmark {
  & > img {
    width: 27px;
    height: 33px;
    cursor: pointer;
  }
}
</style>

<template>
  <div class="Bookmark">
    <img
      src="@/assets/images/readerHeader/bookmark_c.svg"
      title="移除书签"
      @click="removeBookmark"
      v-if="pageMarkedItem"
    />
    <img
      src="@/assets/images/readerHeader/bookmark_1.svg"
      title="添加书签"
      @click="addBookmark"
      v-else-if="pageMarkedItem === null"
    />
    <img
      src="@/assets/images/readerHeader/bookmark_warning.png"
      alt="书签失败"
      v-else
    />
  </div>
</template>

<script setup>
import { saveBookMark, removeBookMark, getBookMark } from "@/api/book/reader";
import { ElMessage } from "element-plus";
import { getToken } from "@/utils/auth";
import { ref, onMounted, defineProps, computed } from "vue";
import useReader from "@/store/modules/reader";
const store = useReader();
const props = defineProps({
  pageIndexInBook: Number,
  pageIndexInChapter: Number,
  chapterId: String,
  dom: Node,
});

const pageMarkedItem = computed(() => {
  const chapterId = store.chapterId;
  const pageMarked = store.bookMarkData.find(
    (ele) =>
      ele.pageNumber === props.pageIndexInChapter && ele.chapterId === chapterId
  );
  return pageMarked ?? null;
});

/**获取书签列表*/
const getBookMarks = () => {
  if (!getToken()) return;
  getBookMark(store.comprehensiveBookData.bookId).then((res) => {
    if (res.code === 200) store.bookMarkData = res.data;
  });
};

/**
 * 添加书签
 */
const addBookmark = async () => {
  const innerText = props.dom.innerText?.slice(0, 40);
  const pageContent = innerText.replace(/[\r\n\s]+/g, "");
  const data = {
    bookId: store.comprehensiveBookData.bookId,
    chapterId: store.chapterId,
    pageNumber: props.pageIndexInChapter,
    pageContent,
  };
  const res = await saveBookMark(data);
  if (res.code === 200) {
    ElMessage.success("添加书签成功！");
    getBookMarks();
  } else {
    ElMessage.error("添加失败，请检查后重试！");
  }
};

/**
 * 删除书签
 */
const removeBookmark = async () => {
  if (!getToken()) return;
  const res = await removeBookMark(pageMarkedItem.value.markId);
  if (res.code === 200) {
    ElMessage.success("删除书签成功！");
    getBookMarks();
  } else {
    ElMessage.error("操作失败，请检查后重试！");
  }
};

onMounted(() => {});
</script>
