<!-- 传统书籍的阅读形式，可能会包括对开，即双页同时显示的阅读形式、单页显示的阅读形式 -->
<style lang="scss" scoped>
.Pages {
  height: 100%; // calc(100% - 12px);
  position: relative;
  width: 937px;
  margin: 0 auto;

  .Pages-icon-left,
  .Pages-icon-right {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--pageTurningIconBackground);
    box-shadow: var(--boxShadow);
    color: var(--pageTurningIconColor);
    font-size: 30px;
    cursor: pointer;
    position: absolute;
    top: 40vh;
    // transform: translateY(-50%);
  }
  .Pages-icon-left {
    left: -100px;
    .el-icon {
      margin: 10px 0 0 10px;
    }
  }
  .Pages-icon-right {
    right: -100px;
    .el-icon {
      margin: 10px 0 0 10px;
    }
  }
  .Pages-box {
    // display: flex;
    overflow-y: auto;
  }
}
</style>
<style lang="scss">
.folio-simulation {
  .Pages-item {
    .Pages-item-main {
      // 设置一个最小高度，这样当某一页内容比较少时候，内容区域也可以撑起来，占满整个屏幕。
      min-height: calc(100vh - 96px - 96px - 48px);
    }
  }
}
</style>

<template>
  <div class="Pages folio-simulation">
    <div class="Pages-icon-left" @click="pageTurning('last')">
      <el-icon><ArrowLeftBold /></el-icon>
    </div>
    <div class="Pages-box" :id="store.onePageBoxIdName" :class="{ 'first-chapter': store.isFirstChapter()}">
      <Pages_r_item
        :has-bookmark="item.hasAccessToChapter"
        :minHeight="false"
        v-for="(item, index) in data"
        :key="item.attrs.id"
        :content="item.content"
        :pageId="item.attrs.id"
        :index="index"
        :pageIndexInBook="
          store.comprehensiveBookData.pageIndexInBook4FirstPageInChapter + index
        "
        :chapterId="item.chapterId"
        :pageIndexInChapter="index + 1"
        :pageItem="item"
      />
    </div>
    <div
      class="Pages-icon-right"
      @click="pageTurning('next')"
    >
      <el-icon><ArrowRightBold /></el-icon>
    </div>
  </div>
</template>

<script setup>
import "@/utils/turn.js";
import { ArrowLeftBold, ArrowRightBold } from "@element-plus/icons-vue";
import { defineProps, ref, watch, nextTick } from "vue";
import Pages_r_item from "./Pages_r_item.vue";
import useReader from "@/store/modules/reader";

const store = useReader();
const idName = `#${store.onePageBoxIdName}`;

const data = ref([]);

let ifFlipInitedOnce = false;

watch(
  () => store.pageData,
  (val) => {
    if (val && val.length > 0) {
      data.value = val;
      // const cumulatedPreviousPageInTotal = store.calculateTotalPagesBeforeCurrentChapter();
      // let onInitPageIndexInChapter = store.comprehensiveBookData.currentPageIndex - cumulatedPreviousPageInTotal;
      // onInitPageIndexInChapter不能是0，兼容性处理
      // onInitPageIndexInChapter = onInitPageIndexInChapter || 1;
      nextTick(() => {
        onTurn(1, val);
      });
    }
  },
  { immediate: true }
);

watch(
  () => [store.styleSetting, store.themeBackground, store.theme],
  () => {
    store.setPageFlippingMethod("");
    setTimeout(() => {
      store.setPageFlippingMethod("r");
    }, 100);
  },
  { deep: true }
);

const pageTurning = (type) => {
  let pageLoading = null;
  if (type === "last") {
    pageLoading = store.lastPage();
  } else if (type === "next") {
    pageLoading = store.nextPage();
  }
};

/**
 * 更改业角触发范围 @/utils/turn.js文件 100行 flipOptions.cornerSize
 */
const onTurn = (pageNum, pageData) => {
  let destroyHost = $(idName)
  if (ifFlipInitedOnce) {
    destroyHost = $(idName).turn('destroy')
  } else {
    ifFlipInitedOnce = true
    destroyHost = $(idName)
  }
  destroyHost.turn({
    elevation: 20,
    pages: pageData.length,
    display: "single", //显示：single=单页，double=双页，默认双页
    page: pageNum, // 默认显示第几页
    gradients: true, // 折叠处的光泽渐变，主要体现翻页的立体感、真实感
    acceleration: true,
    autoCenter: false, //
    height: "100%", //页面高度
    width: 937, //翻书范围宽度，总宽度
  });
  $(idName).bind("end", function (event, pageObject, turned) {
    if (turned) {
      const [num] = $(idName).turn("view");
    }
  });
};
</script>
