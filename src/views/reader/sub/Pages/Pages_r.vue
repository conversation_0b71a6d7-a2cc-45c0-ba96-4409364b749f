<!-- 模块说明 -->
<!--
<style lang="scss" scoped>
.Pages {
  width: 100%;
  height: calc(100% - 12px);
  display: flex;
  justify-content: space-around;
  .Pages-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--pageTurningIconBackground);
    box-shadow: var(--boxShadow);
    color: var(--pageTurningIconColor);
    font-size: 30px;
    cursor: pointer;
    margin-top: 38vh;
    margin-left: 80px;
  }
  .Pages-icon-right {
    margin-left: 0;
    margin-right: 80px;
  }
  .Pages-box {
    position: relative;
    width: calc(160vh + 20px);
    & > section {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      margin-left: 0 !important;
    }
  }
}

</style>
-->

<template>
<!--
  <div class="Pages" :key="key">
    <div class="Pages-icon __flex-center__" @click="store.lastPage">
      <el-icon><ArrowLeftBold /></el-icon>
    </div>
    <div class="Pages-box" ref="boxRefs">
      <section :id="store.twoPageBoxIdName">
        <Pages_r_item v-for="(item, index) in data" :key="item.attrs.id" :pageId="item.attrs.id" :content="item.content" :index="index" />
      </section>
    </div>
    <div class="Pages-icon __flex-center__ Pages-icon-right" @click="store.nextPage">
      <el-icon><ArrowRightBold /></el-icon>
    </div>
  </div>
-->
</template>

<script setup>
/*
import '@/utils/turn.js'
import { ArrowLeftBold, ArrowRightBold } from '@element-plus/icons-vue'
import { ref, onMounted, defineProps, watch, nextTick } from 'vue'
import useReader from '@/store/modules/reader'
import Pages_r_item from './Pages_r_item.vue'
const store = useReader()
const idName = `#${store.twoPageBoxIdName}`
const props = defineProps({
  pageData: {
    type: Array,
    defaule: () => [],
    required: true,
  },
})
const boxRefs = ref(null)
const data = ref([])
const key = ref(1)
const onTurn = async () => {
  key.value += 1
  await nextTick()
  let timer = setInterval(() => {
    const DOM = document.querySelector(idName)
    const len = Array.from(DOM.childNodes).filter((ele) => ele.nodeName !== '#text').length
    if (len === data.value?.length) {
      $(idName).turn({
        display: 'double', //显示：single=单页，double=双页，默认双页
        page: store.comprehensiveBookData.currentPageIndex + 1, // 默认显示第几页
        gradients: true, // 折叠处的光泽渐变，主要体现翻页的立体感、真实感
        autoCenter: true, //
        // turnCorners: '', // 设置可翻页的页角(都试过了，乱写 4个角都能出发卷起)， bl,br   tl,tr   bl,tr
        height: '100%', //页面高度
        width: boxRefs.value.clientWidth, //翻书范围宽度，总宽度
      })
      $(idName).bind('end', function (event, pageObject, turned) {
        if (turned) {
          const [num] = $(idName).turn('view')
          if (num === 0) {
            store.setPageNumber(0)
          } else {
            store.setPageNumber(num - 1)
          }
        }
      })
      clearInterval(timer)
    }
  }, 0)

  let _t = setTimeout(() => {
    clearInterval(timer)
    clearTimeout(_t)
  }, 3000)
}

watch(
  () => [props.pageData, store.styleSetting, store.themeBackground, store.theme],
  (val, oldVal) => {
    if (val[0] !== oldVal[0]) data.value = val[0]
    onTurn()
  },
  { deep: true }
)

onMounted(() => {
  data.value = props.pageData
  onTurn()
})
*/
</script>
