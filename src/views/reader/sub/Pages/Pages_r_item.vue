<!-- 模块说明 -->
<style lang="scss">
.Pages-item-main-content {

  ul,
  ol {
    counter-reset: section;

    li {
      word-break: break-all;
      list-style-type: none;

      >p {
        display: flex;

        &::before {
          counter-increment: section;
        }
      }
    }
  }

  ul {
    li>p {
      .text-wrapper {
        padding-left: 10px;
      }
    }

    &[type='disc'] li>p::before {
      content: '•';
      width: 7px;
      flex-shrink: 0;
      display: flex;
    }

    &[type='circle'] li>p::before {
      content: '◦';
      width: 7px;
      flex-shrink: 0;
      display: flex;
    }

    &[type='square'] li>p::before {
      content: '▪';
      width: 7px;
      flex-shrink: 0;
      display: flex;
    }
  }

  ol {
    &[type='decimal'] li>p::before {
      content: counter(section) '.';
      display: flex;
      width: 25px;
      flex-shrink: 0;
    }

    &[type='decimal-leading-zero'] li>p::before {
      content: counter(section, decimal-leading-zero) '.';
      display: flex;
      width: 30px;
      flex-shrink: 0;
    }

    &[type='lower-roman'] li>p::before {
      content: counter(section, lower-roman) '.';
      display: flex;
      width: 40px;
      flex-shrink: 0;
    }

    &[type='upper-roman'] li>p::before {
      content: counter(section, upper-roman) '.';
      display: flex;
      width: 40px;
      flex-shrink: 0;
    }

    &[type='lower-alpha'] li>p::before {
      content: counter(section, lower-alpha) '.';
      display: flex;
    }

    &[type='lower-latin'] li>p:before {
      display: flex;

      width: 25px;
      flex-shrink: 0;
      content: counter(section, lower-latin) '.';
    }

    &[type='upper-latin'] li>p:before {
      display: flex;

      width: 25px;
      flex-shrink: 0;
      content: counter(section, upper-latin) '.';
    }

    &[type='trad-chinese-informal'] li>p:before {
      display: flex;

      width: 25px;
      flex-shrink: 0;
      content: counter(section, trad-chinese-informal) '.';
    }

    &[type='simp-chinese-formal'] li>p:before {
      display: flex;

      width: 25px;
      flex-shrink: 0;
      content: counter(section, simp-chinese-formal) '.';
    }
  }

  // 章头,节头保持不动
  // 仅修改段落内的css
  p:not(.header-title p) {
    //回退机制
    line-height: var(--line-height, initial);

    .text-wrapper {
      .reader-text:not(.header-title .reader-text) {
        font-size: var(--font-size);
        font-family: var(--font-family);
      }
    }
  }
}

.importCssLineHeight {
  p:not(.header-title p) {
    //切换
    line-height: var(--line-height) !important;
  }
}
</style>
<style lang="less" scoped>
.Pages-item {
  height: 100%;
  width: 100%;
  overflow: auto;

  // webkit
  ::-webkit-scrollbar {
    // display: none;
  }

  // firefox
  scrollbar-width: none;
  // IE/Edge
  -ms-overflow-style: none;

  .Page-item-header {
    width: 100%;
    height: 96px;
    position: relative;

    .Bookmark {
      position: absolute;
      top: 0;
      right: 20px;
    }
  }

  table {
    width: 100%;
    border-collapse: collapse;
    padding: 20px 0;
    word-break: break-all;
  }

  ::v-deep(td) {
    padding: 5px;
  }

  .Pages-item-main {
    font-family: "SimSun";
    flex: 1;
    padding: 0 3.18cm;
    font-size: 1.125rem;

    .Pages-item-main-content {
      height: 100%;
      flex: 1;
      overflow-y: auto;

      div>p {
        padding: 0;
        margin-top: 16px;
        word-wrap: break-word;
      }

      table {
        border-collapse: collapse;
        table-layout: fixed;
        width: 100%;
        margin: 15px 0;
        overflow: hidden;
        page-break-inside: auto;

        ::-webkit-scrollbar {
          display: block !important;
        }

        tr {
          page-break-inside: avoid;
          page-break-after: auto;
        }

        // thead {
        //   display: table-header-group;
        // }
        td,
        th {
          min-width: 1em;
          border: 1px solid var(--umo-content-table-border-color);
          padding: 3px 5px;
          vertical-align: middle;
          box-sizing: border-box;
          position: relative;

          >* {
            margin-bottom: 0;
          }

          &[data-align="left-top"] {
            vertical-align: top;
            text-align: left;
          }

          &[data-align="center-top"] {
            vertical-align: top;
            text-align: center;
          }

          &[data-align="right-top"] {
            vertical-align: middle;
            text-align: right;
          }

          &[data-align="justify-top"] {
            vertical-align: middle;
            text-align: justify;
          }

          &[data-align="left-middle"] {
            vertical-align: middle;
            text-align: left;
          }

          &[data-align="center-middle"] {
            vertical-align: middle;
            text-align: center;
          }

          &[data-align="right-middle"] {
            vertical-align: middle;
            text-align: right;
          }

          &[data-align="justify-middle"] {
            vertical-align: middle;
            text-align: justify;
          }

          &[data-align="left-bottom"] {
            vertical-align: bottom;
            text-align: left;
          }

          &[data-align="center-bottom"] {
            vertical-align: middle;
            text-align: center;
          }

          &[data-align="right-bottom"] {
            vertical-align: bottom;
            text-align: right;
          }

          &[data-align="justify-bottom"] {
            vertical-align: bottom;
            text-align: justify;
          }
        }

        th {
          font-weight: bold;
          text-align: left;
        }

        .selectedCell:after {
          z-index: 2;
          position: absolute;
          content: "";
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          background: var(--umo-content-table-selected-background);
          pointer-events: none;
        }

        .column-resize-handle {
          position: absolute;
          right: -1px;
          top: 0;
          bottom: -1px;
          width: 3px;
          background-color: var(--umo-primary-color);
          pointer-events: none;
        }

        p {
          margin: 0;
        }
      }
    }
  }

  .Page-item-footer {
    width: 100%;
    height: 96px;
    position: relative;

    .page-number {
      position: absolute;
      bottom: 20px;
      width: 100%;

      .page-number-text {
        width: 100%;
        padding: 0 3.18cm;
      }
    }
  }
}
</style>

<template>
  <div class="Pages-item" :style="`background-color:${store.themeBackground};${minHeight ? 'min-height: 1200px;' : ''}`"
    ref="pageItemRefs">
    <div class="Page-item-header" :style="templateStyle.show ? templateStyle.header : ''">
      <div class="Bookmark">
        <!-- 书签 -->
        <!-- !proxy.$route.query.cid后加，用于区分简易预览和常规打开，当编辑器打开预览时，不显示Bookmark -->
        <Bookmark v-if="hasBookmark" :pageIndexInBook="pageIndexInBook" :dom="pageItemRefs"
          :data-chapter-id="props.pageItem.chapterId" :pageIndexInChapter="pageItem?.attrs?._pageNumberInChapter" />
      </div>
    </div>
    <div class="Pages-item-main" :style="templateStyle.main" :data-chapter-id="pageItem?.chapterId"
      :data-page-number-chapter="pageItem?.attrs?._pageNumberInChapter"
      :class="'page-number-chapter-' + pageItem?.attrs?._pageNumberInChapter">
      <div :class="{ importCssLineHeight: confLineHeight }"
        class="Pages-item-main-content __hidden-scroll__ __pageItemContent__">
        <div v-for="(ele, i) in content" :key="ele.attrs.id"
          :style="`${ele.type == 'table' ? 'max-width: 690px;overflow-x:auto;' : ''}`">
          {{ getTableWidth(ele.attrs.id) }}
          <Paragraph :style="{
            '--font-size': confFontSize,
            '--line-height': confLineHeight,
            '--font-family': confFontFamily,
          }" :paragraphData="ele" :pageId="pageId" />
        </div>
      </div>
    </div>
    <div class="Page-item-footer" :style="templateStyle.show ? templateStyle.footer : ''">
      <div class="page-number">
        <div class="page-number-text" :style="templateStyle.pageNumber"> {{ pageIndexInBook }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, ref, computed, nextTick } from "vue";
import useReader from "@/store/modules/reader";
import Paragraph from "./Paragraph.vue";
import Bookmark from "./Bookmark.vue";
import { getCurrentInstance } from "vue";

const { proxy } = getCurrentInstance();

const store = useReader();
const pageItemRefs = ref(null);
const confFontSize = ref();
const confLineHeight = ref();
const confFontFamily = ref();
// 监听样式设置变化,解决文字大小不变的问题
watch(
  () => store.styleSetting["font-size"],
  (newVal, oldVal) => {
    confFontSize.value = newVal == "-1px" ? "" : newVal;
  },
  { deep: true, immediate: true }
);
// 监听样式设置变化,解决行高不变的问题
watch(
  () => store.styleSetting["line-height"],
  (newVal, oldVal) => {
    confLineHeight.value = newVal > 0 ? newVal : "";
  },
  { deep: true, immediate: true }
);
// 监听样式设置变化,解决字体不变的问题
watch(
  () => store.styleSetting["font-family"],
  (newVal, oldVal) => {
    confFontFamily.value = newVal;
  },
  { deep: true, immediate: true }
);


const props = defineProps({
  content: {
    type: Array,
    default: () => [],
  },
  // index: Number,
  pageId: String,
  minHeight: {
    type: Boolean,
    default: true,
  },
  pageIndexInBook: "",
  chapterId: "",
  pageIndexInChapter: "",
  hasBookmark: {
    type: Boolean,
    default: true,
  },
  pageItem: {
    type: Object,
    default: () => { },
  },
});
const templateStyle = computed(() => {
  const obj = {};
  let d = store.templateStyle;
  if (!d) return obj;
  if (d.headerUrl) {
    obj.header = {
      backgroundImage: `url(${d.headerUrl})`,
      backgroundRepeat: "no-repeat",
      backgroundSize: "contain",
      backgroundPosition: "top",
    };
  }
  if (d.footerUrl) {
    obj.footer = {
      backgroundImage: `url(${d.footerUrl})`,
      backgroundRepeat: "no-repeat",
      backgroundPosition: "bottom",
      backgroundSize: "contain",
    };
  }
  if (d.contentUrl) {
    obj.main = {
      backgroundImage: `url(${d.contentUrl})`,
      backgroundRepeat: "no-repeat",
      backgroundSize: "100% 100%",
      minHeight: props.minHeight ? "1200px" : "",
    };
  }
  if (d.pagesPosition === "center") {
    obj.pageNumber = {
      textAlign: "center",
      color: d.pagesFontColor,
    };
  } else if (d.pagesPosition === "left") {
    obj.pageNumber = { textAlign: "left", color: d.pagesFontColor };
  } else if (d.pagesPosition === "right") {
    obj.pageNumber = { textAlign: "right", color: d.pagesFontColor };
  }
  if (d.orderTemplateBgUrl) {
    const ID = "__orderTemplateBgUrl__";
    const s = document.getElementById(ID);
    if (s) s.remove();
    const STYLE = document.createElement("style");
    STYLE.id = ID;
    STYLE.innerText = `
      .__pageItemContent__ .el-collapse{
        border:0;
      }
      .__orderTemplateBgUrl__{
      width:100%;
        background-image: url(${d.orderTemplateBgUrl})!important;
        background-repeat: no-repeat; background-size: 100% 100%;
        }
        .__orderTemplateBgUrl__ .name,
        .__orderTemplateBgUrl__ .__name__{
         
        }
        .video-parent .el-collapse-item__header{
          background-image: url(${d.orderTemplateBgUrl})!important;
          background-repeat: no-repeat;
          background-size: cover;
          background-color: rgba(0,0,0,0);
          border: 0;
        }
        .video-parent .el-collapse-item__header .name{
          color:${d.orderTemplateColor}
        }
        `;
    document.querySelector("body").appendChild(STYLE);
  }
  if (props.content[0]) {
    if (props.content[0].type == "paperWrapping") {
      obj.show = false;
    } else {
      obj.show = true;
    }
  }
  return obj;
});

const getTableWidth = (value) => {
  if (value) {
    nextTick(() => {
      const tableDom = document.getElementById(`${value}`)
      const tableWidth = tableDom?.offsetWidth
      if (tableWidth && tableDom) {
        tableWidth > 691 ? tableDom.style.tableLayout = "fixed" : tableDom.style.tableLayout = "auto"
      }
    })
  }
}
</script>
