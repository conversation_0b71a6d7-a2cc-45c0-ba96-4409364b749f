<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-16 10:19:50
 * @LastEditTime: 2025-01-21 16:07:16
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Pages\Pages_x.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 横向翻页 -->
<!--
<style lang="scss" scoped>
.Pages {
  width: 100%;
  height: calc(100% - 12px);
  display: flex;
  justify-content: space-around;
  .Pages-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--pageTurningIconBackground);
    box-shadow: var(--boxShadow);
    color: var(--pageTurningIconColor);
    font-size: 30px;
    cursor: pointer;
    margin-top: 38vh;
    margin-left: 80px;
  }
  .Pages-icon-right {
    margin-left: 0;
    margin-right: 80px;
  }
  .Pages-box {
    display: flex;
    width: calc(160vh + 20px);
  }
  .Pages-item {
    width: 80vh;
    height: 100%;
    box-shadow: var(--boxShadow);
    margin: 0 5px;
    .Page-item-header {
      width: 100%;
      height: 40px;
      position: relative;
      .Bookmark {
        position: absolute;
        top: 0;
        right: 20px;
      }
    }
    .Pages-item-main {
      height: calc(100% - 80px);
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      .Pages-item-main-sideEdge {
        height: 100%;
        width: 40px;
      }
      .Pages-item-main-content {
        flex: 1;
      }
      .Pages-item-main-content_1 {
        flex: 1;
        overflow-y: auto;
      }
    }
    .Page-item-footer {
      width: 100%;
      height: 40px;
    }
  }
}
</style>
-->
<template>
<!--
  <div class="Pages">
    <div class="Pages-icon __flex-center__" @click="pageTurning('last')">
      <el-icon><ArrowLeftBold /></el-icon>
    </div>
    <div class="Pages-box" ref="pageItemRefs">
      <div class="Pages-item" :style="`background-color:${store.themeBackground}`">
        <div class="Page-item-header">
          <div class="Bookmark">
            <Bookmark :pageNumber="store.comprehensiveBookData.currentPageIndex + 1" :dom="pageItemRefs" />
          </div>
        </div>

        <div class="Pages-item-main">
          <div class="Pages-item-main-sideEdge"></div>
          <div class="Pages-item-main-content">
            <div v-for="(ele, i) in leftData" :key="i" ref="isDisplay">
              <Paragraph :paragraphData="ele" :pageId="pageData.attrs.id" />
            </div>
          </div>
          <div class="Pages-item-main-sideEdge"></div>
        </div>

        <div class="Page-item-footer"></div>
      </div>
-->
      <!-- 右边那页开始 -->
<!--
      <div class="Pages-item" :style="`background-color:${store.themeBackground}`">
        <div class="Page-item-header"></div>

        <div class="Pages-item-main">
          <div class="Pages-item-main-sideEdge"></div>
          <div class="Pages-item-main-content_1">
            <div v-for="(ele, i) in rightData" :key="i">
              <Paragraph :paragraphData="ele" :pageId="pageData.attrs.id" />
            </div>
          </div>
          <div class="Pages-item-main-sideEdge"></div>
        </div>

        <div class="Page-item-footer"></div>
      </div>
    </div>
    <div class="Pages-icon __flex-center__ Pages-icon-right" @click="pageTurning('next')">
      <el-icon><ArrowRightBold /></el-icon>
    </div>
  </div>
-->
</template>

<script setup>
/*
import { ArrowLeftBold, ArrowRightBold } from '@element-plus/icons-vue'
import { defineProps, ref, onMounted, nextTick, watch, onBeforeUnmount } from 'vue'
import Paragraph from './Paragraph.vue'
import useReader from '@/store/modules/reader'
import Bookmark from './Bookmark.vue'
const store = useReader()
let timer = null
let t = null
const key = ref(0)
const pageItemRefs = ref(null)
const isDisplay = ref([])
const leftData = ref([])
const rightData = ref([])
const displayStatus = ref([])
const props = defineProps({
  pageData: {
    type: Object,
    defaule: () => ({ content: [] }),
    required: true,
  },
})

const columnDivision = () => {
  leftData.value = props.pageData.content

  nextTick(() => {
    const OBlist = []
    Array.from(isDisplay.value).forEach((element, index) => {
      const OB = new IntersectionObserver(
        (entries) => {
          displayStatus.value[index] = entries[0]?.isIntersecting
          key.value += 1
        },
        { root: null, rootMargin: '0px', threshold: 1 }
      )
      OB.observe(element)
      OBlist.push(() => OB.unobserve(element))
    })
    const _t = setTimeout(() => {
      OBlist.forEach((ele) => {
        ele && ele()
      })
      clearTimeout(_t)
    }, 1000)
  })
}

const pageTurning = (type) => {
  if (type === 'last') {
    store.lastPage()
  } else if (type === 'next') {
    store.nextPage()
  }
}

watch(
  () => [displayStatus.value, key],
  () => {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      leftData.value = props.pageData.content.filter((ele, i) => displayStatus.value[i])
      rightData.value = props.pageData.content.filter((ele, i) => !displayStatus.value[i])
    }, 100)
  },
  { deep: true }
)

watch(
  () => [props.pageData.content, store.comprehensiveBookData.currentPageIndex, store.styleSetting],
  () => {
    columnDivision()
  },
  { deep: true }
)

const resize = () => {
  if (t) clearTimeout(t)
  t = setTimeout(() => {
    columnDivision()
  }, 100)
}

onMounted(() => {
  columnDivision()
  window.addEventListener('resize', resize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', resize)
})
*/
</script>
