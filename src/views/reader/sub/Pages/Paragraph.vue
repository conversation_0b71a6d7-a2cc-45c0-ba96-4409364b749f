<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-22 11:28:01
 * @LastEditTime: 2024-12-27 13:39:42
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Pages\Paragraph.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<script>
import { defineComponent } from 'vue'
import { getParagraph } from './ParagraphTool.js'

export default defineComponent({
  props: {
    paragraphData: {
      type: Object,
      default: () => ({}),
    },
    pageId: String,
  },
  setup(props) {
    return () => getParagraph(props.paragraphData,props.pageId)
  },
})
</script>
