/*
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-22 11:41:13
 * @LastEditTime: 2025-02-26 11:30:39
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Pages\ParagraphTool.js
 * @Description:
 *
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
 */
import { h, toRaw } from "vue";
import module from "../PageModule/index.js";

const isModule = (name) => !!module[name];

const getId = (paragraphId, textIndex) => {
  let name = paragraphId + "_" + textIndex;
  return name;
};

export function deriveKeyWordIndexFromComplexityId(wordId) {
  const wordIdDivs = wordId.split("_");
  const startIndex = Number.parseInt(wordIdDivs[wordIdDivs.length - 1]);
  if (isNaN(startIndex)) {
    return null;
  }
  return startIndex;
}
export function deriveParagraphIdFromKeywordComplexityId(wordId) {
  const wordIdDivs = wordId.split("_");
  return wordIdDivs[0].substring(1);
}

export function generateTextIdWithFromId(fromWordId, endWordId) {
  const startIndex = deriveKeyWordIndexFromComplexityId(fromWordId);
  const fromparaId = deriveParagraphIdFromKeywordComplexityId(fromWordId);

  const endIndex = deriveKeyWordIndexFromComplexityId(endWordId);
  const endparaId = deriveParagraphIdFromKeywordComplexityId(endWordId);
  if (fromparaId !== endparaId) {
    return null;
  }

  const idArr = [];
  for (let i = startIndex; i <= endIndex; i++) {
    idArr.push("#a" + fromparaId + "_" + i);
  }
  return idArr;
}

export function generateTextId({ paraId, startIndex, keywordLength = 1 }) {
  const idArr = [];
  for (let i = 0; i < keywordLength; i++) {
    idArr.push("#a" + paraId + "_" + (startIndex + i));
  }
  return idArr;
}

/**获取样式*/
const getStyle = (data, type) => {
  if (type === 'mark') {
    const { marks } = data
    if (!marks?.length) return {}
    const styles = {}
    Object.keys(data?.attrs?.margin || {}).forEach((key) => {
      const item = data?.attrs?.margin[key]
      if (item) {
        styles[`margin-${key}`] = `${item}px`
      }
    })

   
    if(data.type=='bubbleInline'){
   
      data.marks.forEach(item => {
        if (item.attrs && item.attrs.color) {
            item.attrs['border-bottom'] =' 1px solid '+ item.attrs.color;
        }
      });
    }
  
    marks.forEach((ele) => {
     
      switch (ele.type) {
        case 'lineHeight':
          styles.lineHeight = ele.attrs.lineHeight
          break
        case 'highlight': //高亮
          styles.backgroundColor = ele.attrs.color
          break

        case 'textStyle': //文字样式
          for (const key in ele.attrs) {
            const element = ele.attrs[key]
            if (element) {
              styles[key] = element
            }
          }
          if (styles.fontSize === '1.125rem') delete styles.fontSize
          break

        case 'textShadow': //文字阴影
          styles.textShadow = '2px 2px 2px'
          break

        case 'textGlow': //文字效果--光
          styles.textShadow =
            ' 0 0 5px rgba(255, 255, 0, 0.8),0 0 10px rgba(255, 255, 0, 0.8),0 0 20px rgba(255, 255, 0, 0.8),0 0 30px rgba(255, 255, 0, 1),0 0 40px rgba(255, 255, 0, 1),0 0 50px rgba(255, 255, 0, 1)'
          break
        case 'textBorder': //文字轮廓
          // styles.TextBorder =
          //   'text-shadow:-1px -1px 0 #000,1px -1px 0 #000,-1px 1px 0 #000,1px 1px 0 #000;' /* 边框宽度和颜色 */
          if (ele.attrs?.textBorder) {
            Object.keys(ele.attrs.textBorder).forEach(textAttr => {
              styles[textAttr] = ele.attrs.textBorder[textAttr];
            })
          }
          break
        case 'textStroke': //文字轮廓
          styles.TextStroke = '1px'
          styles.textFillColor = 'transparent'
          break

        case 'italic': //文字倾斜
          styles.fontStyle = 'italic'
          break

        case 'bold': //文字加粗
          styles.fontWeight = 'bold'
          break

        case 'underline': //下划线
          styles.textDecoration =styles.textDecoration?styles.textDecoration+' underline':'underline'
          break

        case 'strike': //中划线
          styles.textDecoration =styles.textDecoration?styles.textDecoration+' line-through':'line-through' 
          break
       

        case 'subscript': //下标
          // 当使用 sub 标签时，不需要添加 verticalAlign 样式
          // styles.verticalAlign = 'sub'
          break

        case 'superscript': //上标
          // 当使用 sup 标签时，不需要添加 verticalAlign 样式
          // styles.verticalAlign = 'super'
          break

        default:
          break
      }
    })

    for (const key in styles) {
      const element = styles[key]
      if (!element) {
        delete styles[key]
      }
    }
    return styles
  } else {
    let obj = {}
    try {
     
      if(!data.attrs) return {}
      const {  margin = {}, backgroundBorder = {}, columnCount } =
        data?.attrs
        
      if (
        backgroundBorder?.borderColor &&
        backgroundBorder?.borderWidth &&
        backgroundBorder?.borderStyle
      ) {
        obj.border = `${backgroundBorder.borderWidth}px ${backgroundBorder.borderStyle} ${backgroundBorder.borderColor}`
      }
      if (backgroundBorder?.borderRadius) {
        obj.borderRadius = `${backgroundBorder.borderRadius}px`
      }
      if (backgroundBorder?.borderPadding) {
        obj.padding = `${backgroundBorder.borderPadding}px`
      }

      // 处理分栏
      if (columnCount && columnCount > 0) {
        obj.columnCount = columnCount
      }

      obj = { ...obj,lineHeight: data?.attrs?.lineHeight,textAlign:data?.attrs.textAlign, listStyleType:data?.attrs.listType }
      Object.keys(margin).forEach((key) => {
        const item = margin[key]
        if (item) {
          obj[`margin-${key}`] = `${item}px`
        }
      })
    } catch (error) {
      console.error("🚀 ~ getStyle ~ error:", error)
    }
    return obj
  }
}

function headingAttrRetrieve(data) {
  // 编辑器优先采用dataTocId进行标题点击的跳转
  const id = data.attrs?.dataTocId || data.attrs?.id;
  return {
    "data-toc-id": id,
  };
}

/**获取标签名称*/
const getLableName = (data) => {
  const { type, attrs } = data;
  switch (type) {
    case "heading":
      return `h${attrs.level}`;

    case "paragraph":
      return `p`;

    case "text":
      return `span`;

    case "orderedList": //有序列表
      return `ol`;

    case "bulletList": //有序列表
      return `ul`;

    case "listItem": //有序列表
      return `li`;

    case "blockquote":
      return `blockquote`;

    case "link":
      return `a`;

    case "table":
      return `table`;

    case "tableRow":
      return `tr`;

    case "tableHeader":
      return `th`;

    case "tableCell":
      return `td`;
    case "hardBreak":
      return `br`;


    default:
      if (isModule(data.type)) {
        return module[data.type];
      }
      break;
  }
};

/**获取标签样式 或 给自定义组件传递参数*/
const getProps = (data, type, parentType) => {
  if (data.type === "tablePlus") {
    const props = { ...toRaw(data.attrs), content: data.content };
    return props;
  }
  if (!isModule(data.type)) {
    if (data.type === "tableHeader" || data.type === "tableCell") {
      const props = { ...toRaw(data.attrs) };
      delete props.align;
      delete props.backgroundColor;
      props["data-align"] = data.attrs.align;
      props.style = {
        ...getStyle(data, type),
        backgroundColor: data.attrs.backgroundColor,
      };
      if (data.attrs.borderStyle) {
        props.style.border = data.attrs.borderStyle;
      }
      if (data.attrs.borderColor) {
        props.style.borderColor = `${data.attrs.borderColor}`;
        if (!data.attrs.borderStyle) {
          props.style.borderWidth = '2px';
        }
      }
      return props;
    }

    if (type === "mark") {
      return { sign: "selection", class: "reader-text" };
    } else {
   
      let obj = {
        style: {
          ...getStyle(data),
       
         marginLeft:data.attrs?.listType==="lower-roman" || data.attrs?.listType === "upper-roman"?"15px":'',

         color:data.attrs?.color||'',
        },
      };

      if (parentType === "tableHeader" || parentType === "tableCell") {
        delete obj.style.marginTop;
        // delete obj.style.textAlign;
      }

    
     
      if (data?.attrs) {
       
        const { containerColor, backgroundImage, backgroundSize,backgroundRepeat, indent, listType } =
          data?.attrs;
          
        if (containerColor) {
          obj.style.backgroundColor = containerColor;
        }

        if (backgroundImage) {
          obj.style.backgroundImage = `url(${backgroundImage})`;
        }
        if(backgroundRepeat){
          obj.style.backgroundRepeat = backgroundRepeat;
        }
        if (backgroundSize) {
          obj.style.backgroundSize = backgroundSize;
          if (backgroundSize === "auto") {
            obj.style.backgroundRepeat = "repeat";
          } else {
            obj.style.backgroundRepeat = "no-repeat";
          }
        }else{
          obj.style.backgroundSize='cover'
          obj.style.wordBreak='break-word'
        }
        if (indent) {
          obj.style.textIndent = indent * 2 + "em";
        }
        if(listType){
          obj.type=listType
        }

       
      }

      if (data.type === "heading") {
        obj = {
          ...obj,
          ...headingAttrRetrieve(data),
        };
      }
      if (data.type === 'paragraph') {
        obj = {
          ...obj,
          id: data.attrs.id
        }
       
        if(obj.style.textAlign==='distributed'){
          obj.style.textAlignLast='justify'
        }
      }

      if (data.type === 'orderedList') {
        if (data.attrs.start) {
           obj.start = data.attrs.start
        }
    }

    if(data.type=='listItem'){

      data.content.forEach(item => {
         
               const paragraph = item.content?.[0]; // 获取第一个 paragraph\
               if(paragraph?.marks?.length){
                const color = paragraph?.marks[0]?.attrs?.color; // 获取 color 值
                if (color) {
                  obj.style.color=color||''
                }
               }
      })
    }

      return obj;
    }
  } else {
    const props = { ...toRaw(data.attrs) };
    return props;
  }
};

//记录当前段落的id以及序号
let currentParagraphId = '';
let currentParagraphTextChunkIndex = 0;
function getChildren(data, type = "mark") {
  const { content } = data;
  if (!content?.length) return h("br", {}, "");
  const childrenList = [];
  // 判断当前是否在 blockView 下
  content.forEach((ele) => {
    let marksType = [];
    if (ele.marks) marksType = ele.marks.map((item) => item.type);
    if (ele.text && !marksType.includes("link")) {
      const childList4TextNode = [];
      const paragraphId = data.attrs.id
      if (!currentParagraphId || currentParagraphId !== paragraphId) {
        currentParagraphTextChunkIndex = 0;
        currentParagraphId = paragraphId;
      }

      // 检查是否有上标或下标标记
      const hasSubscript = marksType.includes("subscript");
      const hasSuperscript = marksType.includes("superscript");

      const italicText= marksType.includes("italic");


      // 拆字
      for (const i in ele.text) {
        let item = ele.text[i];
        if (item === ' ') {
          item = '\u2002';
        }

        let tagName = 'span';
        let props = { ...getProps(ele, type), id: getId(currentParagraphId , currentParagraphTextChunkIndex++), sign: "selection" ,type};
        if (hasSubscript) {
          tagName = 'sub';
          if (props.style && props.style.verticalAlign === 'sub') {
            delete props.style.verticalAlign;
            delete props.style
          }
          if (!props.style) {
            props.style = {};
          }
   
          props.style.letterSpacing = '0.1em';
          props.style.position = 'relative';
          props.style.top = '-0.1em';
          props.style.fontSize = '0.6em';
        } else if (hasSuperscript) {
          tagName = 'sup';
          if (props.style && props.style.verticalAlign === 'super') {
            delete props.style.verticalAlign;
          }
          if (!props.style) {
            props.style = {};
          }
          props.style.fontSize = '0.6em';
         
        }else if( italicText){
          tagName='em'
          if (!props.style) {
            props.style = {};
          }
          
        }

        childList4TextNode.push(
          h(
            tagName,
            props,
            item
          )
        );
      }
      const wrapperStyle = {
        style: getStyle(ele, type),
        class: 'text-wrapper'
      };
      childrenList.push(
        h(
          'span',
          wrapperStyle,
          childList4TextNode
        )
      );
    } else if (marksType.includes("link")) {
      // 带链接的文字组件
      const attrs = ele.marks.find((item) => item.type === "link")?.attrs;
      const marks = ele.marks.filter((item) => item.type !== "link");
      const linkItem = {
        type: "link",
        attrs,
        content: [{ text: ele.text, type: "text", marks }],
      };
      childrenList.push(
        h(
          getLableName(linkItem),
          getProps(linkItem, "link"),
          getChildren(linkItem, "mark")
        )
      );
    } else {
      let lableName = getLableName(ele);
      if (lableName == "table") {
        let rowData = ele?.content[0]?.content;
        if (rowData) {
          let colList = [];
          rowData.map((item) => {
            let { colwidth } = item.attrs;
            if (colwidth) {
              colwidth.map((col) => {
                if (col) {
                  colList.push(
                  h("col", {
                    style: { width: parseInt(col, 10) + "px" },
                  })
                );
                } else {
                  colList.push(h("col", { style: { "min-width": "25px" } }));
                }
                
              });
            } else {
              colList.push(h("col", { style: { "min-width": "25px" } }));
            }
          });
          childrenList.push(
            h(
              lableName,
              { ...getProps(ele, "", data.type) },
              [
                h(
                  "colgroup",
                  {},
                  colList
                ),
                ...getChildren(ele, "mark"),
              ]
            )
          );
        }
      } else if (ele.type == "bubbleInline") {
        let atts =  { ...getProps(ele, "", data.type), style: getStyle(ele, "mark") }
        childrenList.push(
          h(
            lableName,
           atts,
            getChildren(ele, "mark")
          )
        );
      } else {
        childrenList.push(
          h(
            lableName,
            { ...getProps(ele, "", data.type) },
            getChildren(ele, "mark")
          )
        );
      }
    }
  });
  return childrenList;
}

export const getParagraph = (paragraphData, pageId) => {
  const data = toRaw(paragraphData);
  let lableName = getLableName(data);
  if (lableName == "table") {
    let rowData = data?.content[0]?.content;
    if (rowData) {
      let colList = [];
      rowData.map((item) => {
        let { colwidth } = item.attrs;
        if (colwidth) {
          colwidth.map((col) => {
            if (col) {
              colList.push(
                h("col", {
                  style: { width: parseInt(col, 10) + "px" },
                })
              );
            } else {
              colList.push(h("col", { style: { "min-width": "25px" } }));
            }
          });
        } else {
          colList.push(h("col", { style: { "min-width": "25px" } }));
        }
      });
      return h(lableName, getProps(data), [
        h("colgroup", {}, colList),
        ...getChildren(data),
      ]);
    }
  }
  return h(
    lableName,
    { ...getProps(data) },
    getChildren(data)
  );
};
