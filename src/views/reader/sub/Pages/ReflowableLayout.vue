<!-- 流式布局的阅读形式，可能会包括垂直方向滑动的流式阅读、水平方向滑动流式阅读 -->
<style lang="scss" scoped>
.Pages {
  width: 24.8cm;
  margin: 0 auto;

  .Pages-item {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
  }
}
</style>
<style lang="scss">
.reflowable-layout {
  .Pages-item {
    min-height: 1200px;
    .Pages-item-main {
      min-height: calc(100% - 192px);
    }
  }
}
</style>
<template>
  <div class="Pages reflowable-layout" ref="PageRefs">
    <div
      ref="pageItemRefs"
      v-for="(item, index) in bookReadingStore.pageData"
      :key="item.attrs.id"
      :data-page-index-in-book="
        bookReadingStore.comprehensiveBookData
          .pageIndexInBook4FirstPageInChapter + index
      "
      :class="PAGE_ITEMS_CSS_CLASS"
    >
      <Pages_r_item
        :has-bookmark="hasBookMark && item.hasAccessToChapter"
        :content="item.content"
        :pageId="item.attrs.id"
        :index="index"
        :pageIndexInBook="
          bookReadingStore.comprehensiveBookData
            .pageIndexInBook4FirstPageInChapter + index
        "
        :chapterId="item.chapterId"
        :pageIndexInChapter="index + 1"
        :pageItem="item"
      />
    </div>
  </div>
</template>

<script setup>
import {
  defineProps,
  ref,
  onMounted,
  onUnmounted,
  onBeforeUnmount,
  nextTick,
  watch,
} from "vue";
import useReader from "@/store/modules/reader";
import Pages_r_item from "./Pages_r_item.vue";
import { PAGE_ITEMS_CSS_CLASS } from "@/utils/reader";

// const timer = ref(null)
const bookReadingStore = useReader();
// let totalPagesInPreviousChapters = ref(0)
const props = defineProps({
  hasBookMark: {
    type: Boolean,
    default: true,
  },
  pageData: {
    type: Array,
    required: true,
  },
});
// const data = ref([])
// watch(
//   () => bookReadingStore.chapterId,
//   () => {
//     totalPagesInPreviousChapters.value = bookReadingStore.calculateTotalPagesBeforeCurrentChapter()
//     // data.value = props.pageData
//     nextTick(() => {
//       // visualArea()
//     })
//   },
//   { deep: true }
// )
// const isDisplay = ref([]) //元素列是否进入可视区
// 可视区范围能1个或者多个page，有可能是需要跨章节
// const onSightPages = {}
// const observeList = []
const PageRefs = ref(null);
const pageItemRefs = ref([]);

/**
 * 监听页面是否进入可视区,进入后才渲染内部元素
 */
// const visualArea = () => {
//   nextTick(() => {
//     const rootElement = document.querySelector('.content-pages')
//     Array.from(pageItemRefs.value).forEach((element, index) => {
//       const OB = new IntersectionObserver(
//         (entries) => {
//           const entryPageObj = entries[0]
//           if (entryPageObj?.target) {
//             // console.log('entryPageObj：', entryPageObj.target, entryPageObj.isIntersecting)
//             const chapterId = entryPageObj.target.getAttribute(PAGE_CHAPTER_ID_ATTR)
//             const pageIndexInChapter = entryPageObj.target.getAttribute('data-chapter-page-index')
//             onSightPages[chapterId] = {
//               inSight: entryPageObj.isIntersecting,
//               chapterId: chapterId,
//               pageIndexInChapter: pageIndexInChapter
//             }
//             calculateCurrentPageIndex()
//             // isDisplay.value[index] = entryPageObj.isIntersecting
//           }
//         },
//         { root: rootElement, rootMargin: '0px', threshold: 0.2 }
//       )
//       OB.observe(element)
//       observeList.push(() => OB.unobserve(element))
//     })
//   })
// }

// onMounted(() => {
// data.value = props.pageData
// visualArea()
// PageRefs.value.parentNode.addEventListener('scroll', scrollCallback)
// })

// onBeforeUnmount(() => {
// PageRefs.value.parentNode.removeEventListener('scroll', scrollCallback)
// })

// onUnmounted(() => {
// observeList.forEach((unobserve) => {
//   unobserve && unobserve()
// })
// })
</script>
