<!-- 模块说明 -->
<style lang="scss" scoped>
$sidebar:
  url("@/assets/images/sidebar/resources.png"),
  url("@/assets/images/sidebar/practicalTraining.png"),
  url("@/assets/images/sidebar/task.png"),
  url("@/assets/images/sidebar/discuss.png"),
  url("@/assets/images/sidebar/examination.png"),
  url("@/assets/images/sidebar/note.png"),
  url("@/assets/images/sidebar/mindMap.png");

$sidebar_d:
  url("@/assets/images/sidebar/resources_d.png"),
  url("@/assets/images/sidebar/practicalTraining_d.png"),
  url("@/assets/images/sidebar/task_d.png"),
  url("@/assets/images/sidebar/discuss_d.png"),
  url("@/assets/images/sidebar/examination_d.png"),
  url("@/assets/images/sidebar/note_d.png"),
  url("@/assets/images/sidebar/mindMap_d.png");

$sidebar_:
  url("@/assets/images/sidebar/resources_.png"),
  url("@/assets/images/sidebar/practicalTraining_.png"),
  url("@/assets/images/sidebar/task_.png"),
  url("@/assets/images/sidebar/discuss_.png"),
  url("@/assets/images/sidebar/examination_.png"),
  url("@/assets/images/sidebar/note_.png"),
  url("@/assets/images/sidebar/mindMap_.png");

.Tools {
  position: fixed;
  right: 0;
  width: 80px;
  height: 100%;
  padding: 20px 7px;
  box-sizing: border-box;
  transition: 0.3s;
  transform: translateX(0);
  background-color: var(--modelBackgroundColor);
  box-shadow: var(--boxShadow);
 // z-index: 2;
  @for $i from 1 through 7 {
    .Tools-item_#{$i} {
      display: flex;
      align-items: center;
      flex-direction: column;
      cursor: pointer;
      transition: 0.3s;
      font-weight: 400;
      font-size: 14px;
      color: var(--fontColor);
      padding: 8px 0;
      margin: 14px 0;
      .Tools-item-icon_d,
      .Tools-item-icon {
        width: 20px;
        height: 22px;
        transition: 0.3s;
        background-image: nth($sidebar, $i);
        background-size: 20px;
        background-repeat: no-repeat;
      }
      .Tools-item-icon_d {
        background-image: nth($sidebar_d, $i);
      }
      &:hover {
        color: #0966b4;
        background-color: var(--hoverBackgroundColor);
        border-radius: 5px;
        font-size: 14px;
      }
      &:hover .Tools-item-icon {
        background-image: nth($sidebar_, $i);
      }
    }
    .check-tool-item-#{$i} {
      color: #fff;
      background-color: #0966b4;
      border-radius: 5px;
      font-size: 14px;
      .Tools-item-icon {
        background-image: nth($sidebar_d, $i);
      }
    }
  }
}
</style>

<template>
  <div
    class="Tools"
    :style="{
      transform: store.toolIsHide ? 'translateX(100px)' : 'translateX(0)',
    }"
    ref="toolListRef"
  >
    <section
      v-for="(ele, i) in optionTab"
      :class="`Tools-item_${i + 1} ${toolBoxIsShow && currentComponentType === ele.type ? 'check-tool-item-' + (i + 1) : ''}`"
      :key="ele.icon"
      @click="openBarTool(ele)"
    >
      <div
        :class="`Tools-item-icon ${store.theme === 'dark' ? 'Tools-item-icon_d' : ''}`"
      ></div>
      <span class="__ellipsis__">{{ ele.title }}</span>
    </section>
  </div>
  <SideToolsBox :isShow="toolBoxIsShow">
    <component :is="currentComponents" @edit="editFn" v-if="toolBoxIsShow"></component>
  </SideToolsBox>
</template>

<script setup>
import { ref, defineProps } from "vue";
import useReader from "@/store/modules/reader";
import SideToolsBox from "./Tool/SideToolsBox.vue";
import PracticalTraining from "./Tool/PracticalTraining.vue";
import Examination from "./Tool/Examination.vue";
// import Discuss from "./Tool/Discuss.vue";
import Note from "./Tool/Note.vue";
import Task from "./Tool/Task.vue";
import Resources from "./Tool/Resources.vue";
const emits = defineEmits(["edit"]);
const store = useReader();
const toolBoxIsShow = ref(false);
let currentComponents = {};
let currentComponentType = ref('')
const toolListRef = ref();
const openBarTool = (v) => {
  currentComponents = v.components;
  if (v.type !== currentComponentType.value) {
    toolBoxIsShow.value = false;
    nextTick(() => {
      toolBoxIsShow.value = true;
    })
  } else {
    toolBoxIsShow.value = !toolBoxIsShow.value;
  }
  currentComponentType.value = v.type
};
const optionTab = [
  {
    title: "资源",
    icon: "resources",
    components: Resources,
    type: "resouce",
  },
  {
    title: "实训",
    icon: "practicalTraining",
    components: PracticalTraining,
    type: "practicalTraining",
  },
  {
    title: "作业",
    icon: "task",
    components: Task,
    type: "afterclasspractice",
  },
  // {
  //   title: '讨论',
  //   icon: 'discuss',
  //   components: Discuss,
  //   type: 'discuss'
  // },
  {
    title: "考试",
    icon: "examination",
    components: Examination,
    type: "examination",
  },
  {
    title: "笔记",
    icon: "note",
    components: Note,
    type: "note",
  },
  // {
  //   title: "思维导图",
  //   icon: "mindMap",
  //   components: Discuss,
  //   type: 'mindMap'
  // },
];

defineExpose({
  toolListRef,
});
function editFn(arg) {
  emits("edit", currentComponentType.value, arg);
}

watch(
  () => store.comprehensiveBookData.bookId,
  (nValue, oValue) => {
    toolBoxIsShow.value = false;
    currentComponents = {}
  }
)
</script>
