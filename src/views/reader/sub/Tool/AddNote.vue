<!-- 添加或修改笔记内容 -->

<style lang="scss" scoped>
.add-note {
  .soundRecording {
    display: flex;
    align-items: center;
    background-color: #f2f2f2;
    border-radius: 6px;
    padding: 0 0.5rem 0 0.5rem;
    .sound-track {
      flex: 1;
      background: transparent url('@/assets/images/sound_track_static.png') no-repeat left center / contain;
      height: 3rem;
      &.recording {
        background-image: url('@/assets/images/sound_track.gif');
      }
    }
    .svg-icon {
      width: 1.5rem;
      height: 1.5rem;
      margin: 0 0.2rem 0 0;
    }
    .complete-recording {
      .el-icon {
        background: transparent url('@/assets/images/check_1.png') no-repeat center center / contain;
      }
    }
  }
  .note-input {
    margin-top: 6px;
  }
  .note-upload-list {
    min-height: 100px;
    display: flex;
    flex-wrap: wrap;
    .upload-item {
      border: 1px dashed #999;
      border-radius: 3px;
      margin: 3px;
      cursor: pointer;
      width: 100px;
      height: 100px;
      &:hover {
        border-color: var(--hoverfont);
      }
    }
    .note-upload-item {
      padding: 2px;
      background-color: #eaeaea;
    }
  }
}
</style>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="笔记" width="670px"
    class="add-note"
    :destroy-on-close="true"
    :append-to-body="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    @opened="handleOpened">
    <div
      class="soundRecording">
      <div
        class="timer-display">
        {{ formattedTime }}
      </div>
      <span
        class="sound-track"
        :class="{ 'recording': isTimerRunning }"></span>
      <el-button link
        @click="checkMic"
        class="start-recording"
        v-if="!isTimerRunning">
        <svg-icon
          iconClass="luyin"
          style="font-size: 14px" />开始录音
      </el-button>
      <el-button link
        @click="completeRecording"
        class="complete-recording"
        v-if="isTimerRunning">
        <el-icon
          class="svg-icon"></el-icon>结束录音
      </el-button>
    </div>
    <el-input rows="6"
      class="note-input"
      v-model="localEditNoteData.nodeContent"
      maxlength="500"
      show-word-limit
      type="textarea"
      placeholder="请写下你的笔记" />
    <note-upload-list
      :attachmentList="localEditNoteData.attachments"
      :support-remove="true"
      @remove="toRemoveAttachmentItem"
      mode="float">
      <template #addUpload>
        <div
          class="upload-item __flex-center__"
          @click="selectImage"
          v-loading="loading">
          <el-icon>
            <Plus />
          </el-icon>
        </div>
      </template>
    </note-upload-list>

    <template #footer>
      <!-- <div style="text-align: left"><el-checkbox v-model="checked1" label="公开(仅班级内)" size="large" /></div> -->
      <div
        class="dialog-footer">
        <el-button
          @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="addNote">{{
            Object.keys(props?.editNoteData).length ? "修改" : "添加"
          }}笔记</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, watch, onMounted, onBeforeUnmount } from 'vue'
import Sound from './SoundRecording.js'
import { OssService, _uploads, isImage, isAudio } from '@/utils/aliOss.js'
import { saveBookNote, updateBookNote, getBookNote, deleteBookNote } from '@/api/book/reader'
import { ElMessage } from 'element-plus'
import useReader from '@/store/modules/reader'
import { getToken } from '@/utils/auth'
import dayjs from 'dayjs'
import {} from '@/utils/aliOss.js'
import NoteUploadList from './noteUploadList.vue'
import { unregisterKeyboardEvent, registerKeyboardEvent } from '@/utils/reader'
import { deriveFromAndEndWordIdList, deriveIntersectionNotesData } from '@/utils/reader'

const { proxy } = getCurrentInstance()

const emits = defineEmits(['cancel', 'confirm'])
const props = defineProps({
  bookId: String,
  chapterId: String,
  pageIndexInChapter: Number,
  isShow: {
    type: Boolean,
    default: false
  },
  elementIdList: Array,
  elementText: String,
  editNoteData: Object
})

const store = useReader()
// 计时器相关状态
const startTime = ref(null)
const elapsedTime = ref(0)
const animationFrameId = ref(null)
const isTimerRunning = ref(false)
// 格式化时间为 mm:ss
const formattedTime = computed(() => {
  const totalSeconds = elapsedTime.value / 1000
  const minutes = Math.floor(totalSeconds / 60)
    .toString()
    .padStart(2, '0')
  const seconds = Math.floor(totalSeconds % 60)
    .toString()
    .padStart(2, '0')
  const hundredths = Math.round((totalSeconds % 1) * 100)
    .toString()
    .padStart(2, '0')

  return `${minutes}:${seconds}.${hundredths}`
})

const checked1 = ref(false)
const dialogVisible = ref(false)

let selectedTextIdList = null
let selectedText = null;

let localEditNoteData = ref({ attachments: [] })
let isUpdateNote = false
const loading = ref(false)

async function selectImage() {
  if (loading.value) {
    return Promise.resolve({})
  }
  if (localEditNoteData.value.attachments.length >= 10) {
    return ElMessage.error('您已经上传了10个附件，请移除部分附件后继续上传')
  }
  const [res] = await _uploads({
    accept: '.png,.jpg,.jpeg',
    maxSize: 10,
    changeFn: () => {
      loading.value = true
    }
  })
    .catch(() => {
      ElMessage.error('上传失败')
    })
    .finally(() => {
      loading.value = false
    })
  let type = ''
  if (isImage(res.url)) {
    type = 1
  } else if (isAudio(res.url)) {
    type = 2
  }
  localEditNoteData.value.attachments.push({
    // attachmentId: '',
    attachmentName: res.originName,
    attachmentUrl: res.url,
    attachmentType: type
  })
}
function toRemoveAttachmentItem(noteDataItem, item) {
  localEditNoteData.value.attachments = localEditNoteData.value.attachments.filter(
    noteItem => noteItem.attachmentUrl !== item.attachmentUrl
  )
}
// 更新计时器
function updateTimer(timestamp) {
  if (!startTime.value) startTime.value = timestamp
  elapsedTime.value = timestamp - startTime.value

  if (isTimerRunning.value) {
    animationFrameId.value = requestAnimationFrame(updateTimer)
  }
}

// 开始计时
function startTimer() {
  isTimerRunning.value = true
  startTime.value = null
  elapsedTime.value = 0
  animationFrameId.value = requestAnimationFrame(updateTimer)
}

// 停止计时
function stopTimer() {
  isTimerRunning.value = false
  elapsedTime.value = 0
  cancelAnimationFrame(animationFrameId.value)
}

// 重置计时器
function resetTimer() {
  stopTimer()
  elapsedTime.value = 0
}

function handleClose() {
  checked1.false = ''
  localEditNoteData.value = {}
  unregisterKeyboardEvent('addNote')
  resetTimer()
  emits('cancel')
}
// 开始录音前，先检查麦克风状态
function checkMic() {
  if (localEditNoteData.value.attachments.length >= 10) {
    return ElMessage.error('您已经上传了10个附件，请移除部分附件后继续上传')
  }
  // 检测并请求访问麦克风
  navigator.mediaDevices
    .getUserMedia({ audio: true })
    .then(function (stream) {
      // console.log('有麦克风')
      startRecording()
    })
    .catch(function (error) {
      // console.log('没有麦克风或访问被拒绝')
      return ElMessage.error('请打开媒体设备，并允许浏览器进行访问')
    })
}
/**
 * 开始录音
 */
function startRecording() {
  if (loading.value) {
    return ElMessage.error('正在上传笔记附件，请稍后尝试')
  }
  startTimer()
  Sound.startRecording()
    .then(async () => {})
    .catch(error => {
      // ElMessage.error('请打开媒体设备，并允许浏览器进行访问')
    })
}

/**
 * 结束录音
 */
async function completeRecording() {
  stopTimer()
  loading.value = true
  const blob = await Sound.stopRecording()
  // console.log(blob)
  // const audioUrl = URL.createObjectURL(blob)
  const name = 'ZHJY' + dayjs().format('YYYYMMDDHHmmss') + '.mp3'
  const files = new File([blob], name)
  const res = await OssService(files)
  localEditNoteData.value.attachments.push({
    attachmentUrl: res.url,
    attachmentName: name,
    attachmentSize: (files.size / 1024).toFixed(2),
    attachmentType: 2
  })
  loading.value = false
}

function resetState() {
  isUpdateNote = false
  localEditNoteData.value = {}
}
function handleOpened() {
  registerKeyboardEvent(
    'addNote',
    e => {
      // 由于阅读器注册了左键和右键进行翻页，当弹窗出现的时候，弹窗注册左键和右键，并停止事件冒泡，用于区分阅读器，查看上一页和下一页。
      // 其他弹窗同理
      if (e.keyCode === 39 || e.keyCode === 37) {
        e.cancelBubble = true
      }
    },
    {
      before: 'reader'
    }
  )
}

async function addNote() {
  if (!getToken()) return
  // return
  if (!localEditNoteData.value.nodeContent || (localEditNoteData.value.nodeContent && localEditNoteData.value.nodeContent.length < 5)) {
    return ElMessage.error('请输入至少5个字的笔记内容')
  }
  if (!Object.keys(props?.editNoteData)?.length) {
    // 获取到每个段落选中的拆分文字的开始id和结束id
    const { fromWordIdList, endWordIdList } = deriveFromAndEndWordIdList(selectedTextIdList)
    //保存笔记
    const data = {
      bookId: props.bookId,
      chapterId: props.chapterId,
      pageNumber: props.pageIndexInChapter,
      nodeContent: localEditNoteData.value.nodeContent,
      attachments: localEditNoteData.value.attachments,
      shareFlag: checked1.value ? 1 : 2,
      fromWordId: fromWordIdList,
      endWordId: endWordIdList,
      bookContent: selectedText
    }
    const res = await saveBookNote(data).then(resp => {
      if (resp.code !== 200) {
        return Promise.reject(resp.msg)
      }
      return resp
    })
    if (res.code === 200) dialogVisible.value = false
  } else {
    //修改笔记
    const { bookContent, chapterId, fromWordId, endWordId, nodeContent, pageNumber, chapterName, noteId } = localEditNoteData.value
    const data = {
      noteId: noteId,
      bookId: props.bookId,
      chapterId: chapterId,
      pageNumber: pageNumber,
      attachments: localEditNoteData.value.attachments.map(attachment => {
        return {
          ...attachment,
          createTime: undefined,
          attachmentId: undefined
        }
      }),
      shareFlag: checked1.value ? 1 : 2,
      fromWordId,
      endWordId,
      nodeContent,
      bookContent
    }
    // console.log(localEditNoteData.value)
    const res = await updateBookNote(data).then(resp => {
      if (resp.code !== 200) {
        return Promise.reject(resp.msg)
      }
      return resp
    })
    if (res.code === 200) {
      dialogVisible.value = false
    }
  }
  ElMessage.success(isUpdateNote ? '修改成功' : '添加成功')
  resetState()
  emits('confirm')
}

onMounted(() => {
  selectedTextIdList = JSON.parse(JSON.stringify(props.elementIdList))
  selectedText = props.elementText
  if (props.editNoteData?.noteId) {
    isUpdateNote = true
    localEditNoteData.value = JSON.parse(JSON.stringify(toRaw(props.editNoteData)))
  }

  if (isUpdateNote) {
    // 如果是编辑笔记
    dialogVisible.value = true
    return;
  }

  // 如果是添加笔记, 判断是否有笔记重合
  const result = deriveIntersectionNotesData(toRaw(props.elementIdList), store.noteList);
  if (result?.length > 0) {
    promptToRemoveExistedNotes(result);
  } else {
    dialogVisible.value = true
  }
})

watch(
  () => props.editNoteData,
  val => {
    if (val?.noteId) {
      isUpdateNote = true
      localEditNoteData.value = JSON.parse(JSON.stringify(toRaw(val)))
    }
  },
  { deep: true }
)

const getNoteList = async () => {
  const res2 = await getBookNote({
    bookId: store.comprehensiveBookData.bookId,
    sort: 1,
    noteType: 1
  })
  if (res2.code === 200) {
    store.setNoteList(res2.data)
  }
}

function promptToRemoveExistedNotes(toBeRemovedNoteIds) {
  proxy.$modal
    .confirm('该内容已有笔记，如添加新笔记，原笔记将被删除。')
    .then(function () {
      const toBeDeleteNotePromise = toBeRemovedNoteIds.map(noteId => {
        return deleteBookNote(noteId)
      });
      Promise.all(toBeDeleteNotePromise)
      .then(() => {
        getNoteList();
        dialogVisible.value = true
      });
    })
    .catch(() => {
      dialogVisible.value = false
      emits('cancel')
    })
}

watch(
  () => props.isShow,
  val => {
    // if (Object.keys(props?.editNoteData)?.length > 0) {
    //   // 如果是编辑笔记
    //   dialogVisible.value = val
    //   return;
    // }
    // // 如果是添加笔记, 判断是否有笔记重合
    // const result = deriveIntersectionNotesData(toRaw(props.elementIdList), store.noteList);
    // if (result?.length > 0) {
    //   promptToRemoveExistedNotes(result);
    // } else {
    //   dialogVisible.value = val
    // }
  },
  { deep: true, immediate: true }
)

// 组件卸载前清理
onBeforeUnmount(() => {
  stopTimer()
})
</script>
