<style lang="scss" scoped>
// .audio-play-panel {
//   width: 100%;
//   height: 21px;
//   background: var(--noteContentBackground);
//   border-radius: 12px;
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   padding: 0 18px;
//   box-sizing: border-box;
//   .audio-play-btn {
//     & > img {
//       width: 15px;
//       height: 15px;
//       cursor: pointer;
//       margin-top: 5px;
//     }
//   }
//   .play-time {
//     font-size: 12px;
//     color: #999999;
//     margin: 0 20px;
//   }
//   .play-speed {
//     flex: 1;
//     background: #d2e9f9;
//     height: 4px;
//     position: relative;
//     cursor: pointer;
//     .speed {
//       height: 100%;
//       background-color: #0966b4;
//       position: relative;
//       display: flex;
//       align-items: center;
//       transition: 0.5s;
//       .spot {
//         width: 8px;
//         height: 8px;
//         background-color: #0966b4;
//         border-radius: 50%;
//         position: absolute;
//         right: 0;
//         line-height: 8px;
//         cursor: pointer;
//         transition: 0.3s;
//         &:hover {
//           transform: scale(1.3);
//         }
//       }
//     }
//   }
// }
</style>
<template>
  <!-- <div class="audio-play-panel">
    <div class="audio-play-btn">
      <img src="@/assets/images/sidebar/play-voice.png" alt="" v-if="audioData.paused" @click="playAudio" />
      <img src="@/assets/images/sidebar/stop-voice.png" alt="" v-else @click="stopAudio" />
    </div>
    <div class="play-time">{{ audioData.currentTime }}</div>
    <div class="play-speed" ref="speedBoxRef" @click.stop="mousedown">
      <div class="speed" :style="`width:${speed}%`">
        <div class="spot"></div>
      </div>
    </div>
    <div class="play-time">{{ audioData.duration }}</div>
    <div class="play-close">
      <el-button link :icon="CloseBold" @click="closeAudio"></el-button>
    </div>
  </div> -->
</template>
<script setup>
// import { CloseBold } from '@element-plus/icons-vue'
// import { ref, defineProps, reactive, watch, onMounted, onBeforeUnmount } from 'vue'
// const speed = ref(30)
// const speedBoxRef = ref(null)
// const Audio = ref(null)
// const audioData = reactive({
//   duration: 0, //音频总时间
//   paused: true, //是否暂停
//   currentTime: 0, //当前播放时间
// })
// let timer = null
// const props = defineProps({
//   src: {
//     type: String,
//     default: 'http://dutp-test.oss-cn-beijing.aliyuncs.com/%E9%82%A6%E9%82%A6%E7%9A%84%E6%90%9E%E7%AC%91%E6%9D%A5%E7%94%B5%E9%93%83%E5%A3%B0.mp3',
//   },
//   setSoundRecordingItem: Function,
// })
// const mousedown = (e) => {
//   Audio.value.currentTime = (e.layerX / speedBoxRef.value.offsetWidth) * Audio.value.duration
//   speed.value = (e.layerX / speedBoxRef.value.offsetWidth) * 100
// }
// function secondsToMinutesAndSeconds(seconds) {
//   const minutes = Math.floor(seconds / 60)
//   const remainingSeconds = (seconds % 60).toFixed(0)
//   return `${minutes}:${remainingSeconds}`
// }
// const getAudioData = (src) => {
//   Audio.value = document.createElement('audio')
//   Audio.value.src = src
//   Audio.value.autoplay = true
//   Audio.value.onended = () => {
//     //播放结束
//     if (timer) {
//       clearInterval(timer)
//       timer = null
//     }
//   }
//   playAudio()
// }

// const playAudio = () => {
//   if (timer) {
//     clearInterval(timer)
//     timer = null
//   }

//   Audio.value.play()
//   timer = setInterval(() => {
//     audioData.duration = Audio.value.duration.toString() == 'Infinity' ? '未知' : secondsToMinutesAndSeconds(Audio.value.duration)
//     audioData.paused = Audio.value.paused
//     audioData.currentTime = secondsToMinutesAndSeconds(Audio.value.currentTime)
//     speed.value = (Audio.value.currentTime / Audio.value.duration) * 100
//     if (Audio.value.paused) {
//       clearInterval(timer)
//       timer = null
//     }
//   }, 100)
// }
// const stopAudio = () => {
//   Audio.value.pause()
// }
// const closeAudio = () => {
//   Audio.value.pause()
//   clearInterval(timer)
//   timer = null
//   props.setSoundRecordingItem && props.setSoundRecordingItem()
// }
// watch(
//   () => props.src,
//   () => {
//     Audio.value.pause()
//     clearInterval(timer)
//     getAudioData(props.src)
//   }
// )
// onMounted(() => {
//   getAudioData(props.src)
// })
// onBeforeUnmount(() => {
//   closeAudio()
// })
</script>
