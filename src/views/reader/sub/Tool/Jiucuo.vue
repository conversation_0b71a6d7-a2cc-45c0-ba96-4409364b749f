<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2025-01-20 12:46:37
 * @LastEditTime: 2025-01-22 14:36:51
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Tool\Jiucuo.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 提交纠错的弹窗 -->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="提交纠错" width="700"
    :destroy-on-close="true">
    <el-form
      :model="formValue"
      label-width="auto"
      :rules="rules"
      ref="formRef"
      class="add-note">
      <el-form-item
        label="错误类型"
        prop="type">
        <el-checkbox-group
          v-model="formValue.type">
          <el-checkbox
            value="1"
            name="type">错别字</el-checkbox>
          <el-checkbox
            value="2"
            name="type">逻辑错误</el-checkbox>
          <el-checkbox
            value="3"
            name="type">内容错误</el-checkbox>
          <el-checkbox
            value="4"
            name="type">图片错误</el-checkbox>
          <el-checkbox
            value="5"
            name="type">其他</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item
        label="上传图片">
        <note-upload-list
          :attachmentList="formValue.images"
          :support-remove="true"
          @remove="toRemoveAttachmentItem"
          mode="float">
          <template
            #addUpload>
            <div
              class="upload-item __flex-center__"
              @click="selectImage"
              v-loading="loading">
              <el-icon>
                <Plus />
              </el-icon>
            </div>
          </template>
        </note-upload-list>
      </el-form-item>
      <el-form-item
        label="问题描述"
        prop="describe">
        <el-input
          type="textarea"
          v-model="formValue.describe"
          :rows="5"
          placeholder="请输入问题描述" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button
        @click="cancel">取消</el-button>
      <el-button
        type="primary"
        @click="onOk">确认提交</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, reactive, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import { saveBookFault } from '@/api/book/reader'
import useReader from '@/store/modules/reader'
import NoteUploadList from './noteUploadList.vue'
import { _uploads, isImage } from '@/utils/aliOss.js'

const loading = ref(false)
const store = useReader()
const props = defineProps({ uuid: String, elementList: Array })
const dialogVisible = ref(false)
const formValue = reactive({
  type: [],
  describe: '',
  images: []
})
const formRef = ref(null)
async function selectImage() {
  if (loading.value) {
    return Promise.resolve({})
  }
  const [res] = await _uploads({
    accept: '.png,.jpg,.jpeg',
    maxSize: 10,
    changeFn: () => {
      loading.value = true
    }
  })
    .catch(() => {
      ElMessage.error('上传失败')
    })
    .finally(() => {
      loading.value = false
    })
  let type = ''
  if (isImage(res.url)) {
    type = 1
  }
  if (formValue.images.length >= 6) {
    return ElMessage.error('您已经上传了6个图片文件，请移除部分图片后继续上传')
  }
  formValue.images.push({
    // attachmentId: '',
    attachmentName: res.originName,
    attachmentUrl: res.url,
    attachmentType: type
  })
}

watch(
  () => props.uuid,
  () => {
    dialogVisible.value = true
    formValue.type = []
    formValue.describe = ''
    formValue.images = []
  },
  { deep: true }
)
const rules = {
  type: [{ required: true, message: '请选择错误类型!', trigger: 'submit' }],
  describe: [{ required: true, message: '请输入问题描述!', trigger: 'blur' }]
}
const cancel = () => {
  dialogVisible.value = false
}

const getSelectedText = () => {
  let text = ''
  props.elementList
    .sort((a, b) => a.id.split('_')[1] - b.id.split('_')[1])
    .forEach(element => {
      text += element.innerText
    })
  return text
}

const onOk = async () => {
  const validate = await formRef.value.validate()
  if (validate) {
    const data = {
      bookId: store.comprehensiveBookData.bookId,
      chapterId: store.chapterId,
      // （bug：VYOA-1867） +1页码显示错误，先注释
      // pageNumber: store.comprehensiveBookData.currentPageIndex + 1,
      pageNumber: store.comprehensiveBookData.currentPageIndex,
      faultText: getSelectedText(),
      comment: formValue.describe,
      images: formValue.images.map(image => {
        return {
          fileUrl: image.attachmentUrl,
          fileName: image.attchmentName
        }
      }),
      faultType: formValue.type.toString()
    }
    const res = await saveBookFault(data)
    if (res.code === 200) {
      dialogVisible.value = false
      ElMessage.success('提交纠错信息成功!')
    }
  }
}
const changeUpload = v => {
  formValue.images = v.map(ele => {
    return {
      fileName: ele.originName,
      fileUrl: ele.url
    }
  })
}
function toRemoveAttachmentItem(noteDataItem, item) {
  localEditNoteData.value.attachments = localEditNoteData.value.attachments.filter(
    noteItem => noteItem.attachmentUrl !== item.attachmentUrl
  )
}
</script>
<style lang="scss">
.add-note {
  .note-upload-list {
    min-height: 100px;
    display: flex;
    .upload-item {
      border: 1px dashed #999;
      border-radius: 3px;
      margin: 3px;
      cursor: pointer;
      &:hover {
        border-color: var(--hoverfont);
      }
    }
    .upload-item,
    .note-upload-item {
      width: 100px;
      height: 100px;
      border-radius: 3px;
      margin: 3px;
      cursor: pointer;
      &:hover {
        border-color: var(--hoverfont);
      }
    }
    .note-upload-item {
      padding: 2px;
      background-color: #eaeaea;
      &.audio-item .note-item-imageBg {
        background-color: #f5f9fc;
      }
    }
  }
}
</style>
