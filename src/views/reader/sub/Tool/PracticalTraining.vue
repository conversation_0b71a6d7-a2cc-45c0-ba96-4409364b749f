<!-- 实训 -->
<style lang="scss" scoped>
.PracticalTraining {
  width: 365px;

  .header {
    margin-bottom: 20px;
  }

  .title {
    font-weight: 400;
    font-size: 14px;
    color: #999;
    line-height: 14px;
    text-align: justify;
    margin-bottom: 14px;
  }

  .content {
    position: relative;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    .content-card {
      width: 100%;
      height: 78px;
      background: var(--pageBackgroundColor);
      box-shadow: var(--noteBoxShadow);
      border-radius: 3px;
      padding: 4px;
      box-sizing: border-box;
      margin-bottom: 10px;
      transition: 0.3s;
      position: relative;
      .jumpTo {
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        line-height: 24px;
        width: 58px;
        height: 24px;
        background: rgba(0, 0, 0, 0.5);
        position: absolute;
        right: 0;
        top: 0;
        text-align: center;
        border-radius: 0px 3px 0px 5px;
        cursor: pointer;
        display: none;

        &:hover {
          color: #0966b4;
        }
      }

      & > nav {
        display: flex;
        align-items: center;
        height: 48px;

        & > img {
          width: 29px;
          height: 29px;
        }

        .name {
          flex: 1;
          font-size: 14px;
          color: #999;
        }
      }

      & > footer {
        position: relative;
        .footer-btn {
          position: absolute;
          right: 0;
          width: 42px;
          height: 17px;
          border-radius: 9px;
          border: 1px solid #0966b4;
          font-weight: 400;
          font-size: 12px;
          color: #0966b4;
          cursor: pointer;
          transition: 0.3s;

          &:hover {
            background-color: #0966b4;
            color: #fff;
          }
        }
      }
    }

    .content-card:hover .jumpTo {
      display: block;
    }
  }
}
</style>

<template>
  <div class="PracticalTraining">
    <header class="header">
      <el-select
        v-model="chapter"
        style="width: 143px"
        :popper-class="store.theme === 'dark' ? '__darkSelect__' : ''"
        @change="change"
        :clearable="true"
      >
        <el-option
          v-for="item in chaptersOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </header>
    <template v-for="item in data" :key="item.chapterId">
      <section class="title __ellipsis__">
        <el-tooltip :content="item.chapterName">
          {{ item.chapterName }}
        </el-tooltip>
      </section>
      <main class="content">
        <section
          class="content-card"
          v-for="(ele, i) in item.bookResourcesList"
          :key="i"
        >
          <nav>
            <img
              src="@/assets/images/sidebar/practicalTrainingTitle.png"
              alt=""
            />
            <div class="name __ellipsis__">
              {{ ele.fileName }}
            </div>
          </nav>
          <footer>
            <div
              class="footer-btn __flex-center__"
              @click="openPracticalTraining(ele)"
            >
              打开
            </div>
          </footer>
          <div class="jumpTo" @click.stop="jumpTo(ele)">跳转</div>
        </section>
      </main>
    </template>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import { getBookResource, getChapters } from "@/api/book/reader";
import useReader from "@/store/modules/reader";
import { ElMessage } from "element-plus";

const store = useReader();
const data = ref([]);
const chaptersOptions = ref([]);
const chapter = ref("全部章节");

const openPracticalTraining = (v) => {
  if (!v.fileUrl) {
    return ElMessage.error("实训地址不可以为空");
  }
  window.open(v.fileUrl, "_blank");
};

/**
 * 跳转
 */
const jumpTo = async (v) => {
  console.log(v);
  await store.jumpToChapter(v.chapterId, v.pageNumber);
};
/**
 * 获取实训列表
 */
const getResourcesList = async () => {
  const params = {
    bookId: store.comprehensiveBookData.bookId,
    resourceType: 1,
    chapterId: chapter.value === "全部章节" ? undefined : chapter.value,
  };
  const res = await getBookResource(params);
  if (res.code === 200) data.value = res.data;
};

const change = () => {
  if (!chapter.value) chapter.value = "全部章节";
  getResourcesList();
};

//获取章节列表
const getChaptersList = async () => {
  const res = await getChapters(store.comprehensiveBookData.bookId);
  const option = [
    {
      label: "全部章节",
      value: "全部章节",
    },
  ];
  if (res.code === 200) {
    res.data.forEach((ele) => {
      option.push({ label: ele.chapterName, value: ele.chapterId });
    });
  }
  chaptersOptions.value = option;
};
onMounted(() => {
  getResourcesList();
  getChaptersList();
});
</script>
