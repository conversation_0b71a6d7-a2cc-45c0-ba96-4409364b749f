<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2025-01-20 09:50:01
 * @LastEditTime: 2025-01-20 11:25:32
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Tool\PreviewDialog.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<template>
  <el-dialog
    v-model="data.isShow"
    :title="data.title"
    width="75%"
    top="20px"
    :destroy-on-close="true"
    @close="closeHandler4Preview"
    @opened="openHandler4Preview"
  >
    <main style="width: 100%; height: 80vh">
      <div v-if="isVideo(previewStore.url)" class="resources-bg">
        <video
          :src="previewStore.url"
          controls
          width="100%"
          height="100%"
          controlslist="nodownload"
        ></video>
      </div>
      <div v-if="isAudio(previewStore.url)" class="resources-bg">
        <audio controls width="100%" height="100%" controlslist="nodownload">
          <source :src="previewStore.url" type="audio/wav" />
          <source :src="previewStore.url" type="audio/mpeg" />
          <source :src="previewStore.url" type="audio/ogg" />
        </audio>
      </div>
      <div v-else class="resources-bg">
        <iframe
          :src="data.url"
          frameborder="0"
          width="100%"
          height="100%"
        ></iframe>
      </div>

      <!--  -->
    </main>
  </el-dialog>
</template>

<script setup>
import { reactive, watch } from "vue";
import { getPreviewFileUrl } from "@/utils/index.js";
import usePreview from "@/store/modules/preview.js";
import { unregisterKeyboardEvent, registerKeyboardEvent } from "@/utils/reader";

const previewStore = usePreview();
const data = reactive({
  isShow: false,
  title: "",
  url: "",
});
watch(
  () => previewStore.uuid,
  () => {
    data.isShow = true;
    data.title = previewStore.title;
    if (!previewStore.isConvertUrl) return (data.url = previewStore.url);
    // if (previewStore.url.endsWith('.mp3') || previewStore.url.endsWith('.wav')) return (data.url = previewStore.url)
    if (previewStore.url.split(".").pop()?.length > 4) {
      data.url = previewStore.url;
    } else {
      data.url = getPreviewFileUrl(previewStore.url);
    }
  },
  { deep: true }
);
function closeHandler4Preview() {
  data.isShow = false;
  unregisterKeyboardEvent("previewResourceInReader");
}

//判断是否是视频格式
function isVideo(url) {
  const videoTypes = [".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv"];
  return videoTypes.some((type) => url.includes(type));
}
//判断是否是音频格式
function isAudio(url) {
  const audioTypes = [".mp3", ".wav", ".ogg"];
  return audioTypes.some((type) => url.includes(type));
}

function openHandler4Preview() {
  registerKeyboardEvent(
    "previewResourceInReader",
    (e) => {
      // 由于阅读器注册了左键和右键进行翻页，当弹窗出现的时候，弹窗注册左键和右键，并停止世界冒泡，用于区分变速器，查看上一页和下一页。
      // 其他弹窗同理
      if (e.keyCode === 39 || e.keyCode === 37) {
        e.cancelBubble = true;
      }
    },
    {
      before: "reader",
    }
  );
}
</script>
<style lang="less" scoped>
.resources-bg {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 80vh;
}
</style>
