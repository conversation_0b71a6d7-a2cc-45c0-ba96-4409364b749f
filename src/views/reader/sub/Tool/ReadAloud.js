/*
 * @Author: 矫建武 <EMAIL>
 * @Date: 2025-02-26 15:58:23
 * @LastEditTime: 2025-02-27 13:16:31
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Tool\ReadAloud.js
 * @Description: 朗读
 *
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
 */
import useReadAloud from '@/store/modules/readAloud'
import { ElMessage } from "element-plus"
class ReadAloud {
  constructor() {
    this.status = true
    this.utterance = null
    this.elementListIdName = []
    this.style = null//样式的style标签
  }

  start(text, element) {
    console.log('text', text)
    console.log('element', element)
    if (!this.status) return ElMessage.error('当前朗读未完成')
    if (element) this.elementListIdName = element.map(ele => ele.id)
    this.utterance = new SpeechSynthesisUtterance(text);
    // 设置语速（范围：0.1到10，1为正常速度）
    this.utterance.rate = useReadAloud().rate;
    // 设置音高（范围：0到2，1为正常音高）
    this.utterance.pitch = useReadAloud().pitch;
    //音量(范围0到1)
    this.utterance.volume = useReadAloud().volume;
    const voices = window.speechSynthesis.getVoices();
    this.utterance.voice = voices[4];
    window.speechSynthesis.speak(this.utterance);
    this.utterance.onstart = () => {
      useReadAloud().setReadAloudStatus(true)
      this.status = false
      this.createStyleAddBody()
    }
    this.utterance.onend = () => {
      useReadAloud().setReadAloudStatus(false)
      this.status = true
      this.clearStyle()
    }
  }

  /**暂停*/
  pause(callback) {
    window.speechSynthesis.pause();
    this.utterance.onpause = () => {
      callback && callback()
    }
  }

  /**恢复朗读*/
  resume(callback) {
    window.speechSynthesis.resume();
    this.utterance.onresume = () => {
      callback && callback()
    }
  }

  /**关闭*/
  close(callback) {
    window.speechSynthesis.cancel();
    this.utterance.onresume = () => {
      useReadAloud().setReadAloudStatus(false)
      callback && callback()
    }
  }

  /**创建style标签生成样式*/
  createStyleAddBody() {
    this.style = document.createElement('style')
    let styleStr = ''
    this.elementListIdName.forEach(ele => {
      styleStr += `#${ele},`
    })
    styleStr += '__pp{background-color:#0078d7}'
    this.style.innerHTML = styleStr
    document.querySelector('body').appendChild(this.style)
  }

  /**清除样式*/
  clearStyle() {
    this.style.remove()
  }
}

const _ReadAloud = new ReadAloud()
export default _ReadAloud