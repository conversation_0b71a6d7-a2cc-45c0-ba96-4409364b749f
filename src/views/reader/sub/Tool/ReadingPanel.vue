<!-- 朗读控制面板 -->
<style lang="scss" scoped>
// .ReadingPanel {
//   width: 140px;
//   background: #000000;
//   opacity: 0.7;
//   padding: 10px;
//   box-sizing: border-box;
//   color: #fff;
//   border-radius: 50px;
//   display: flex;
//   justify-content: space-between;
//   align-items: center;

//   .main {
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
//     font-size: 30px;
//   }

//   .icon {
//     padding: 0 5px;

//     &:hover {
//       color: var(--hoverfont);
//     }

//     cursor: pointer;
//   }
// }
</style>

<template>
  <!-- <template v-if="readAloudStore.readAloudStatus">
    <DragBox :startOption="startOption" :margins='20' localName="__ReadingPanel__">
      <div class="ReadingPanel">
        <header>{{ playStatus ? '朗读中' : '已暂停' }}</header>
        <div class="main">
          <el-icon class="icon" @click="pause" v-if="playStatus">
            <VideoPause />
          </el-icon>
          <el-icon class="icon" @click="resume" v-else>
            <VideoPlay />
          </el-icon>
          <el-icon class="icon" @click="close">
            <CloseBold />
          </el-icon>
        </div>
      </div>
    </DragBox>
  </template> -->
</template>
<script setup>
// import { VideoPlay, VideoPause, CloseBold } from '@element-plus/icons-vue'
// import { ref, watch } from 'vue'
// import DragBox from '@/components/DragDialog/DragBox.vue'
// import useReadAloud from '@/store/modules/readAloud'
// import ReadAloud from "./ReadAloud.js"
// const readAloudStore = useReadAloud()

// const startOption = { x: 600, y: 70 }
// const playStatus = ref(true)


// /**继续*/
// const resume = () => {
//   ReadAloud.resume(() => {
//     playStatus.value = true
//   })
// }

// /**暂停*/
// const pause = () => {
//   ReadAloud.pause(() => {
//     playStatus.value = false
//   })
// }



// const close = () => {
//   ReadAloud.close(() => {
//     playStatus.value = true
//   })
// }

// watch(
//   () => readAloudStore.readAloudStatus,
//   (val) => {
//   },
//   { deep: true, immediate: true }
// )
</script>
