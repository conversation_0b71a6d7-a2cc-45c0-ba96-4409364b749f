<!-- 选中阅读器文字出现的公工具栏 -->
<style lang="scss" scoped>
.Selection {
  // background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  .Selection-nav {
    height: 28px;
    display: flex;
    margin-bottom: 5px;
    .Selection-nav-item {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background-color: #484949;
      color: #fff;
      margin: 0 3px;
      cursor: pointer;
      position: relative;
      &:hover {
        color: var(--hoverfont);
      }
    }
  }
  .Selection-nav-item2 {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 68px;
    height: 28px;
    border-radius: 28px;
    background-color: #484949;
    padding: 3px;
    box-sizing: border-box;
    & > section {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      cursor: pointer;
    }
    & > section:nth-of-type(1) {
      background-color: #ffc560;
    }
    & > section:nth-of-type(2) {
      background-color: #8bb4f0;
    }
    & > section:nth-of-type(3) {
      background-color: #f0828a;
    }
  }
  .Selection-content {
    width: 526px;
    height: 54px;
    background-image: url("@/assets/images/selectionTableBar/background.png");
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: space-around;
    align-items: center;
    .Selection-content-item {
      text-align: center;
      font-size: 12px;
      color: #fff;
      padding-bottom: 7px;
      box-sizing: border-box;
      position: relative;
      cursor: pointer;
      &:hover {
        color: var(--hoverfont);
      }
    }
  }
}
</style>

<template>
  <div
    :id="TEXT_SELECTION_TOOLBAR_ID"
    class="Selection"
    :style="`left:${x - 263}px;top:${y - 54 - 33}px`"
    @click.stop="() => {}"
  >
    <div class="Selection-nav">
      <div
        class="Selection-nav-item __flex-center__"
        :style="'color:' + drawALineStyle.color"
        v-show="drawALineStatus"
        @click="setLineStyle('background')"
      >
        <svg-icon iconClass="A" style="font-size: 16px" />
      </div>
      <div
        class="Selection-nav-item __flex-center__"
        v-show="drawALineStatus"
        @click="setLineStyle('underline')"
      >
        <svg-icon iconClass="A1" style="font-size: 16px" />
      </div>
      <div
        class="Selection-nav-item __flex-center__"
        v-show="drawALineStatus"
        @click="setLineStyle('underline double')"
      >
        <svg-icon iconClass="A_2" style="font-size: 16px" />
      </div>
      <div
        class="Selection-nav-item __flex-center__"
        v-show="drawALineStatus"
        @click="setLineStyle('underline wavy')"
      >
        <svg-icon iconClass="A_3" style="font-size: 16px" />
      </div>
      <div class="Selection-nav-item2" v-show="drawALineStatus">
        <section @click="tabStyleColor('#FFC560')"></section>
        <section @click="tabStyleColor('#8BB4F0')"></section>
        <section @click="tabStyleColor('#F0828A')"></section>
      </div>
    </div>
    <div class="Selection-content">
      <div
        class="Selection-content-item"
        v-for="ele in option"
        :key="ele.icon"
        @click.stop="clickItem(ele.icon)"
        :style="
          ele.icon === 'underline' && drawALineStatus
            ? 'color: var(--hoverfont);'
            : ''
        "
      >
        <svg-icon :iconClass="ele.icon" style="font-size: 16px" />
        <div>{{ ele.title }}</div>
        <div
          class="AIicon"
          style="position: absolute; right: -8px; top: -5px"
          v-if="ele.ai"
        >
          <svg-icon iconClass="AIicon" style="font-size: 10px" />
        </div>
        <div
          class="AIicon"
          style="position: absolute; right: 4px; top: -5px"
          v-if="ele.icon === 'spirit'"
        >
          <svg-icon iconClass="AIicon" style="font-size: 10px" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, defineProps } from "vue";
import { saveBookLine, getBookLine, deleteBookLine } from "@/api/book/reader";
import useReader from "@/store/modules/reader";
import { getToken } from "@/utils/auth";
import { findClosestPageNode, CHAPTER_PAGE_NUMBER, deriveIntersectionLinemarksData, TEXT_ID_DELIMITER, TEXT_SELECTION_TOOLBAR_ID, deriveFromAndEndWordIdList  } from "@/utils/reader";

const store = useReader();
const STYLE_ID = "__abcdefghijklmnopqrstuvwxyz__";
const props = defineProps({
  x: {
    type: Number,
    default: 0,
  },
  y: {
    type: Number,
    default: 0,
  },
  elementList: {
    type: Array,
    default: () => [],
  },
  elementIdList: {
    type: Array,
    default: () => [],
  },
  close: Function,
  isStatus: Boolean,
  elementText: String
});
const drawALineStatus = ref(false);
// text-decoration underline --单实线 | underline double --双实线 | underline wavy --波浪线 | background --背景色
// text-decoration-color 下划线颜色
const drawALineStyle = reactive({
  color: "#FFC560",
  lineStyle: "underline",
});
const lineDefaultValue = ref({});
const clickItem = (type) => {
  if (type !== "underline") {
    props.close(type);
  } else {
    addBookLine("own");
    createStyle();
  }
};
const tabStyleColor = (v) => {
  drawALineStyle.color = v;
  addBookLine();
};
const setLineStyle = (v) => {
  drawALineStyle.lineStyle = v;
  addBookLine();
};

/**
 * 保存划线
 */
const addBookLine = async (source) => {
  if (!getToken()) return;
  if (drawALineStatus.value && source) {
    await deleteBookLine({ lineId: lineDefaultValue.value.lineId });
  } else {
    const { fromWordIdList, endWordIdList } = deriveFromAndEndWordIdList(props.elementIdList)

    // 有任何重合的划线都会删除，包含交集的划线以及完全匹配的划线
    const existedIntersectionLinemarksData = deriveIntersectionLinemarksData(toRaw(props.elementIdList), store.bookLineData);
    if (existedIntersectionLinemarksData?.length > 0) {
      await Promise.all(existedIntersectionLinemarksData.map((lineid) => {
        return deleteBookLine({ lineId: lineid });
      }))
    }

    const parentPageNode = findClosestPageNode(
      document.querySelector("#" + props.elementIdList[0])
    );
    const pageNumberInChapter = parentPageNode.getAttribute(CHAPTER_PAGE_NUMBER);
    const data = {
      bookId: store.comprehensiveBookData.bookId,
      chapterId: store.chapterId,
      pageNumber: pageNumberInChapter,
      word: props.elementText,
      color: drawALineStyle.color,
      lineStyle: drawALineStyle.lineStyle,
      fromWordId: fromWordIdList,
      endWordId: endWordIdList,
    };
    await saveBookLine(data);
  }
  const res = await getBookLine({
    bookId: store.comprehensiveBookData.bookId,
    color: null,
  });
  if (res.code === 200) {
    store.setBookLineData(res.data);
  }
  isDefaultValue();
};

const createStyle = () => {
  drawALineStatus.value = !drawALineStatus.value;
  removeStyle();
  const styles = document.createElement("style");
  styles.id = STYLE_ID;
  styles.innerHTML = `${props.elementIdList.join(', ')}{ background-color:#0078d7;color:#fff }`;
  const bodys = document.querySelector("body");
  bodys.appendChild(styles);
};

const removeStyle = () => {
  const styles = document.getElementById(STYLE_ID);
  if (styles) styles.remove();
};

/**判断选中是否为默认值*/
const isDefaultValue = () => {
  const { fromWordIdList, endWordIdList } = deriveFromAndEndWordIdList(props.elementIdList)
  const v = store.bookLineData.find(
    (ele) => ele.fromWordId === fromWordIdList && ele.endWordId === endWordIdList
  );
  if (v) {
    drawALineStatus.value = true;
    drawALineStyle.color = v.color;
    drawALineStyle.lineStyle = v.lineStyle;
    lineDefaultValue.value = v;
  } else {
    lineDefaultValue.value = {};
  }
};

onMounted(() => {
  isDefaultValue();
});

onBeforeUnmount(() => {
  removeStyle();
});
const option = [
  {
    title: "复制",
    icon: "fuzhi",
  },
  {
    title: "划线",
    icon: "underline",
  },
  {
    title: "笔记",
    icon: "note",
  },
  {
    title: "百科",
    icon: "baike",
  },
  {
    title: "词典",
    icon: "shuben",
  },
  {
    title: "朗读",
    icon: "langdu",
    // ai: true,
  },
  {
    title: "纠错",
    icon: "jiucuo",
  },
  {
    title: "阅读精灵",
    icon: "spirit",
  },
  {
    title: "翻译",
    icon: "translate",
    ai: true,
  },
];
</script>
