<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-27 13:17:53
 * @LastEditTime: 2025-02-26 14:12:26
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Tool\SideToolsBox.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 模块说明 -->
<style lang="scss" scoped>
.SideToolsBox {
  height: calc(100vh - 52px);
  position: fixed;
  background-color: var(--modelBackgroundColor);
  bottom: 0;
  right: 83px;
  z-index: 1;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;
  transition: 0.3s;
  box-shadow: var(--boxShadow);
}
</style>

<template>
  <div
    class="SideToolsBox"
    :style="`transform:translateX(${vistible ? '0' : '150%'});padding:${vistible ? '20px' : '0'}`"
  >
    <slot></slot>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, defineProps } from "vue";
const vistible = ref(false);
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
  },
});

watch(
  () => props.isShow,
  (val) => {
    vistible.value = val;
  },
  { deep: true, immediate: true }
);
onMounted(() => {});
</script>
