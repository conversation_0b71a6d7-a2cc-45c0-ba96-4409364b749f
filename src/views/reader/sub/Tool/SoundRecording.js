// 录音的辅助工具
class SoundRecording {
  constructor() {
    this.mediaRecorder = null //创建处理录音的实例
    this.audioChunks = [] //录音分段保存的数据
  }
  /**
   * 开始录音
   */
  startRecording() {
    this.mediaRecorder = null
    this.audioChunks = []
    return new Promise((resolve, reject) => {
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then(stream => {
          this.mediaRecorder = new MediaRecorder(stream)
          this.mediaRecorder.start()
          this.mediaRecorder.ondataavailable = event => {
            this.audioChunks.push(event.data)
          }
          resolve()
        })
        .catch(error => {
          console.error('获取媒体设备权限失败：', error)
          reject(error)
        })
    })
  }
  /**
   * 结束录音,返回blob
   */
  stopRecording() {
    return new Promise((resolve, reject) => {
      this.mediaRecorder.stop()
      this.mediaRecorder.onstop = () => {
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/mp3' })
        // const audioUrl = URL.createObjectURL(audioBlob);
        resolve(audioBlob)
      }
    })
  }
}

export default new SoundRecording()
