<template>
  <el-dialog
    v-model="dialogVisible"
    width="180px"
    :fullscreen="false"
    class="tts-reading-dialog"
    :modal="false"
    :draggable="false"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body>
    <div class="tts-reading-dialog-body">
      <el-icon><Microphone /></el-icon>
      <span class="in-reading">朗读中</span>
      <span class="reading-status" @click="controlReadingStatus">{{ inReading ? '暂停': '继续' }}</span>
      <el-icon class="reading-complete" @click="terminateReading"><Close /></el-icon>
    </div>
  </el-dialog>
</template>

<script setup>
// https://github.com/tom-s/speak-tts
import { ref, onMounted } from 'vue';
import Speech from 'speak-tts';
import useTTSReading, { TTS_READING_STATUS } from "@/store/modules/readAloud";
import { ElMessage } from 'element-plus';

const ttsReadingStore = useTTSReading();

const isInitialized = ref(true);

// const selectedLang = ref('');
const selectedVoice = ref(null);
const availableVoices = ref([]);
// const rate = ref(1);
const dialogVisible = ref(false)

let speechInstance = null

function controlReadingStatus() {
  if (ttsReadingStore.readingStatus === TTS_READING_STATUS.RUNNING) {
    speechInstance.pause();
    ttsReadingStore.setReadingStatus(TTS_READING_STATUS.PAUSED)
  } else if (ttsReadingStore.readingStatus === TTS_READING_STATUS.PAUSED) {
    speechInstance.resume();
    ttsReadingStore.setReadingStatus(TTS_READING_STATUS.RUNNING)
  }
}

function terminateReading() {
  speechInstance.cancel();
  ttsReadingStore.setReadingStatus(TTS_READING_STATUS.STOP)
  ttsReadingStore.startReadWithAloud('')
  dialogVisible.value = false
}

function speak(text) {
  const language = selectedVoice.value
  if(language) {
    speechInstance.setLanguage(language.lang)
    speechInstance.setVoice(language.name)
  }
  speechInstance.speak({
    text: text,
    queue: false,
    splitSentences: false,
    listeners: {
      onstart: () => {
        // console.log("Start utterance")
        ttsReadingStore.setReadingStatus(TTS_READING_STATUS.RUNNING)
      },
      onend: () => {
        // console.log("End utterance")
        terminateReading()
      },
      onresume: () => {
        ttsReadingStore.setReadingStatus(TTS_READING_STATUS.RUNNING)
        // console.log("Resume utterance")
        // controlReadingStatus()
      },
      onboundary: (event) => {
        // console.log(event.name + ' boundary reached after ' + event.elapsedTime + ' milliseconds.')
      }
    }
  }).then((data) => {
    // console.log("Success !", data)
    ttsReadingStore.setReadingStatus(TTS_READING_STATUS.STOP)
  })
  .catch(e => {
    console.error("An error occurred :", e)
    terminateReading()
  })
}

function _init() {
	speechInstance = new Speech()
	speechInstance.init({
		'volume': 0.5,
		'lang': 'zh-CN',
		'rate': 1,
		'pitch': 1,
		//'voice':'Google UK English Male',
		'splitSentences': false,
		'listeners': {
			'onvoiceschanged': (voices) => {
				// console.log("Voices changed", voices)
			}
		}
	}).then((data) => {
    isInitialized.value =
		// console.log("Speech is ready", data)
    availableVoices.value = data
	}).catch(e => {
		console.error("An error occured while initializing : ", e)
	})
}

const inReading = computed(() => {
  return ttsReadingStore.readingStatus === TTS_READING_STATUS.RUNNING
})

onMounted(() => {
  _init()
});

watch(
  () => ttsReadingStore.textInReading,
  (nValue, oValue) => {
    if (!speechInstance.hasBrowserSupport()) {
      ElMessage.error('您的浏览器不支持本地系统语音朗读')
      return
    }
    if (nValue === oValue) {
      return
    }
    if (nValue) {
      dialogVisible.value = true;
      speak(nValue);
    } else {
      dialogVisible.value = false;
      stop();
    }
  }
)
</script>
<style lang="scss">
.tts-reading-dialog {
  padding: 0;
  background-color: transparent;
  .tts-reading-dialog-body {
    border-radius: 20px;
    color: rgb(29 165 24);
    padding: 5px;
    background-color: rgb(201 233 198);
    display: flex;
    flex-direction: row;
    align-items: center;
    .el-icon {
      width: 1.6em;
      height: 1.6em;
      svg {
        height: 100%;
        width: 100%;
      }
    }
  }
  .el-dialog__header {
    padding: 0;
  }
}
</style>
<style scoped lang="scss">
.tts-reading-dialog {
  .in-reading {
    margin: 0 0 0 0.7em;
  }
  .reading-status {
    cursor: pointer;
    margin: 0 1em 0 1em;
  }
  .reading-complete {
    cursor: pointer;
  }
}
</style>
