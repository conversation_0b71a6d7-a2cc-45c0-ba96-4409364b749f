<!-- 添加或的附件 音频和图片的展示组件 -->
<style lang="scss" scoped>
.audio_icon {
  bottom: 10%;
  right: 10%;
  position: absolute;
  color: #0966b4;
}
.note-upload-item-bg {
  background-color: rgb(245, 249, 252);
}
.float-column {
  .note-upload-item {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 3px;
    margin: 3px;
    cursor: pointer;
    &:hover {
      border-color: var(--hoverfont);
    }

    &.audio-item .note-item-imageBg {
      position: relative;
      background-image: url('@/assets/images/sidebar/note-voice.png');
      background-size: 38% 38%;
      background-position: center 78%;
      .audio-name {
        top: 20%;
        position: absolute;
        width: 100%;
      }
      .audio_kb {
        top: 40%;
        position: absolute;
        width: 100%;
      }
    }
    .note-item-imageBg {
      background-size: 100%;
      background-repeat: no-repeat;
      height: 100%;
      background-position: center center; // 让背景图居中显示
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover; // 确保图片覆盖整个节点
    }

    .maskLayer {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      opacity: 0;
      transition: opacity 0.3s;
      .icon {
        width: 30px;
        height: 30px;
        color: white;
        margin: 0 10px;
        cursor: pointer;
      }
    }

    &:hover .maskLayer {
      opacity: 1;
    }
  }
}

.note-upload-list.dual-column {
  display: flex;
  flex-wrap: wrap;
  gap: 6px; // 设置节点之间的间距
}
.dual-column {
  .note-upload-item {
    border-radius: 4px;
    // flex: 1 1 calc(50% - 3px); // 每行显示两个节点，减去间距
    width: calc(50% - 3px);
    box-sizing: border-box;
    position: relative;
    aspect-ratio: 6/3;
    overflow: hidden;
    position: relative;

    &.audio-item {
      .note-item-imageBg {
        background-image: url('@/assets/images/sidebar/note-voice.png');
        background-size: 22% 44%;
        background-position: 8% 49%;
        .audio-name {
          top: 20%;
          position: absolute;
          width: 100%;
          padding-left: 30%;
        }
      }
    }
    .note-item-imageBg {
      background-size: 100%;
      background-repeat: no-repeat;
      height: 100%;
      background-position: center center; // 让背景图居中显示
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover; // 确保图片覆盖整个节点
    }

    .maskLayer {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      opacity: 0;
      transition: opacity 0.3s;

      .icon {
        width: 30px;
        height: 30px;
        color: white;
        margin: 0 10px;
        cursor: pointer;
      }
    }

    &:hover .maskLayer {
      opacity: 1;
    }
  }
}
</style>

<template>
  <div
    class="note-upload-list"
    :class="{
      'dual-column': mode === 'dual',
      'float-column': mode === 'float',
    }">
    <slot name="addUpload" />
    <div
      class="note-upload-item note-upload-item-bg"
      v-for="(attachmentItem, i) in localAttachmentList"
      :key="attachmentItem.noteId"
      :class="{ 'audio-item': isAudioExtension(attachmentItem.attachmentUrl) }">
      <div
        v-if="isImgExtension(attachmentItem.attachmentUrl)"
        :style="{ backgroundImage: `url(${attachmentItem.attachmentUrl})` }"
        class="note-item-imageBg">
        <div
          class="maskLayer">
          <div class="icon"
            @click="zoom(attachmentItem)">
            <el-icon><zoom-in /></el-icon>
          </div>
          <div class="icon"
            v-if="supportRemove">
            <el-popconfirm
              title="确定移除该附件吗？"
              @confirm="confirmEvent(attachmentItem)"
              @cancel="cancelEvent">
              <template
                #reference>
                <el-icon
                  title="移除附件">
                  <Delete />
                </el-icon>
              </template>
            </el-popconfirm>
          </div>
        </div>
      </div>
      <div
        v-else-if="isAudioExtension(attachmentItem.attachmentUrl)"
        class="note-item-imageBg">
        <div
          class="audio-name __ellipsis__">
          <span>{{ attachmentItem.attachmentName }}</span>
          <!-- mp3大小 -->
          <div
            v-if="!supportRemove"
            class="audio_kb">
            {{ attachmentItem.attachmentSize + 'kb' }}
          </div>
        </div>
        <el-icon
          v-if="!supportRemove"
          class="audio_icon"
          :size="16">
          <VideoPlay />
        </el-icon>
        <div
          class="maskLayer">
          <div class="icon"
            @click="zoom(attachmentItem)">
            <el-icon
              style="margin: 10px 0 0 10px;">
              <VideoPlay />
            </el-icon>
          </div>
          <div class="icon">
            <el-popconfirm
              title="确定移除该附件吗？"
              @confirm="confirmEvent(attachmentItem)"
              @cancel="cancelEvent">
              <template
                #reference>
                <el-icon
                  style="margin-top: 10px;"
                  title="移除附件">
                  <Delete />
                </el-icon>
              </template>
            </el-popconfirm>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import usePreview from '@/store/modules/preview.js'
import { ref, defineProps, watch, toRaw } from 'vue'
const previewStore = usePreview()
const localAttachmentList = ref([])
const emits = defineEmits(['change'])
const props = defineProps({
  supportRemove: false,
  mode: {
    type: String,
    default: 'dual'
  },
  noteItem: {
    type: Object,
    default: () => ({})
  },
  attachmentList: {
    type: Array,
    default: () => []
  }
})
const confirmEvent = item => {
  emits('remove', props.noteItem, item)
}
const cancelEvent = () => {
  console.log('cancel!')
}
function isImgExtension(fileUrl) {
  return fileUrl.endsWith('.jpg') || fileUrl.endsWith('.png') || fileUrl.endsWith('.jpeg')
}
function isAudioExtension(fileUrl) {
  return fileUrl.endsWith('.mp3') || fileUrl.endsWith('.wav')
}
function zoom(v) {
  previewStore.setData({ url: v.attachmentUrl, title: v.attachmentName })
}
// function del(toBeDeleteAttachmentItem) {
//   // localAttachmentList.value = localAttachmentList.value.filter((ele) => ele.attachmentUrl !== v.attachmentUrl)
//   emit('change', toBeDeleteAttachmentItem)
// }

onMounted(() => {
  // console.log(props)
  if (props.attachmentList) {
    localAttachmentList.value = JSON.parse(JSON.stringify(toRaw(props.attachmentList)))
  }
})
watch(
  () => props.attachmentList,
  nval => {
    if (nval) {
      localAttachmentList.value = JSON.parse(JSON.stringify(toRaw(nval)))
    }
  },
  { deep: true }
)
</script>
