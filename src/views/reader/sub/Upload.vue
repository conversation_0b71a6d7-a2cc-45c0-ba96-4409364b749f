<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2025-01-03 16:05:13
 * @LastEditTime: 2025-01-21 13:25:07
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\sub\Upload.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<style lang="scss" scoped>
.Upload {
  min-height: 100px;
  .Upload-item {
    width: 100px;
    height: 100px;
    float: left;
    border: 1px dashed #999;
    border-radius: 3px;
    margin: 3px;
    cursor: pointer;
    &:hover {
      border-color: var(--hoverfont);
    }
  }
  .Upload-item-img {
    width: 100px;
    height: 100px;
    float: left;
    border-radius: 3px;
    overflow: hidden;
    margin: 3px;
    position: relative;
    .maskLayer {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      transition: 0.3s;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      color: rgba(0, 0, 0, 0);
      font-size: 16px;
      &:hover {
        background-color: rgba(0, 0, 0, 0.4);
        color: #fff;
      }
      .icon {
        cursor: pointer;
        transition: 0.3s;
        &:hover {
          color: var(--hoverfont);
        }
      }
    }
    & > img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
<template>
  <div class="Upload">
    <div class="Upload-item __flex-center__" @click="selectImage">
      <el-icon><Plus /></el-icon>
    </div>
    <note-upload-list :attachmentList="eleItem.attachments" :support-remove="true" :note-item="eleItem" @remove="toRemoveAttachmentItem"></note-upload-list>
  </div>
</template>

<script setup>
import { ref, defineEmits, defineProps, watch } from 'vue'
import { Delete, Plus, ZoomIn } from '@element-plus/icons-vue'
import { _uploads } from '@/utils/aliOss.js'
import usePreview from '@/store/modules/preview.js'
const previewStore = usePreview()
const emit = defineEmits(['change'])
const props = defineProps({
  accept: {
    type: String,
    default: '.jpg,.png',
  },
  defaultData: Array,
})
const fileList = ref([])
function isImgExtension(fileUrl) {
  return fileUrl.endsWith('.jpg') || fileUrl.endsWith('.png') || fileUrl.endsWith('.jpeg')
}
function isAudioExtension(fileUrl) {
  return fileUrl.endsWith('.mp3') || fileUrl.endsWith('.wav')
}
const selectImage = async () => {
  const [res] = await _uploads({ accept: props.accept })
  fileList.value.push(res)
  emit('change', fileList.value)
}
const zoom = (v) => {
  previewStore.setData({ url: v.url, title: v.result.name })
}
const del = (v) => {
  fileList.value = fileList.value.filter((ele) => ele.url !== v.url)
  emit('change', fileList.value)
}
watch(
  () => props.defaultData,
  (val) => {
    if (val?.length) {
      fileList.value = val
      emit('change', fileList.value)
    }
  },
  { deep: true, immediate: true }
)
</script>
