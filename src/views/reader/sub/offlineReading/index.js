import JSZip from "jszip"

const jszipConfig = [
  {
    name: 'sub',
    type: 'folder',
    content: [
      {
        name: 'index.css',
        type: 'file',
        content: [`* {
          margin: 0;
          padding: 0;
        }
        body {
          width: 100vw;
          height: 100vh;
          overflow: hidden;
        }
        .header {
          width: 100%;
          height: 50px;
          background-color: #c9c9c9;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 20px;
          box-sizing: border-box;
        }
        .header .left {
          width: 200px;
          font-size: 24px;
          font-weight: bold;
        }
        .header .right {
          width: 200px;
        }
        .header .center {
          flex: 1;
          display: flex;
          justify-content: space-around;
          align-items: center;
        }
        .paging {
          font-size: 18px;
        }
        .lastBtn,
        .nextBtn {
          width: 30px;
          height: 30px;
        }
        .num {
          width: 30px;
          outline-style: none;
          height: 20px;
          margin: 0 5px;
        }
        
        .main {
          width: 100%;
          height: calc(100vh - 50px);
          background-color: #e4e2e2;
          display: flex;
          justify-content: space-around;
        }
        .page {
          width: 1000px;
          height: 100%;
          overflow-y: auto;
        }
        .pageItem {
          width: 100%;
          min-height: 1200px;
          margin: 10px 0;
          background-color: #fff;
          padding: 0 60px;
          box-sizing: border-box;
          display: flex;
          justify-content: space-between;
          flex-direction: column;
        }
        .pageItemMain {
          flex: 1;
        }
        .pageItemHeader,
        .pageItemFooter {
          width: 100%;
          height: 80px;
          display: flex;
          justify-content: space-around;
          align-items: center;
        }
        `]
      },
      {
        name: 'index.js',
        type: 'file',
        content: [`
        const { createApp, ref, h, toRaw, defineComponent, reactive } = Vue
        const { ElMessage } = ElementPlus
        class ToolFunction {
          /**模拟生成uuid*/
          static uuid() {
            return 'Key-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9)
          }
        
          /**大写英文字母转小写*/
          static convertToLowercase(input) {
            return input.replace(/[A-Z]/g, function (match) {
              return match.toLowerCase()
            })
          }
          static stringToBase64(str) {
            // 使用 TextEncoder 将字符串转换为 Uint8Array
            const encoder = new TextEncoder()
            const uint8Array = encoder.encode(str)
        
            // 将 Uint8Array 转换为二进制字符串
            let binaryString = ''
            uint8Array.forEach((byte) => {
              binaryString += String.fromCharCode(byte)
            })
        
            // 使用 btoa 将二进制字符串转换为 Base64
            return btoa(binaryString)
          }
          /**url地址转二进制*/
          static getPreviewFileUrl(url) {
            const previewFileUrlBase64 = this.stringToBase64(url)
            return \`https://preview.bell-lab.com/onlinePreview?url=\${previewFileUrlBase64}\`
          }
        }
        const PreviewDialog = {
          template: \`  <el-dialog v-model="data.isShow" :title="data.title" width="75%" top="20px" :destroy-on-close="true">
          <main style="width: 100%; height: 80vh">
            <slot></slot>
          </main>
        </el-dialog>\`,
        
          setup() {
            const data = reactive({
              isShow: false,
              title: '',
            })
        
            return { data }
          },
          props: {
            uid: {
              type: String,
              default: 'default',
            },
            title: String,
          },
          watch: {
            uid() {
              this.data.isShow = true
              this.data.title = this.title
            },
          },
        }
        const previewComponents = {
          //图片
          image: {
            template: \`  <div :style="{ 'text-align': nodeAlign }" :id="id">
            <el-image
              :style="{ height: height + 'px', width: width + 'px' }"
              :src="src"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="[src]"
              :initial-index="4"
              fit="cover" />
            <div v-if="isShowImageTitle == '1'">{{ name }}</div>
          </div>\`,
        
            setup() {
              return {}
            },
            props: {
              height: [Number, String],
              src: String,
              nodeAlign: String,
              name: String,
              isShowImageTitle: String,
              id: String,
              width: [Number, String],
            },
          },
        
          //板式图片
          imageLayout: {
            template: \`  <div :style="{ 'text-align': nodeAlign }" :id="id">
            <el-image
              :style="{ height: height + 'px', width: width + 'px' }"
              :src="src"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="[src]"
              :initial-index="4"
              fit="cover" />
            <div v-if="isShowImageTitle == '1'">{{ name }}</div>
          </div>\`,
            setup() {
              return {}
            },
            props: {
              height: [Number, String],
              src: String,
              nodeAlign: String,
              name: String,
              isShowImageTitle: String,
              id: String,
              width: [Number, String],
            },
          },
        
          //行内图片
          imageInLine: {
            template: \`<div :id="id" class="ImageInLine">
                <el-image :style="{ height: height+'px', width: width+'px' }" :src="src" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" :preview-src-list="[src]" :initial-index="4" fit="cover" />
              </div>\`,
            props: {
              src: String,
              id: String,
              height: [String, Number],
              width: [String, Number],
            },
          },
        
          //图标
          imageIcon: {
            template: \`<div :id="id" class="ImageInLine">
                <el-image :style="{ height: height+'px', width: width+'px' }" :src="src" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" :preview-src-list="[src]" :initial-index="4" fit="cover" />
              </div>\`,
            props: {
              src: String,
              id: String,
              height: [String, Number],
              width: [String, Number],
            },
          },
          //图片
          image: {
            template: \`<div :id="id">
            <el-image :style="{ height: height+'px', width: width+'px' }" :src="src" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" :preview-src-list="[src]" :initial-index="4" fit="cover" />
            </div>\`,
             props: {
              src: String,
              id: String,
              height: [String, Number],
              width: [String, Number],
            },
          }
        
          //画廊
          imageGallery: {
            template: \` <el-card>
            <div :style="
            'grid-template-columns: repeat('+columns+', 1fr);width: 98%;column-gap: 5px; margin: 0 auto;padding: 5px;display: flex;'">
              <div v-for="(item, index) in columnContents" :key="index" style="flex: 1;margin-bottom: 5px;break-inside: avoid;overflow: hidden;">
                <div v-for="ele in item" :key="ele.src">
                  <el-image :src="ele.src" :preview-src-list="[ele.src]" />
                  <div style="text-align: center;font-size: 14px;color: #555;margin-bottom: 10px;" v-if="isShowImgTitle === '1'">{{ ele.name }}</div>
                </div>
              </div>
            </div>
            <div style="text-align: center;" v-if="isShowGalleryTitle === '1'">{{ galleryTitle }}</div>
          </el-card>\`,
            props: {
              galleryTitle: String, //画廊标题
              isShowNo: String, // 是否显示图号 0否1是  默认显示
              isShowGalleryTitle: String, // 是否显示画廊标题 0否1是 默认显示
              isShowImgTitle: String, // 是否显示图片标题 0否1是 默认显示
              imgList: Array,
              columns: Number, //画廊几列
            },
            computed: {
              columnContents() {
                let arr = Array.from({ length: this.columns }, () => [])
                this.imgList.forEach((image, index) => {
                  const columnIndex = index % this.columns // 按列计算分配
                  arr[columnIndex].push(image)
                })
                return arr
              },
            },
          },
        
          // 视频
          video: {
            template: \`<div  :style="{ height: height + 'px', }" :src="src" >
            <video
              class="video-play"
              :src="src"
              preload="metadata"
              controlslist="nofullscreen nodownload noremoteplayback"
              controls
              oncontextmenu="return false"
              width='920'
              crossorigin="anonymous"></video>
          </div>\`,
            props: {
              height: String,
              src: String,
            },
          },
        
          //章头节头
          jointHeader: {
            template: \`<div class="headerbg" :style="'height:'+height+'px;background:url('+bgImg+') no-repeat;background-size:cover; line-height:'+height+'px;margin:15px 0'">
            <h1 class="header-title" :style="'color:'+color">
              {{ title }}
            </h1>
          </div>\`,
            props: {
              height: String,
              title: String,
              bgImg: String,
            },
          },
          //章头节头
          chapterHeader: {
            template: \`<div class="headerbg" :style="'height:'+height+'px;background:url('+bgImg+') no-repeat;background-size:cover; line-height:'+height+'px;margin:15px 0'">
            <h1 class="header-title" :style="'color:'+color">
              {{ title }}
            </h1>
          </div>\`,
            props: {
              height: String,
              title: String,
              bgImg: String,
            },
          },
        
          //音频
          audio: {
            template: \` <div style="margin:15px 0">
            <audio
              oncontextmenu="return false"
              :src="src"
              preload="auto"
              controls
              controlsList="nodownload"
              crossorigin="anonymous"
              style="width:100%"></audio>
            <div style="width:100%" style="text-align: center;">
              {{ name }}
            </div>
          </div>\`,
            props: {
              src: String,
              name: String,
            },
          },
        
          // 链接
          links: {
            template: \`<span style="display: inline-block;">
            <u class="title" @click="openLink">
              <span v-if="plateType === 'titleIcon' && type !== 'websiteLink'">{{ title }}</span>
              <span :href="href" v-else style=" display: flex;
              align-items: center;
              cursor: pointer;
              color: #0966b4;">{{ title }}</span>
              <el-icon><Link /></el-icon>
            </u>
          </span>\`,
            props: {
              plateType: String,
              type: String,
              title: String,
              href: String,
            },
            methods: {
              openLink() {
                switch (this.type) {
                  case 'chaptersInThisBook':
                    ElMessage.error('离线模式不支持章节跳转')
                    break
        
                  case 'websiteLink':
                    window.open(this.href, '_blank')
                    break
        
                  case 'crossReferencing':
                    ElMessage.error('离线模式不支持交叉引用跳转')
                    break
        
                  case 'resourceLibrary':
                    ElMessage.error('离线模式不支持打开资源库')
                    break
        
                  default:
                    break
                }
              },
            },
          },
        
          // 文件预览
          file: {
            template: \`
            <div style="margin:10px 0;width:100%;height: 50px;background-image: url(http://***********:1024/images/modelBackground.png);display: flex;justify-content: space-between;align-items: center;padding: 0 16px;box-sizing: border-box;font-size: 14px;" v-if="getFileIcon(url, true) !== 'ppt'">
            <div style=" display: flex;align-items: center;">
              <img :src="getFileIcon(url)" alt="" style="width: 28px; height: 28px" />
              <div style="  font-weight: bold;font-size: 16px;color: #0966b4;margin-left: 12px;">{{ name }}</div>
            </div>
            <el-button link @click="preview">打开</el-button>
          </div>
          <el-collapse v-model="activeNames" v-else>
            <el-collapse-item class="video-parent" name="1">
              <template #title>
                <div class="collapse-title" style="margin:10px 0">
                  <img :src="getFileIcon(url)" alt="" style="width: 17px; height: 17px" />
                  <span class="name">{{ name }}</span>
                </div>
              </template>
              <div>
                <iframe :src="ToolFunction.getPreviewFileUrl(url)" frameborder="0" width="920px" height="600px" style="transform: translateY(-47px)"></iframe></div
            ></el-collapse-item>
          </el-collapse>
          <PreviewDialog :uid="uid" :title="name">
              <iframe :src="ToolFunction.getPreviewFileUrl(url)" width="100%" height="100%" frameborder="0"></iframe>
          </PreviewDialog>
          \`,
            props: {
              type: String,
              url: String,
              name: String,
              nodeAlign: String,
              size: Number,
            },
            components: { PreviewDialog },
            setup() {
              const activeNames = ref(['1'])
              const iconOption = {
                ppt: 'http://***********:1024/images/ppt.png',
                pptx: 'http://***********:1024/images/ppt.png',
                doc: 'http://***********:1024/images/doc.png',
                docx: 'http://***********:1024/images/doc.png',
                html: 'http://***********:1024/images/html.png',
                pdf: 'http://***********:1024/images/pdf.png',
                zip: 'http://***********:1024/images/zip.png',
                mp3: 'http://***********:1024/images/mp3.png',
                acc: 'http://***********:1024/images/mp3.png',
                wav: 'http://***********:1024/images/mp3.png',
                flac: 'http://***********:1024/images/mp3.png',
                png: 'http://***********:1024/images/png.png',
                jpg: 'http://***********:1024/images/png.png',
                mp4: 'http://***********:1024/images/mp4.png',
                avi: 'http://***********:1024/images/mp4.png',
                mov: 'http://***********:1024/images/mp4.png',
                wmv: 'http://***********:1024/images/mp4.png',
                flv: 'http://***********:1024/images/mp4.png',
              }
              const uid = ref('')
              return {
                activeNames,
                iconOption,
                ToolFunction,
                uid,
              }
            },
            methods: {
              getFileIcon(url, type) {
                const suffix = url.split('.').pop()
                if (type) return suffix
                return this.iconOption[this.ToolFunction.convertToLowercase(suffix)]
              },
              preview() {
                this.uid = this.ToolFunction.uuid()
              },
            },
          },
        
          //代码快
          codeBlock: {
            template: \`<pre style="margin:10px 0;background-color: #2d2d2d;padding: 10px;border-radius: 5px;overflow-x: auto;">
              <code style="font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;color: #d63384;">
                {{code}}
              </code></pre>\`,
            props: {
              code: String,
            },
          },
        
          //作业试卷等
          questions: {
            template: \`
            <div style="margin:10px 0">
              <el-card>
                <div style="color:#f00">
                  离线模式暂不支持作业、习题、试卷等
                </div>
              </el-card>
            </div>
            \`,
          },
        }
        
        class _CreateTemplate {
          constructor() {
            this.addAttribute = ['link', 'iframe']
          }
          isAddAttribute(name) {
            return this.addAttribute.includes(name)
          }
          isModule(name) {
            return previewComponents[name]
          }
          /**获取样式*/
          getStyle(data, type) {
            if (type === 'mark') {
              const { marks } = data
              if (!marks?.length) return {}
              let styles = {}
              Object.keys(data?.attrs?.margin || {}).forEach((key) => {
                const item = margin[key]
                if (item) {
                  styles[\`margin-\${key}\`] = item + 'px'
                }
              })
        
              marks.forEach((ele) => {
                switch (ele.type) {
                  case 'highlight': //高亮
                    styles.backgroundColor = ele.attrs.color
                    break
        
                  case 'textStyle': //文字样式
                    for (const key in ele.attrs) {
                      const element = ele.attrs[key]
                      if (element) {
                        styles[key] = element
                      }
                    }
                    if (styles.fontSize === '14px') delete styles.fontSize
                    break
        
                  case 'textShadow': //文字阴影
                    styles.textShadow = '2px 2px 2px'
                    break
        
                  case 'textGlow': //文字效果--光
                    styles.textShadow =
                      ' 0 0 5px rgba(255, 255, 0, 0.8),0 0 10px rgba(255, 255, 0, 0.8),0 0 20px rgba(255, 255, 0, 0.8),0 0 30px rgba(255, 255, 0, 1),0 0 40px rgba(255, 255, 0, 1),0 0 50px rgba(255, 255, 0, 1)'
                    break
        
                  case 'textStroke': //文字轮廓
                    styles.TextStroke = '1px'
                    styles.textFillColor = 'transparent'
                    break
        
                  case 'italic': //文字倾斜
                    styles.fontStyle = 'italic'
                    break
        
                  case 'bold': //文字加粗
                    styles.fontWeight = 'bold'
                    break
        
                  case 'underline': //下划线
                    styles.textDecoration = 'underline'
                    break
        
                  case 'strike': //中划线
                    styles.textDecoration = 'line-through'
                    break
        
                  case 'subscript': //下标
                    styles.verticalAlign = 'sub'
                    break
        
                  case 'superscript': //上标
                    styles.verticalAlign = 'super'
                    break
        
                  default:
                    break
                }
              })
        
              for (const key in styles) {
                const element = styles[key]
                if (!element) {
                  delete styles[key]
                }
              }
              return styles
            } else {
              let obj = {}
        
              try {
                const { textAlign, listType, margin } = data?.attrs
                obj = { textAlign, listStyleType: listType }
                Object.keys(margin).forEach((key) => {
                  const item = margin[key]
                  if (item) {
                    obj[\`margin-\${key}\`] = item + 'px'
                  }
                })
              } catch (error) { }
        
              return obj
            }
          }
        
          /**获取标签名称*/
          getLableName(data) {
            const { type, attrs } = data
            switch (type) {
              case 'heading':
                return \`h\${attrs.level}\`
        
              case 'paragraph':
                return "p"
        
              case 'text':
                return "span"
        
              case 'orderedList': //有序列表
                return "ol"
        
              case 'bulletList': //有序列表
                return "ul"
        
              case 'listItem': //有序列表
                return "li"
        
              case 'blockquote':
                return "blockquote"
        
              case 'link':
                return "a"
        
              case 'tablePlus':
                return "div"
        
              case 'table':
                return "table"
        
              case 'tableRow':
                return "tr"
        
              case 'tableHeader':
                return "th"
        
              case 'tableCell':
                return "td"
        
              default:
                if (this.isModule(data.type)) {
                  return previewComponents[data.type]
                }
                break
            }
          }
        
          getProps(data, type, parentType) {
            if (!this.isModule(data.type) && !this.isAddAttribute(type)) {
              if (data.type === 'tableHeader' || data.type === 'tableCell') {
                const props = { ...toRaw(data.attrs) }
                delete props.align
                delete props.backgroundColor
                props['data-align'] = data.attrs.align
                props.style = { ...this.getStyle(data, type), backgroundColor: data.attrs.backgroundColor }
                return props
              }
        
              if (type === 'mark') {
                return { style: this.getStyle(data, type) }
              } else {
                const obj = { style: { ...this.getStyle(data), marginTop: '0.75em' } }
        
                if (parentType === 'tableHeader' || parentType === 'tableCell') {
                  delete obj.style.marginTop
                  delete obj.style.textAlign
                }
                if (data?.attrs) {
                  const { containerColor, backgroundImage, backgroundSize } = data?.attrs
                  if (containerColor) {
                    obj.style.backgroundColor = containerColor
                  }
        
                  if (backgroundImage) {
                    obj.style.backgroundImage = \`url(\${backgroundImage})\`
                  }
        
                  if (backgroundSize) {
                    obj.style.backgroundSize = backgroundSize
                    if (backgroundSize === 'auto') {
                      obj.style.backgroundRepeat = 'repeat'
                    } else {
                      obj.style.backgroundRepeat = 'no-repeat'
                    }
                  }
                }
        
                return obj
              }
            } else {
              const props = { ...toRaw(data.attrs) }
              if (data.type === 'tableHeader' || data.type === 'tableCell') {
                delete props.align
                delete props.backgroundColor
                props['data-align'] = data.attrs.align
                props.style = { backgroundColor: data.attrs.backgroundColor }
              }
              return props
            }
          }
        
          getChildren(data, type = 'mark') {
            const { content } = data
            if (!content?.length) return h('br', {}, '')
            const childrenList = []
        
            content.forEach((ele) => {
              let marksType = []
              if (ele.marks) marksType = ele.marks.map((item) => item.type)
        
              if (ele.text && !marksType.includes('link')) {
                childrenList.push(h(this.getLableName(ele), { ...this.getProps(ele, type), sign: 'selection' }, ele.text))
              } else if (marksType.includes('link')) {
                /**针对link处理*/
                const attrs = ele.marks.find((item) => item.type === 'link')?.attrs
                const marks = ele.marks.filter((item) => item.type !== 'link')
                const linkItem = { type: 'link', attrs, content: [{ text: ele.text, type: 'text', marks }] }
                childrenList.push(h(this.getLableName(linkItem), this.getProps(linkItem, 'link'), this.getChildren(linkItem)))
              } else {
                childrenList.push(h(this.getLableName(ele), { ...this.getProps(ele, '', data.type), sign: 'selection' }, this.getChildren(ele)))
              }
            })
        
            return childrenList
          }
          getParagraph(paragraphData) {
            const data = toRaw(paragraphData)
            return h(this.getLableName(data), { ...this.getProps(data), sign: 'selection' }, this.getChildren(data))
          }
        }
        const CreateTemplate = new _CreateTemplate()
        const Paragraph = defineComponent({
          props: {
            datas: Object,
          },
          setup(props) {
            return () => CreateTemplate.getParagraph(props.datas)
          },
        })
        
        const app = Vue.createApp({
          components: { Paragraph },
          setup() {
            // const pageNumber = ref(1)
            return {
              // pageNumber,
              pageData: _pageData.content,
            }
          },
          methods: {
            // lastPage() { },
            // nextPage() {
        
            // }
          },
          mounted() {
          },
        })
        app.use(ElementPlus)
        app.mount('#app')
        `]
      },
      {
        name: 'pageData.js',
        type: 'file',
        content: [`const _pageData = {
          "type": "doc",
          "content": [
            {
              "type": "page",
              "attrs": {
                "id": "2fwaqc4k",
                "extend": false,
                "class": "bellCss",
                "HTMLAttributes": null,
                "pageNumber": 1,
                "force": false,
                "slots": {},
                "pagesPosition": "top",
                "pagesAlign": "center",
                "pagesShow": true,
                "color": "#333"
              },
              "content": [
                {
                  "type": "chapterHeader",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "title": "认识与安装 Linux 操作系统",
                    "bgColor": null,
                    "bgImg": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1739582800602.svg",
                    "textAlign": "center",
                    "height": "258"
                  }
                },
                {
                  "type": "jointHeader",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "title": "1.1 项目描述",
                    "bgColor": null,
                    "bgImg": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1739598253503.png",
                    "textAlign": "center",
                    "height": "45",
                    "color": "#fff"
                  }
                },
                {
                  "type": "papers",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "width": null,
                    "height": 200,
                    "paperInfo": {
                      "createBy": "166",
                      "createTime": "2025-02-19 11:07:37",
                      "updateBy": "author",
                      "updateTime": "2025-02-19 18:06:06",
                      "remark": null,
                      "paperId": "1892048365118504962",
                      "paperTitle": "xinchunceshi",
                      "questionQuantity": 1,
                      "totalScore": 0,
                      "paperType": 1,
                      "delFlag": "0",
                      "dtbTestPaperQuestionCollectionList": null
                    }
                  }
                },
                {
                  "type": "papers",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "width": null,
                    "height": 200,
                    "paperInfo": {
                      "createBy": "166",
                      "createTime": "2025-02-19 17:51:10",
                      "updateBy": null,
                      "updateTime": null,
                      "remark": null,
                      "paperId": "1892149920031244290",
                      "paperTitle": "作业流",
                      "questionQuantity": 1,
                      "totalScore": 0,
                      "paperType": 2,
                      "delFlag": "0",
                      "dtbTestPaperQuestionCollectionList": null
                    }
                  }
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "hjrydb5z",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": 1,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Times New Roman",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正书宋_GBK",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "是一种应用广泛的操作系统，它不仅安装在通用计算机中，还大量嵌入智能手机、平板电脑、机顶盒、智能电视机、路由器、防火墙、导航系统和游戏机等各种物联网的智能设备中，"
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Times New Roman",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正书宋_GBK",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "已成为全球各类网络终端设备中装有量和用户数量最多的操作系统。"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "9lo8uig7",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": 1,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正书宋_GBK",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "德雅职业学校是一所中等职业学校，几年前，已经构建了自己的校园网，如图 "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Times New Roman",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "1- 1 "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正书宋_GBK",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "所示。基于 "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Times New Roman",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正书宋_GBK",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "特有的高可靠性、高稳定性和高安全性等特点及服务器的运行效率和建设成本等因素的考虑，学校选择了 "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Times New Roman",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正书宋_GBK",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "作为网络服务器的操作系统。"
                    }
                  ]
                },
                {
                  "type": "imageLayout",
                  "attrs": {
                    "id": "rzh1rokl",
                    "extend": false,
                    "class": "bellCss",
                    "nodeAlign": "center",
                    "vnode": true,
                    "type": "image/png",
                    "name": "图1-1",
                    "size": 161284,
                    "file": null,
                    "src": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1739605180019.png",
                    "content": null,
                    "width": "353.00",
                    "height": 200,
                    "left": 0,
                    "top": 0,
                    "angle": null,
                    "draggable": false,
                    "rotatable": false,
                    "equalProportion": true,
                    "flipX": false,
                    "flipY": false,
                    "uploaded": true,
                    "error": false,
                    "previewType": "imageLayout",
                    "imageTitle": "图片标题",
                    "linkAddress": "",
                    "isShowNo": "1",
                    "isShowImageTitle": "1"
                  }
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "angx3g6w",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正书宋_GBK",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "如何从众多的 "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Times New Roman",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正书宋_GBK",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "中选择一款适合的产品，以及正确地安装 "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Times New Roman",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正书宋_GBK",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "服务器，并能实现从本地或异地主机实施本地或远程登录 "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Times New Roman",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正书宋_GBK",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "服务器，是校园网的网"
                    }
                  ]
                }
              ]
            },
            {
              "type": "page",
              "attrs": {
                "id": "0m6wzru7",
                "extend": false,
                "class": "bellCss",
                "HTMLAttributes": null,
                "pageNumber": 2,
                "force": true,
                "slots": {},
                "pagesPosition": "top",
                "pagesAlign": "center",
                "pagesShow": true,
                "color": "#333"
              },
              "content": [
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "5hzz40qk",
                    "extend": true,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正书宋_GBK",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "络管理员首先遇到和必须解决的问题。"
                    }
                  ]
                },
                {
                  "type": "jointHeader",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "title": "1.2.Linux的特点",
                    "bgColor": null,
                    "bgImg": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1739598253503.png",
                    "textAlign": "center",
                    "height": "45",
                    "color": "#fff"
                  }
                },
                {
                  "type": "heading",
                  "attrs": {
                    "id": "u9co3ha2",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "data-toc-id": "sfjcvn",
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": "",
                    "level": 1
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正品尚黑简体",
                            "fontSize": "18px",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "1.2.1 Linux的诞生与特点"
                    }
                  ]
                },
                {
                  "type": "heading",
                  "attrs": {
                    "id": "rcdd1t",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "data-toc-id": "rcdd1t",
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": "",
                    "level": 2
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "汉仪中宋简",
                            "fontSize": "16px",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "1.Linux的诞生"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "rk6hehfq",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": 1,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux 在外表和性能上都与 UNIX 非常接近，要讲 Linux 的产生，不能不提到 UNIX 和Minix。UNIX 是由美国贝尔实验室的 Ken L.Thompson 和 Dennis M.Ritchie 在 1969—1973 年设计的多用户、多任务分时操作系统，UNIX 操作系统相当可靠并运行稳定，至今仍广泛应用于银行、航空、保险、金融等领域的大中型计算机和高端服务器中。UNIX 的早期版本源代码是免费开放的，但是从 1979 年发行的版本 7 开始，AT&T 公司为了其商业利益，明确提出了“不可对学生提供源代码”的严格限制，致使操作系统的课程只讲理论。 由于操作系统的理论繁杂、算法众多，所以学生只学理论并不清楚实际的操作系统是如何运作的。 为了扭转这种局面，荷兰著名计算机科学家 Andrew S.Tanenbaum 教授在 1984—1986 年编写了一个开放源代码且与 UNIX完全兼容、有全新内核架构的操作系统（大约 1.2 万行代码），并以小型 UNIX（mini-UNIX）之意，将其命名为 Minix。Minix 主要面向教师教学研究和学生学习操作系统原理所使用。 由于Andrew S.Tanenbaum 教授坚持保持 Minix 的小型化，以便学生能在一个学期内学完课程，所以没有接纳众人对 Minix 扩展的要求，最终导致 Linux 的诞生。"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "zlqga8lw",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": 1,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "1990 年，芬兰赫尔辛基大学学生 Linus Torvalds（林纳斯 · 托瓦兹）为完成自己操作系统课程的作业，开始基于 Minix 编写一些程序，最后他惊奇地发现这些程序已经足够实现一个操作系统的基本功能，他将这个操作系统命名为 Linux，也就是 Linus’s UNIX 的意思，并且以可爱的胖企鹅作为其标志。1991 年 10 月 5 日，Linus Torvalds 宣布了 Linux 系统的第一个正式版本，其版本"
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        },
                        {
                          "type": "inputCommon",
                          "attrs": {
                            "vnode": true,
                            "inputCommon": {
                              "id": 1739758097181
                            }
                          }
                        }
                      ],
                      "text": "号为 0.02"
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "。Linus Torvalds 是一个完全的理想主义者，他希望 Linux 是一个完全免费的操作系统，并在 GNU 的 GPL（General Public License，通用公共许可证）原则下发行。 所谓 GNU（GNU是 GNU is Not UNIX 的递归"
                    }
                  ]
                }
              ]
            },
            {
              "type": "page",
              "attrs": {
                "id": "tl02049a",
                "extend": false,
                "class": "bellCss",
                "HTMLAttributes": null,
                "pageNumber": 3,
                "force": true,
                "slots": {},
                "pagesPosition": "top",
                "pagesAlign": "center",
                "pagesShow": true,
                "color": "#333"
              },
              "content": [
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "ns4l0cwe",
                    "extend": true,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "缩写）是一个自由软件计划，最初由美国麻省理工学院的研究员Richard M.Stallman 倡导，以研究和开发自由软件为目的，以“开放、共享、自由”为宗旨。GNU 的软件可以自由使用和修改，但是用户在发布 GNU 软件时，必须让下一个用户有获得源代码的权利并且必须告知他这一点。 这一规定是为防止某些别有用心的人或公司将 GNU 软件稍加修改去申请版权，把它说成是自己的产品，其目的是让 GNU 永远是免费和公开的，这就是 GPL 原则。"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "1w7r09up",
                    "extend": true,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": 1,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linus Torvalds 把 Linux 源代码发布在 Internet 上，就引起了大家的广泛关注，许多程序员自愿对它进行改进并为它开发程序，使得 Linux 迅猛发展，到 1994 年 3 月 14 日，Linux 1.0 终于诞生（代码量 176 350 行），这时它已经是一个功能完备的操作系统了，其内核写得紧凑高效，可以充分发挥硬件的性能，在 4 MB 内存的 80386 机器上表现得非常好。 所以说，Linux 是许多人努力的成果，世界上有成千上万的开发人员对 Linux 做出过贡献，为其增加新功能、修改错误，而且仍不断地改进，Linux 的版本也因此得以不断地更新发展。"
                    }
                  ]
                },
                {
                  "type": "heading",
                  "attrs": {
                    "id": "moejho",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "data-toc-id": "moejho",
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": "",
                    "level": 2
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "2.Linux的特点 "
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "vm9i3wpk",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "源代码开放"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "lu5or3tp",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux 是开放源代码的自由软件的代表，可以从互联网上很方便地免费下载，且由于可以得到 Linux 的源代码，所有操作系统的内部逻辑可见，这样就可以准确地查明故障原因，及时采取相应对策，也使得遍及全球的开发人员都能够在 Linux 内核的基础上加以改良，从而使 Linux 能"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "tykstru4",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "够不断茁壮成长。"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "xhza1lju",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "●   真正的多用户、多任务"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "jc3qeiej",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux 支持多个用户从相同或不同的终端上同时使用同一台计算机，系统资源可以由多个用户拥有并共享使用，各个用户间互不影响。Linux 允许多个程序同时并独立地执行，还可以给紧"
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(255, 255, 255)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "5"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "w9infu2g",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "项目 1"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "m8ao5ufv",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "认识与安装 Linux 操作系统急任务以较高的优先级。"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "r6nm40bq",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "●   完全兼容 POSIX 标准"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "oxrx9rno",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux 完全符合 POSIX（Portable Operating System Interface of UNIX，面向 UNIX 的可移植操作系统）标准，该标准定义了应用程序和操作系统之间的接口标准，其目的是提高应用程序的可移植性，即用于保证编制的应用程序的源代码可以在重新编译以后移植到任何符合 POSIX标准的其他操作系统上运行。"
                    }
                  ]
                },
                {
                  "type": "video",
                  "attrs": {
                    "id": "goa3l988",
                    "extend": false,
                    "class": "bellCss",
                    "nodeAlign": "center",
                    "margin": {},
                    "nodeFloat": "unset",
                    "vnode": true,
                    "file": null,
                    "name": "图片标题",
                    "size": 3001552,
                    "src": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1739936456176.mp4",
                    "width": 0,
                    "height": 400,
                    "draggable": false,
                    "uploaded": true,
                    "previewType": "video",
                    "duration": null
                  }
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "vzlpl3u1",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "●   强大的硬件平台可移植性"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "px0eopwm",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "硬件平台可移植性是指只需修改操作系统底层的少量代码，便可从一种硬件平台转移到另一种硬件平台后仍然按其自身方式运行的能力。Linux 不仅能在掌上电脑、笔记本电脑、PC、工作站或巨型机等各种通用计算机上运行，而且能运行于智能手机、智能电视机、网络通信设备和物联网终端等各种智能设备，Linux 是迄今为止运行硬件平台最多的操作系统。"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "mol8oke6",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "●   丰富的网络功能Linux 在通信和网络功能方面优于其他操作系统，为用户提供了完善的、强大的网络功能。"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "39wy2nn2",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux 不仅能够作为网络工作站使用，还可以作为各类服务器实现各种网络服务，如基于 IP 包过滤的防火墙、路由器、代理服务器、文件服务器、打印服务器、Web、FTP、DNS、DHCP、E-mail 服务器等。"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "ci11jyhh",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "●   良好的用户界面"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "p3p7dlbg",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux 提供了字符和图形两种用户界面。 在字符界面下，用户通过键盘输入相应的指令进行操作，字符界面占用的系统资源较少，运行速度和性能较高。 图形界面是用户利用鼠标和键盘对图形化的菜单、窗口、对话框等元素进行操作来完成相应作业， Linux 给用户提供了一个类似于 Windows 系统的直观、易操作、交互性强的友好的图形化界面。"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "yb10jskk",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "●   可靠的系统安全"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "dzgbla91",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux 采取了许多安全技术措施，包括对读 / 写进行权限控制、带保护的子系统、审计跟踪、核心授权、细力度或者基于角色的安全访问控制、文件级或文件系统级加密功能、与 TPM 等硬件安全技术的结合、桌面级个人防火墙、集成电子签名、电子印章等功能，这为网络多用户环境中的用户提供了必要的安全保障。"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "yy4x70qg",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "●   高度的稳定性"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "r6lxwu1q",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux 承袭了 UNIX 的优良特性，具有健壮的基础架构。Linux 的基础架构由相互无关的层组成，每层都有特定的功能和严格的权限许可，从而保证最大限度上的稳定运行，可以连续运行数月、数年而无须重新启动。"
                    }
                  ]
                }
              ]
            },
            {
              "type": "page",
              "attrs": {
                "id": "7gfyu3ry",
                "extend": false,
                "class": "bellCss",
                "HTMLAttributes": null,
                "pageNumber": 4,
                "force": true,
                "slots": {},
                "pagesPosition": "bottom",
                "pagesAlign": "center",
                "pagesShow": true,
                "color": "#333"
              },
              "content": [
                {
                  "type": "heading",
                  "attrs": {
                    "id": "tyj35p",
                    "extend": true,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "data-toc-id": "tyj35p",
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": "",
                    "level": 1
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正品尚黑简体",
                            "fontSize": "11.5pt",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "1.2.2 Linux系统的组成"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "1p7krwue",
                    "extend": true,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux 系统由四个部分组成： Linux 内核、Linux 文件系统、"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "wbq272ah",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux Shell 和 Linux 应用程序如图 1- 2 所示。"
                    }
                  ]
                },
                {
                  "type": "imageLayout",
                  "attrs": {
                    "id": "vr1j2emp",
                    "extend": false,
                    "class": "bellCss",
                    "nodeAlign": "center",
                    "vnode": true,
                    "type": "image/png",
                    "name": "图1-2 Linux系统的组成",
                    "size": 14591,
                    "file": null,
                    "src": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1739878249792.png",
                    "content": null,
                    "width": "319.00",
                    "height": 200,
                    "left": 0,
                    "top": 0,
                    "angle": null,
                    "draggable": false,
                    "rotatable": false,
                    "equalProportion": true,
                    "flipX": false,
                    "flipY": false,
                    "uploaded": true,
                    "error": false,
                    "previewType": "imageLayout",
                    "imageTitle": "图片标题",
                    "linkAddress": "",
                    "isShowNo": "1",
                    "isShowImageTitle": "1"
                  }
                },
                {
                  "type": "heading",
                  "attrs": {
                    "id": "jsw0vdvx",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "data-toc-id": "l274hh",
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": "",
                    "level": 2
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "1.Linux内核"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "vftul06y",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        },
                        {
                          "type": "inputCommon",
                          "attrs": {
                            "vnode": true,
                            "inputCommon": {
                              "id": 1739882511052
                            }
                          }
                        }
                      ],
                      "text": "内核（kernel）"
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "是 Linux 最核心的部分，是系统的“心脏”，"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "f0us4l4u",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "管理着整个计算机系统的软硬件资源和所有应用程序的执行。"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "uaflwscf",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "内核的主要模块有：CPU 和进程管理、文件信息管理、存储管"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "8se1m43j",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "理、设备管理和驱动、网络通信、系统初始化（引导）和系统调用等。"
                    }
                  ]
                },
                {
                  "type": "heading",
                  "attrs": {
                    "id": "aq84h9",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "data-toc-id": "aq84h9",
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": "",
                    "level": 2
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "2.Linux文件系统"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "dk6okgzn",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "文件系统是操作系统中负责存取和管理信息的模块。Linux 文件系统是文"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "4qnwp709",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "件存放在磁盘等存储设备上的组织方法，主要体现在对文件和目录的组织上。"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "s1t5jdr3",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux 支 持 多 种 文 件 系 统， 如 xfs、ext 4、ReiserFS、JFS、ISO 9660、FAT 32、NFS 等，通过安装 ntfs- 3g 驱动程序，Linux 也能支持对 NTFS 文件系统的读写操作。"
                    }
                  ]
                },
                {
                  "type": "resourceCover",
                  "attrs": {
                    "id": "yhiwbce7",
                    "extend": false,
                    "class": "bellCss",
                    "vnode": true,
                    "rcType": 1,
                    "bgSrc": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1738917920012.png",
                    "url": "https://blog.csdn.net/weixin_52352417/article/details/143038288/?bgColor=000000",
                    "title": "Linux硬件AR",
                    "showTitle": true,
                    "width": 100,
                    "file": null,
                    "bgColor": "#000000",
                    "readingContent": "",
                    "filename": "",
                    "size": 0
                  }
                },
                {
                  "type": "heading",
                  "attrs": {
                    "id": "b6oy80ot",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "data-toc-id": "azg6ra",
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": "",
                    "level": 2
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "3.Linux Shell"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "3a93j6bq",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Linux 的内核并不能直接接受来自终端的用户命令，即不能直接与用户进行交互操作，而是需要 Shell（外壳）来充当用户和内核之间的桥梁。Shell 是系统的用户界面，提供了用户与内核Linux 系统的组成项目 1认识与安装 Linux 操作系统进行交互操作的一种接口，它接收用户输入的命令并把它送入内核去执行。Shell 分为以下两类："
                    }
                  ]
                },
                {
                  "type": "codeBlock",
                  "attrs": {
                    "id": "h02nvvop",
                    "extend": false,
                    "class": "bellCss",
                    "margin": {},
                    "vnode": true,
                    "language": "python",
                    "prismLanguage": "html",
                    "theme": "light",
                    "lineNumbers": true,
                    "wordWrap": false
                  }
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "5o3tv3e8",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "●图形界面 Shell：提供一个图形使用者接口（GUI）。 目前，应用最为广泛的图形界面是Windows Explorer（微软的 Windows 系列制作系统），还有广为人知的 Linux Shell，其中 Linux Shell包括 X Window manager（BlackBox 和 FluxBox），以及功能更强大的 CDE、GNOME、KDE、XFCE等图形桌面环境。"
                    }
                  ]
                },
                {
                  "type": "audio",
                  "attrs": {
                    "id": "4p1czej5",
                    "extend": false,
                    "class": "bellCss",
                    "nodeAlign": "center",
                    "margin": {},
                    "nodeFloat": "unset",
                    "vnode": true,
                    "file": null,
                    "name": "关于Linux的硬件要求语音解答",
                    "size": 254194,
                    "src": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1739881430773.mp3",
                    "uploaded": true,
                    "isShowTitle": true,
                    "previewType": "audio",
                    "duration": 1
                  }
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "t41x8vl2",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "●字符界面 Shell：字符界面 Shell 不仅是一个命令解释程序，而且还是一种程序设计语言，它允许用户编写由 Shell 命令组成的程序。 用这种编程语言编写的 Shell 程序与其他应用程序具有同样的效果。 基于字符界面的 Shell 主要有 Bourne Again Shell、C Shell 等。内核、文件系统和 Shell 一起形成了基本的操作系统结构，它们使用户可以运行程序、管理文件以及使用系统。"
                    }
                  ]
                }
              ]
            },
            {
              "type": "page",
              "attrs": {
                "id": "oa86e1ot",
                "extend": false,
                "class": "bellCss",
                "HTMLAttributes": null,
                "pageNumber": 5,
                "force": true,
                "slots": {},
                "pagesPosition": "bottom",
                "pagesAlign": "center",
                "pagesShow": true,
                "color": "#333"
              },
              "content": [
                {
                  "type": "heading",
                  "attrs": {
                    "id": "8jnxdc",
                    "extend": true,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "data-toc-id": "8jnxdc",
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": "",
                    "level": 1
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正品尚黑简体",
                            "fontSize": "11.5pt",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "1.2.3 Red Hat Enterprise Linux 8/CentOS 8简介"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "1ga33pw9",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": 1,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "Red Hat 公司发行的各 Linux 版本是目前世界上使用最广泛、商业运作最成功的 Linux 发行版本。2003 年"
                    },
                    {
                      "type": "bubbleInline",
                      "attrs": {
                        "id": "fouf0bc0",
                        "extend": false,
                        "class": "bellCss",
                        "vnode": true,
                        "bubbleIcon": "",
                        "bubbleTitle": "",
                        "bubbleContent": "安装Linux的硬件要求",
                        "bubbleUrl": "",
                        "bubbleType": "2"
                      }
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "，在 Red Hat Linux 9 发布之后，公司对其产品线进行了大力度的改革。Red Hat公司不再开发桌面版的发行套件，而将全部力量集中在企业版即 Red Hat Enterprise Linux 的开发上。 原有的桌面版 Red Hat Linux 发行套件则与来自民间的 Fedora 合并，成为 Fedora 发行版本"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "6z7xtyq8",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "（称为社区版）。 企业版的发展历史见表 1- 1。"
                    }
                  ]
                },
                {
                  "type": "tablePlus",
                  "attrs": {
                    "id": "sxi2i8nf",
                    "name": "表格 1-1",
                    "isShowTitle": true,
                    "isTitlePosition": false
                  },
                  "content": [
                    {
                      "type": "table",
                      "attrs": {
                        "id": "s03nighf",
                        "extend": false,
                        "class": "bellCss",
                        "margin": {}
                      },
                      "content": [
                        {
                          "type": "tableRow",
                          "attrs": {
                            "id": "08ppqenu",
                            "extend": false,
                            "class": "bellCss"
                          },
                          "content": [
                            {
                              "type": "tableHeader",
                              "attrs": {
                                "id": "3getugdz",
                                "extend": false,
                                "class": "bellCss",
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": "#DAE8F4",
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "ls584yrt",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "方正书宋_GBK",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "发行版本（"
                                    },
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "Times New Roman",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "RHEL / CentOS"
                                    },
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "方正书宋_GBK",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "）"
                                    }
                                  ]
                                }
                              ]
                            },
                            {
                              "type": "tableHeader",
                              "attrs": {
                                "id": "1cba8t7p",
                                "extend": false,
                                "class": "bellCss",
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": "#DAE8F4",
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "0lq02nm2",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "方正书宋_GBK",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "发布日期（"
                                    },
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "Times New Roman",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "RHEL / CentOS"
                                    },
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "方正书宋_GBK",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "）"
                                    }
                                  ]
                                }
                              ]
                            },
                            {
                              "type": "tableHeader",
                              "attrs": {
                                "id": "kuhxq8ap",
                                "extend": false,
                                "class": "bellCss",
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": "#DAE8F4",
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "fuqgcymn",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "方正书宋_GBK",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "内核版本"
                                    }
                                  ]
                                }
                              ]
                            }
                          ]
                        },
                        {
                          "type": "tableRow",
                          "attrs": {
                            "id": "8f8z7u3r",
                            "extend": false,
                            "class": "bellCss"
                          },
                          "content": [
                            {
                              "type": "tableCell",
                              "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": null,
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "sflm4kg1",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "Times New Roman",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "Red Hat Linux 6.2E"
                                    }
                                  ]
                                }
                              ]
                            },
                            {
                              "type": "tableCell",
                              "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": null,
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "p3cjwint",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "Times New Roman",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "2000-03-27"
                                    }
                                  ]
                                }
                              ]
                            },
                            {
                              "type": "tableCell",
                              "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": null,
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "uvlerzqh",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": null,
                                            "fontSize": "12px",
                                            "color": null,
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "2.2.14"
                                    }
                                  ]
                                }
                              ]
                            }
                          ]
                        },
                        {
                          "type": "tableRow",
                          "attrs": {
                            "id": "d6lyzpsz",
                            "extend": false,
                            "class": "bellCss"
                          },
                          "content": [
                            {
                              "type": "tableCell",
                              "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": null,
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "qr24dhjw",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "Times New Roman",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "Red Hat Linux 6.2E"
                                    }
                                  ]
                                }
                              ]
                            },
                            {
                              "type": "tableCell",
                              "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": null,
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "8xhq3udg",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "Times New Roman",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "2000-03-27"
                                    }
                                  ]
                                }
                              ]
                            },
                            {
                              "type": "tableCell",
                              "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": null,
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "atc7gv2m",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "",
                                            "fontSize": "12px",
                                            "color": "",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "2.2.14"
                                    }
                                  ]
                                }
                              ]
                            }
                          ]
                        },
                        {
                          "type": "tableRow",
                          "attrs": {
                            "id": "jovosfr4",
                            "extend": false,
                            "class": "bellCss"
                          },
                          "content": [
                            {
                              "type": "tableCell",
                              "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": null,
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "z7fivwds",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "Times New Roman",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "Red Hat Linux 6.2E"
                                    }
                                  ]
                                }
                              ]
                            },
                            {
                              "type": "tableCell",
                              "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": null,
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "7vwz9p4n",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "Times New Roman",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "2000-03-27"
                                    }
                                  ]
                                }
                              ]
                            },
                            {
                              "type": "tableCell",
                              "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": null,
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "pp9vzorg",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "",
                                            "fontSize": "12px",
                                            "color": "",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "2.2.14"
                                    }
                                  ]
                                }
                              ]
                            }
                          ]
                        },
                        {
                          "type": "tableRow",
                          "attrs": {
                            "id": "oi2i66qn",
                            "extend": false,
                            "class": "bellCss"
                          },
                          "content": [
                            {
                              "type": "tableCell",
                              "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": null,
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "72k3q8bj",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "Times New Roman",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "Red Hat Linux 6.2E"
                                    }
                                  ]
                                }
                              ]
                            },
                            {
                              "type": "tableCell",
                              "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": null,
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "w802wiar",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "Times New Roman",
                                            "fontSize": "12px",
                                            "color": "rgb(35, 31, 32)",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "2000-03-27"
                                    }
                                  ]
                                }
                              ]
                            },
                            {
                              "type": "tableCell",
                              "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null,
                                "align": null,
                                "backgroundColor": null,
                                "borderColor": null,
                                "borderStyle": null
                              },
                              "content": [
                                {
                                  "type": "paragraph",
                                  "attrs": {
                                    "id": "24vpciii",
                                    "extend": false,
                                    "class": "bellCss",
                                    "columnCount": 0,
                                    "indent": null,
                                    "textAlign": "left",
                                    "lineHeight": 1,
                                    "margin": {},
                                    "backgroundImage": "",
                                    "containerColor": "",
                                    "backgroundSize": "",
                                    "backgroundBorder": ""
                                  },
                                  "content": [
                                    {
                                      "type": "text",
                                      "marks": [
                                        {
                                          "type": "textStyle",
                                          "attrs": {
                                            "fontFamily": "",
                                            "fontSize": "12px",
                                            "color": "",
                                            "letterSpacing": "0px"
                                          }
                                        }
                                      ],
                                      "text": "2.2.14"
                                    }
                                  ]
                                }
                              ]
                            }
                          ]
                        }
                      ]
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "ft547jiu",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "#000",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "        Red Hat Enterprise Linux 8（简称 RHEL 8）是 Red Hat 企业的第七次重要版本发布，其内核由3. 10. 0 升级为 4. 18. 0。 该版本为支持客户工作负载的混合云部署提供了一个稳定、安全和良好的基础。 与旧版本的不同主要有：支持最多 4 PB 的物理内存（RHEL 7 为 64 TB）、软件仓库增加到两个（BaseOS 和 AppStream）、包管理系统 yum 基于 dnf、停止支持 KDE、默认集成 Cockpit 使用户能通过 Web 控制台管理和监控系统的运行状况、MySQL 数据库重新回归、安全方面用 nftables取代 iptables 等。"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "5pwyby4u",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": 1,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "#000",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "CentOS（Community Enterprise Operating，社区企业操作系统）是由社群支持的基于 RHEL的源代码进行再编译后得到的一个派生的发行版本（修复了 RHEL 很多已知的漏洞），全免费、可自由派发、功能上完全兼容 RHEL 是 CentOS 的主要特征。 见3D模型 1-1"
                    }
                  ]
                },
                {
                  "type": "resourceCover",
                  "attrs": {
                    "id": "y8vsi0lb",
                    "extend": false,
                    "class": "bellCss",
                    "vnode": true,
                    "rcType": 0,
                    "bgSrc": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1739758149941.png",
                    "url": "https://preview.bell-lab.com/onlinePreview?url=aHR0cHM6Ly9wcmV2aWV3LmJlbGwtbGFiLmNvbS9vbmxpbmVQcmV2aWV3P3VybD1hSFIwY0RvdkwyUjFkSEF0ZEdWemRDNXZjM010WTI0dFltVnBhbWx1Wnk1aGJHbDVkVzVqY3k1amIyMHZNVGN6T1RnM09UQTNNVGs1T1M1bVluZyUzRA%3D%3D",
                    "title": "3D模型   1-1",
                    "showTitle": true,
                    "width": 100,
                    "file": null,
                    "bgColor": "#ffffff",
                    "readingContent": "",
                    "filename": "test.fbx",
                    "size": 244
                  }
                }
              ]
            },
            {
              "type": "page",
              "attrs": {
                "id": "ryeb8pd6",
                "extend": false,
                "class": "bellCss",
                "HTMLAttributes": null,
                "pageNumber": 6,
                "force": true,
                "slots": {},
                "pagesPosition": "bottom",
                "pagesAlign": "center",
                "pagesShow": true,
                "color": "#333"
              },
              "content": [
                {
                  "type": "heading",
                  "attrs": {
                    "id": "hecnr1",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "data-toc-id": "hecnr1",
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": "",
                    "level": 2
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正品尚黑简体",
                            "fontSize": "11.5pt",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "1. RHEL 8的安装准备"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "1rg2cyba",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "1.硬件的基本要求"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "pzy7m313",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": 1,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "在安装 Linux 之前，应先确定计算机的硬件是否能被 Linux 所支持。 随着 Linux 内核的不断完善， RHEL 8 支持几乎所有的处理器（CPU）和大部分的主流硬件。 现在，各大硬件厂商纷纷针对 Linux 推出了驱动程序和补丁，使 Linux 在硬件驱动上获得了更广泛的支持，最新的硬件支持列表可以在 "
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "link",
                          "attrs": {
                            "href": "http://hardware.redhat.com/hcl/",
                            "target": "_blank",
                            "rel": "noopener noreferrer nofollow",
                            "class": null
                          }
                        },
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "http://hardware.redhat.com/hcl/"
                    },
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": " 上查到。 图形界面的 RHEL 8 要求系统至少有 512 MB 内存，为方便用户可选择性使用多种应用程序，通常建议使用 20 GB以上硬盘。"
                    }
                  ]
                },
                {
                  "type": "resourceCover",
                  "attrs": {
                    "id": "gpx28za5",
                    "extend": false,
                    "class": "bellCss",
                    "vnode": true,
                    "rcType": 7,
                    "bgSrc": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1738917920012.png",
                    "url": "https://blog.csdn.net/weixin_52352417/article/details/143038288",
                    "title": "Linux系统的硬件要求",
                    "showTitle": true,
                    "width": 100,
                    "file": null,
                    "bgColor": "#ffffff",
                    "readingContent": "",
                    "filename": "",
                    "size": 0
                  }
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "i6rmknc1",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "2.多重引导"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "4ut9auf1",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "用户可以在计算机上仅安装 Linux，也可在已安装有其他操作系统的硬盘上增加安装 Linux，使 Linux 与其他操作系统（如：Windows、Mac 等）在计算机上并存，且相互独立。 在计算机开启时，用户可选择启动不同的操作系统，即 Linux 可支持多重引导。"
                    }
                  ]
                },
                {
                  "type": "resourceCover",
                  "attrs": {
                    "id": "ykcgnl7q",
                    "extend": false,
                    "class": "bellCss",
                    "vnode": true,
                    "rcType": 5,
                    "bgSrc": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1738917920012.png",
                    "url": "https://preview.bell-lab.com/onlinePreview?url=aHR0cHM6Ly9wcmV2aWV3LmJlbGwtbGFiLmNvbS9vbmxpbmVQcmV2aWV3P3VybD1hSFIwY0RvdkwyUjFkSEF0ZEdWemRDNXZjM010WTI0dFltVnBhbWx1Wnk1aGJHbDVkVzVqY3k1amIyMHZNVGN6T1RnM09UTTROemM1Tmk1d2NIUSUzRA%3D%3D",
                    "title": "教学资源 1-1",
                    "showTitle": true,
                    "width": 100,
                    "file": null,
                    "bgColor": "#ffffff",
                    "readingContent": "",
                    "filename": "新建 PPT 演示文稿.ppt",
                    "size": 20992
                  }
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "wopcs6jb",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "3.选择安装方式"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "5mbych3o",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "Microsoft Yahei",
                            "fontSize": "16px",
                            "color": "rgb(35, 31, 32)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "RHEL 8 支持的安装方式包括光盘 /U 盘安装、硬盘安装、NFS 映像安装、FTP 安装和 HTTP安装等。 光盘 /U 盘和硬盘安装属于本地安装，NFS、FTP 和 HTTP 安装属于网络安装。 此外，借助 PXE+Kickstart 或 Cobbler 工具可实现自动化、批量化安装。 图 1-3"
                    }
                  ]
                },
                {
                  "type": "imageLayout",
                  "attrs": {
                    "id": "ue5t02m6",
                    "extend": false,
                    "class": "bellCss",
                    "nodeAlign": "center",
                    "vnode": true,
                    "type": "image/webp",
                    "name": "图1-3",
                    "size": 9564,
                    "file": null,
                    "src": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1739879572808.webp",
                    "content": null,
                    "width": "333.00",
                    "height": 200,
                    "left": 0,
                    "top": 0,
                    "angle": null,
                    "draggable": false,
                    "rotatable": false,
                    "equalProportion": true,
                    "flipX": false,
                    "flipY": false,
                    "uploaded": true,
                    "error": false,
                    "previewType": "imageLayout",
                    "imageTitle": "图片标题",
                    "linkAddress": "",
                    "isShowNo": "1",
                    "isShowImageTitle": "1"
                  }
                }
              ]
            },
            {
              "type": "page",
              "attrs": {
                "id": "b4z9tmbl",
                "extend": false,
                "class": "bellCss",
                "HTMLAttributes": null,
                "pageNumber": 7,
                "force": true,
                "slots": {},
                "pagesPosition": "bottom",
                "pagesAlign": "center",
                "pagesShow": true,
                "color": "#333"
              },
              "content": [
                {
                  "type": "heading",
                  "attrs": {
                    "id": "fj3jc7",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "data-toc-id": "fj3jc7",
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": "",
                    "level": 2
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正品尚黑简体",
                            "fontSize": "11.5pt",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "2. 国产操作系统获重大突破"
                    }
                  ]
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "4ei7hvck",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": 1,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "text": "国产操作系统起步于国家“七五”计划期间，但“缺芯少魂”一直是中国信息产业发展的Linux 网络操作系统配置与管理 10软肋，其中“魂”指的就是操作系统。 操作系统是包括计算机在内的所有智能设备的灵魂，也是信息产业的基础和根基。 中国作为发展中的大国，只有掌握操作系统技术与生态，才能在未来的发展之路上不受制于人。 从全球范围来看，谋求“操作系统自主权”已经成为各国不约而同的选择。 韩国准备在 2026 年前，将其政府的操作系统转向开源国产系统；德国、瑞士、巴西、荷兰等国家也都有类似的计划，目的都是降低成本、预防风险。鉴于以上情况，国务院于 2006 年发布了《国家中长期科学和技术发展规划纲要 ( 2006—2020 年 )》, 对“核高基”、载人航天、探月工程等 16 个重大科技专项的发展进行了规划和部署。 其中“核高基”是“核心电子器件、高端通用芯片及基础软件产品”的简称，其主要建设目标是：在芯片、软件和电子器件领域，追赶国际技术和产业的迅速发展，通过持续创新，攻克一批关键技术、研发一批战略核心产品。2009 年 11 月，工信部发布了专门针对“核高基”科技重大专项 2009 年课题申报通知，其中基础软件部分包括 6 个项目、20 个子课题。 这是我国基础软件在经历了将近 20 年的艰难发展之后，作为“十一五规划”中的首个课题被正式推上快速发展的特殊通道。“核高基”专项的适时出现，犹如助推器，给了基础软件更强劲的发展支持量。"
                    }
                  ]
                },
                {
                  "type": "resourceCover",
                  "attrs": {
                    "id": "xaychxwb",
                    "extend": false,
                    "class": "bellCss",
                    "vnode": true,
                    "rcType": 6,
                    "bgSrc": "http://dutp-test.oss-cn-beijing.aliyuncs.com/1738917920012.png",
                    "url": "",
                    "title": "拓展阅读",
                    "showTitle": true,
                    "width": 100,
                    "file": null,
                    "bgColor": "#ffffff",
                    "readingContent": " &nbsp; &nbsp; &nbsp; 破性进展。 红旗 Linux、深度、银河麒麟、中科方德等操作系统技术更加成熟，统信 UOS、华为鸿蒙、欧拉、阿里龙蜥等国产操作系统相继面世，呈现一片欣欣向荣的景象。 尤其是华为发布的面向智能手机、平板及电脑等终端设备、深入消费者个人及家庭应用场景的鸿蒙系统，已获得超过 2. 2 亿用户的青睐。 国产操作系统已逐步摆脱缺技术、少生态的困境，并且前所未有地获得了规模化应用和市场认可。",
                    "filename": "",
                    "size": 0
                  }
                },
                {
                  "type": "paragraph",
                  "attrs": {
                    "id": "3tpzdcxf",
                    "extend": false,
                    "class": "bellCss indent-1",
                    "columnCount": 0,
                    "indent": 1,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": ""
                  },
                  "content": [
                    {
                      "type": "text",
                      "text": "经过 15 年“核高基”专项的持续建设和推进，国产基础软件的发展形势已明显好转，近期在国内、外环境的共同作用下，国产操作系统发展无论是在技术层面还是市场层面均已取得突"
                    }
                  ]
                }
              ]
            },
            {
              "type": "page",
              "attrs": {
                "id": "2tdpnseb",
                "extend": false,
                "class": "bellCss",
                "HTMLAttributes": null,
                "pageNumber": 8,
                "force": true,
                "slots": {},
                "pagesPosition": "bottom",
                "pagesAlign": "center",
                "pagesShow": true,
                "color": "#333"
              },
              "content": [
                {
                  "type": "heading",
                  "attrs": {
                    "id": "9vc9w4",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "data-toc-id": "9vc9w4",
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": "",
                    "level": 1
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正品尚黑简体",
                            "fontSize": "11.5pt",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "课后作业"
                    }
                  ]
                },
                {
                  "type": "heading",
                  "attrs": {
                    "id": "8isinv",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": "left",
                    "lineHeight": 1,
                    "margin": {},
                    "data-toc-id": "8isinv",
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": "",
                    "level": 2
                  },
                  "content": [
                    {
                      "type": "text",
                      "marks": [
                        {
                          "type": "textStyle",
                          "attrs": {
                            "fontFamily": "方正品尚黑简体",
                            "fontSize": "11.5pt",
                            "color": "rgb(0, 174, 239)",
                            "letterSpacing": "0px"
                          }
                        }
                      ],
                      "text": "任务1-1"
                    }
                  ]
                },
                {
                  "type": "questions",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "width": null,
                    "height": 200,
                    "collapsed": true,
                    "questionTitle": ""
                  }
                },
                {
                  "type": "questions",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "width": null,
                    "height": 200,
                    "collapsed": true,
                    "questionsList": [
                      {
                        "bookId": "1890288984567123970",
                        "bookQuestionId": "1891832324085297156",
                        "chapterId": "1890291803311378434",
                        "folderId": "0",
                        "questionType": 5,
                        "userQuestionId": "1891832323955273730",
                        "userQuestion": {
                          "questionId": "1891832323955273730",
                          "questionType": 5,
                          "questionContent": "请将以下Linux系统部署中的相关命令与其功能正确连线：",
                          "rightAnswer": "[]",
                          "analysis": "A-b：tar命令用于创建或解压缩.tar文件。在Linux中，tar是一个常用的归档工具，可以将多个文件和目录打包成一个文件，也可以对打包的文件进行解压缩。B-a：ps命令用于显示当前系统中运行的进程信息。通过ps命令，用户可以查看系统中正在运行的进程，以及这些进程的详细信息，如进程ID、运行时间、占用的资源等。C-d：mkdir命令用于创建新的目录。在Linux中，mkdir是一个基本的文件操作命令，通过它可以轻松地在文件系统中创建新的目录。D-c：chmod命令用于修改文件或目录的权限。在Linux中，文件和目录的权限决定了哪些用户可以对它们进行读、写和执行等操作。通过chmod命令，用户可以灵活地设置文件和目录的权限，以确保系统的安全性和稳定性。",
                          "disorder": 1,
                          "sort": 0,
                          "folderId": "0",
                          "options": [
                            {
                              "optionId": "1891832324085297153",
                              "optionContent": "显示当前系统中运行的进程信息",
                              "questionId": "1891832323955273730",
                              "rightFlag": 1,
                              "optionPosition": 2,
                              "sort": 0
                            },
                            {
                              "optionId": "1891832324085297154",
                              "optionContent": "创建新的目录",
                              "questionId": "1891832323955273730",
                              "rightFlag": 1,
                              "optionPosition": 2,
                              "sort": 0
                            },
                            {
                              "optionId": "1891832324022382594",
                              "optionContent": "tar",
                              "questionId": "1891832323955273730",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891832324085297155",
                              "optionContent": "修改文件或目录的权限",
                              "questionId": "1891832323955273730",
                              "rightFlag": 1,
                              "optionPosition": 2,
                              "sort": 0
                            },
                            {
                              "optionId": "1891832324022382595",
                              "optionContent": "ps",
                              "questionId": "1891832323955273730",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891832324022382596",
                              "optionContent": "mkdir",
                              "questionId": "1891832323955273730",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891832324022382597",
                              "optionContent": "chmod",
                              "questionId": "1891832323955273730",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891832324022382598",
                              "optionContent": "创建或解压缩.tar文件",
                              "questionId": "1891832323955273730",
                              "rightFlag": 1,
                              "optionPosition": 2,
                              "sort": 0
                            }
                          ],
                          "codeContent": null,
                          "createSource": null
                        }
                      }
                    ],
                    "questionTitle": ""
                  }
                },
                {
                  "type": "questions",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "width": null,
                    "height": 200,
                    "collapsed": true,
                    "questionsList": [
                      {
                        "bookId": "1890288984567123970",
                        "bookQuestionId": "1891824297856774149",
                        "chapterId": "1890291803311378434",
                        "folderId": "0",
                        "questionType": 4,
                        "userQuestionId": "1891824297789665281",
                        "userQuestion": {
                          "questionId": "1891824297789665281",
                          "questionType": 4,
                          "questionContent": "请将以下Linux系统部署MySQL数据库服务器的步骤按正确的顺序排列：",
                          "rightAnswer": "",
                          "analysis": "<strong>正确答案</strong>：A, C, D, E, B（或A, D, C, E, B，但通常建议先安装软件再启动服务）在Linux系统中部署MySQL数据库服务器时，通常需要按照以下步骤进行：安装MySQL数据库软件（A）：这是部署过程的第一步，你需要从官方仓库或通过其他方式获取MySQL的安装包，并按照说明进行安装。启动MySQL服务（C）：安装完成后，你需要启动MySQL服务以使其开始运行。在某些情况下，安装过程可能会自动启动服务，但这取决于具体的安装方式和系统配置。设置MySQL的root用户密码（D）：在MySQL服务启动后，你通常需要设置root用户的密码以确保数据库的安全性。这是通过MySQL的命令行客户端或其他管理工具完成的。创建数据库和用户并分配权限（E）：一旦root用户密码设置完成，你就可以根据需要创建数据库、用户，并为这些用户分配适当的权限。这是数据库管理的重要部分，确保只有授权用户才能访问和操作数据库。配置MySQL服务为系统启动时自动运行（B）：最后，你可能希望配置MySQL服务在系统启动时自动运行，这样你就不需要在每次系统重启后手动启动服务了。这通常是通过系统的服务管理器（如systemd或SysVinit）来完成的。需要注意的是，虽然上述步骤是按照一定的逻辑顺序排列的，但在某些情况下，步骤的顺序可能会有所不同。例如，有些系统可能在安装MySQL时要求你设置root密码，或者在启动服务之前进行其他配置。然而，从一般性的角度来看，上述步骤提供了一个合理的、可遵循的部署流程。另外，虽然答案中给出了A, C, D, E, B的顺序，但在某些情况下，你可能会选择先设置root密码（D）再启动服务（C），特别是如果你担心在安装后立即启动服务可能会暴露未受保护的root账户。然而，在大多数情况下，按照A, C, D, E, B的顺序进行部署是合理且有效的。",
                          "disorder": 1,
                          "sort": 0,
                          "folderId": "0",
                          "options": [
                            {
                              "optionId": "1891824297856774145",
                              "optionContent": "配置MySQL服务为系统启动时自动运行",
                              "questionId": "1891824297789665281",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891824297789665282",
                              "optionContent": "安装MySQL数据库软件",
                              "questionId": "1891824297789665281",
                              "rightFlag": 0,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891824297856774146",
                              "optionContent": "启动MySQL服务",
                              "questionId": "1891824297789665281",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891824297856774147",
                              "optionContent": "设置MySQL的root用户密码",
                              "questionId": "1891824297789665281",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891824297856774148",
                              "optionContent": "创建数据库和用户并分配权限",
                              "questionId": "1891824297789665281",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            }
                          ],
                          "codeContent": null,
                          "createSource": null
                        }
                      }
                    ],
                    "questionTitle": ""
                  }
                },
                {
                  "type": "questions",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "width": null,
                    "height": 200,
                    "collapsed": true,
                    "questionsList": [
                      {
                        "bookId": "1890288984567123970",
                        "bookQuestionId": "1891823646955319299",
                        "chapterId": "1890291803311378434",
                        "folderId": "0",
                        "questionType": 7,
                        "userQuestionId": "1891823646888210434",
                        "userQuestion": {
                          "questionId": "1891823646888210434",
                          "questionType": 7,
                          "questionContent": "在Linux系统中部署MySQL数据库服务器时，必须手动创建数据库用户和分配权限。",
                          "rightAnswer": "",
                          "analysis": "在Linux系统中部署MySQL数据库服务器时，虽然手动创建数据库用户和分配权限是一种常见的做法，但并不是必须的。MySQL提供了多种方式来管理用户和权限，包括通过命令行工具（如mysql）、图形化工具（如phpMyAdmin、MySQL Workbench）或配置文件。实际上，在安装MySQL时，通常会创建一个默认的root用户，并允许你通过初次登录来设置root用户的密码。之后，你可以使用root用户来创建其他数据库用户，并分配适当的权限。但是，这并不是说必须手动执行这些步骤；在某些情况下，自动化脚本或配置管理工具可能被用来完成这些任务。此外，一些Linux发行版的MySQL安装包可能还包含了预设的数据库用户和权限配置，这些配置可以在安装过程中或之后进行调整。因此，判断题中的说法“必须手动创建数据库用户和分配权限”是不准确的。正确的做法是根据具体需求和部署环境来选择合适的方法来管理MySQL数据库的用户和权限。",
                          "disorder": 1,
                          "sort": 0,
                          "folderId": "0",
                          "options": [
                            {
                              "optionId": "1891823646955319297",
                              "optionContent": "正确",
                              "questionId": "1891823646888210434",
                              "rightFlag": 0,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891823646955319298",
                              "optionContent": "错误",
                              "questionId": "1891823646888210434",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            }
                          ],
                          "codeContent": null,
                          "createSource": null
                        }
                      }
                    ],
                    "questionTitle": ""
                  }
                },
                {
                  "type": "questions",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "width": null,
                    "height": 200,
                    "collapsed": true,
                    "questionsList": [
                      {
                        "bookId": "1890288984567123970",
                        "bookQuestionId": "1891823363902713860",
                        "chapterId": "1890291803311378434",
                        "folderId": "0",
                        "questionType": 3,
                        "userQuestionId": "1891823363902713858",
                        "userQuestion": {
                          "questionId": "1891823363902713858",
                          "questionType": 3,
                          "questionContent": "在Linux系统中部署Web服务器时，通常需要###配置文件###来定义Web服务器的根目录和端口号等关键参数。",
                          "rightAnswer": "",
                          "analysis": "在Linux系统中部署Web服务器时，配置文件的编辑是至关重要的一步。这些配置文件定义了Web服务器的关键参数，包括但不限于：Web服务器的根目录：这是Web服务器存储网页文件的目录。当用户访问Web服务器时，服务器会从该目录中查找并返回相应的网页文件。端口号：Web服务器监听的端口号，通常是80（HTTP）或443（HTTPS）。用户通过浏览器访问Web服务器时，需要在URL中指定这个端口号（尽管在大多数情况下，浏览器会自动使用默认的HTTP或HTTPS端口）。其他配置：如访问控制、日志记录、性能优化等。对于不同的Web服务器软件，配置文件的名称和位置可能有所不同。例如，Apache HTTP Server通常使用httpd.conf或位于/etc/httpd/conf.d/目录下的多个配置文件；而Nginx则使用nginx.conf文件，并可能包含位于其他目录中的包含文件。因此，在填写这道填空题时，虽然具体的文件名可能因Web服务器软件而异，但“配置文件”是一个一般性的、正确的描述。在实际操作中，你需要根据所使用的Web服务器软件来查找和编辑相应的配置文件。",
                          "disorder": 1,
                          "sort": 0,
                          "folderId": "0",
                          "options": [
                            {
                              "optionId": "1891823363902713859",
                              "optionContent": "",
                              "questionId": "1891823363902713858",
                              "rightFlag": 0,
                              "optionPosition": 1,
                              "sort": 0
                            }
                          ],
                          "codeContent": null,
                          "createSource": null
                        }
                      }
                    ],
                    "questionTitle": ""
                  }
                },
                {
                  "type": "questions",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "width": null,
                    "height": 200,
                    "collapsed": true,
                    "questionsList": [
                      {
                        "bookId": "1890288984567123970",
                        "bookQuestionId": "1891822319164502022",
                        "chapterId": "1890291803311378434",
                        "folderId": "0",
                        "questionType": 2,
                        "userQuestionId": "1891822319101587457",
                        "userQuestion": {
                          "questionId": "1891822319101587457",
                          "questionType": 2,
                          "questionContent": "在Linux系统中部署一个数据库服务器时，你可能需要考虑以下哪些因素？（多选）",
                          "rightAnswer": "",
                          "analysis": "A. <strong>选择合适的数据库管理系统（DBMS）</strong>：这是部署数据库服务器的第一步。不同的DBMS（如MySQL、PostgreSQL、Oracle等）有不同的特性和性能表现，你需要根据应用需求选择合适的DBMS。B. <strong>配置数据库服务器的内存和CPU资源</strong>：数据库服务器通常需要大量的内存和CPU资源来处理数据查询和事务。因此，在部署时，你需要根据预期的负载和性能要求来配置服务器的硬件资源。C. <strong>设置数据库的备份和恢复策略</strong>：数据是数据库服务器的核心，因此备份和恢复策略至关重要。你需要定期备份数据库，并确保在数据丢失或损坏时能够迅速恢复。D. <strong>确保数据库服务器的安全性</strong>：数据库服务器存储着敏感数据，因此安全性是一个重要考虑因素。你需要实施访问控制，确保只有授权用户才能访问数据库，并考虑使用加密来保护数据传输和存储。E. <strong>安装并配置Web服务器以提供数据库访问的前端界面</strong>：虽然为数据库服务器提供前端访问界面是一个常见的需求，但它并不是部署数据库服务器时的必需步骤。有些数据库服务器可能仅用于后端处理，而不直接与用户交互。因此，这个选项不是部署数据库服务器时必须考虑的因素，而是根据具体需求来决定的。所以，在这个多选题中，E选项不是正确答案。",
                          "disorder": 1,
                          "sort": 0,
                          "folderId": "0",
                          "options": [
                            {
                              "optionId": "1891822319164502017",
                              "optionContent": "选择合适的数据库管理系统（DBMS）",
                              "questionId": "1891822319101587457",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891822319164502018",
                              "optionContent": "配置数据库服务器的内存和CPU资源",
                              "questionId": "1891822319101587457",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891822319164502019",
                              "optionContent": "设置数据库的备份和恢复策略",
                              "questionId": "1891822319101587457",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891822319164502020",
                              "optionContent": "确保数据库服务器的安全性，包括访问控制和加密",
                              "questionId": "1891822319101587457",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891822319164502021",
                              "optionContent": " 安装并配置Web服务器以提供数据库访问的前端界面",
                              "questionId": "1891822319101587457",
                              "rightFlag": 0,
                              "optionPosition": 1,
                              "sort": 0
                            }
                          ],
                          "codeContent": null,
                          "createSource": null
                        }
                      }
                    ],
                    "questionTitle": ""
                  }
                },
                {
                  "type": "questions",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "width": null,
                    "height": 200,
                    "collapsed": true,
                    "questionsList": [
                      {
                        "bookId": "1890288984567123970",
                        "bookQuestionId": "1891821633261580293",
                        "chapterId": "1890291803311378434",
                        "folderId": "0",
                        "questionType": 1,
                        "userQuestionId": "1891821633194471426",
                        "userQuestion": {
                          "questionId": "1891821633194471426",
                          "questionType": 1,
                          "questionContent": "当你首次安装一台Linux服务器并希望配置一个静态IP地址，你应该编辑哪个配置文件？（以CentOS为例）",
                          "rightAnswer": "",
                          "analysis": "在CentOS系统中，网络接口的配置文件通常位于/etc/sysconfig/network-scripts/目录下，文件名格式为ifcfg-&lt;interface&gt;，其中&lt;interface&gt;是网络接口的名称，如eth0、ens33等。要配置静态IP地址，你需要编辑这个配置文件。选项A是Debian/Ubuntu系统中用于网络配置的文件。选项C用于定义主机名和IP地址的映射，不用于配置网络接口。选项D用于定义DNS服务器地址，也不用于配置网络接口。",
                          "disorder": 1,
                          "sort": 0,
                          "folderId": "0",
                          "options": [
                            {
                              "optionId": "1891821633261580290",
                              "optionContent": "/etc/network/interfaces",
                              "questionId": "1891821633194471426",
                              "rightFlag": 0,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891821633261580291",
                              "optionContent": "/etc/sysconfig/network-scripts/ifcfg-&lt;interface&gt;",
                              "questionId": "1891821633194471426",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891821633261580292",
                              "optionContent": "/etc/hosts",
                              "questionId": "1891821633194471426",
                              "rightFlag": 0,
                              "optionPosition": 1,
                              "sort": 0
                            }
                          ],
                          "codeContent": null,
                          "createSource": null
                        }
                      }
                    ],
                    "questionTitle": ""
                  }
                },
                {
                  "type": "questions",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "width": null,
                    "height": 200,
                    "collapsed": true,
                    "questionsList": [
                      {
                        "bookId": "1890288984567123970",
                        "bookQuestionId": "1891821183279869957",
                        "chapterId": "1890291803311378434",
                        "folderId": "0",
                        "questionType": 1,
                        "userQuestionId": "1891821183212761089",
                        "userQuestion": {
                          "questionId": "1891821183212761089",
                          "questionType": 1,
                          "questionContent": "在部署一个Web服务器时，如果你选择了Apache作为你的HTTP服务器，并且希望它在系统启动时自动运行，你应该如何配置？（假设环境为较新的Linux发行版，如CentOS 7  或 Ubuntu 16.04 ）",
                          "rightAnswer": "",
                          "analysis": "在较新的Linux发行版中，Systemd是系统和服务管理器，用于启动和管理系统服务。systemctl enable httpd命令会将Apache（通常服务名为httpd）设置为开机自启动。选项B和C是较老的方法，不适用于所有现代Linux发行版。选项D虽然也用于设置服务自启动，但它是针对SysVinit系统的，而不是Systemd",
                          "disorder": 1,
                          "sort": 0,
                          "folderId": "0",
                          "options": [
                            {
                              "optionId": "1891821183212761090",
                              "optionContent": "使用systemctl enable httpd命令",
                              "questionId": "1891821183212761089",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891821183279869954",
                              "optionContent": "修改/etc/init.d/httpd脚本",
                              "questionId": "1891821183212761089",
                              "rightFlag": 0,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891821183279869955",
                              "optionContent": "在/etc/rc.local文件中添加启动命令",
                              "questionId": "1891821183212761089",
                              "rightFlag": 0,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891821183279869956",
                              "optionContent": "使用chkconfig httpd on命令",
                              "questionId": "1891821183212761089",
                              "rightFlag": 0,
                              "optionPosition": 1,
                              "sort": 0
                            }
                          ],
                          "codeContent": null,
                          "createSource": null
                        }
                      }
                    ],
                    "questionTitle": ""
                  }
                },
                {
                  "type": "questions",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "width": null,
                    "height": 200,
                    "collapsed": true,
                    "questionsList": [
                      {
                        "bookId": "1890288984567123970",
                        "bookQuestionId": "1891820663479775234",
                        "chapterId": "1890291803311378434",
                        "folderId": "0",
                        "questionType": 1,
                        "userQuestionId": "1891820663286837250",
                        "userQuestion": {
                          "questionId": "1891820663286837250",
                          "questionType": 1,
                          "questionContent": "在Linux系统中，如果你打算使用YUM包管理器来安装一个新的软件包，你通常需要以下哪个命令？",
                          "rightAnswer": "",
                          "analysis": "yum search &lt;package_name&gt;用于搜索软件包，yum update &lt;package_name&gt;用于更新软件包，yum remove &lt;package_name&gt;用于删除软件包。而yum install &lt;package_name&gt;是用于安装新软件包的正确命令。",
                          "disorder": 1,
                          "sort": 0,
                          "folderId": "0",
                          "options": [
                            {
                              "optionId": "1891820663349751809",
                              "optionContent": "yum search &lt;package_name&gt;",
                              "questionId": "1891820663286837250",
                              "rightFlag": 0,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891820663349751810",
                              "optionContent": "yum install &lt;package_name&gt;",
                              "questionId": "1891820663286837250",
                              "rightFlag": 1,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891820663349751811",
                              "optionContent": "yum update &lt;package_name&gt;",
                              "questionId": "1891820663286837250",
                              "rightFlag": 0,
                              "optionPosition": 1,
                              "sort": 0
                            },
                            {
                              "optionId": "1891820663349751812",
                              "optionContent": "yum remove &lt;package_name&gt;",
                              "questionId": "1891820663286837250",
                              "rightFlag": 0,
                              "optionPosition": 1,
                              "sort": 0
                            }
                          ],
                          "codeContent": null,
                          "createSource": null
                        }
                      }
                    ],
                    "questionTitle": ""
                  }
                }
              ]
            },
            {
              "type": "page",
              "attrs": {
                "id": "6uan1cxs",
                "extend": false,
                "class": "bellCss",
                "HTMLAttributes": null,
                "pageNumber": 9,
                "force": true,
                "slots": {},
                "pagesPosition": "bottom",
                "pagesAlign": "center",
                "pagesShow": true,
                "color": "#333"
              },
              "content": [
                {
                  "type": "heading",
                  "attrs": {
                    "id": "lh7xu5",
                    "extend": false,
                    "class": "bellCss",
                    "columnCount": 0,
                    "indent": null,
                    "textAlign": null,
                    "lineHeight": 1.5,
                    "margin": {},
                    "data-toc-id": "lh7xu5",
                    "backgroundImage": "",
                    "containerColor": "",
                    "backgroundSize": "",
                    "backgroundBorder": "",
                    "level": 2
                  },
                  "content": [
                    {
                      "type": "text",
                      "text": "作业部分一"
                    }
                  ]
                },
                {
                  "type": "papers",
                  "attrs": {
                    "vnode": true,
                    "id": null,
                    "width": null,
                    "height": 200,
                    "paperInfo": {
                      "createBy": "1",
                      "createTime": "2025-02-21 17:33:19",
                      "updateBy": null,
                      "updateTime": null,
                      "remark": null,
                      "paperId": "1892870203851608066",
                      "paperTitle": "第一章节的作业情况",
                      "questionQuantity": 6,
                      "totalScore": 0,
                      "paperType": 2,
                      "delFlag": "0",
                      "dtbTestPaperQuestionCollectionList": null
                    }
                  }
                }
              ]
            }
          ]
        }`]
      },
    ]
  },
  {
    name: 'index.html',
    type: 'file',
    content: [`<!DOCTYPE html>
    <html lang="en">
    
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>决战的开始</title>
      <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
      <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
      <link rel="stylesheet" href="./sub/index.css">
      <script src="https://unpkg.com/element-plus"></script>
      <style>
        table,
        table tr th,
        table tr td {
          border: 1px solid #000;
          /* 黑色边框，1像素宽 */
        }
    
        table {
          border-collapse: collapse;
          /* 合并边框，避免边框重复 */
          text-align: center;
          width: 100%;
          font-size: 14px;
    
        }
    
    
        .video-parent.collapse-title {
          display: flex;
          align-items: center;
          font-weight: 800;
          font-size: 16px;
          color: #333333;
        }
    
        .video-parent .collapse-title .name {
          font-weight: bold;
          font-size: 16px;
          color: #0966b4;
        }
    
        .video-parent .el-collapse-item__content {
          padding: 0;
        }
    
        .video-parent .el-collapse-item__header {
          background-image: url(./sub/assets/images/modelBackground.png);
        }
    
        .video-parent .collapse-title>img {
          margin: 0 10px 0 17px;
        }
      </style>
    </head>
    
    <body>
      <div id="app">
        <header class="header">
          <div class="left">
            决战的开始
          </div>
          <div class="center">
            <div class="paging"> </div>
          </div>
          <div class="right"></div>
        </header>
        <main class="main">
          <div class="page">
            <div class="pageItem" v-for="(ele,i) in pageData" :key="ele.attrs.id" :id="'pageId_'+i">
              <div class="pageItemHeader"></div>
              <div class="pageItemMain">
                <Paragraph v-for="item in ele.content" :key="item.attrs.id" :datas="item" />
              </div>
              <div class="pageItemFooter">
                {{i+1}}
              </div>
            </div>
          </div>
        </main>
      </div>
    
      <script src="./sub/pageData.js"></script>
      <script src="./sub/index.js"></script>
    </body>
    
    </html>`]
  }
]


const createZip = (_zip, _data) => {
  return new Promise((resolve) => {
    let timer = null
    function initZip(zip, data) {
      if (timer) clearTimeout(timer)
      data.forEach(ele => {
        if (timer) clearTimeout(timer)
        if (ele.type === 'folder') {
          initZip(zip.folder(ele.name), ele.content)
        } else if (ele.type === 'file') {
          zip.file(ele.name, ele.content[0])
          if (timer) clearTimeout(timer)
        }
        timer = setTimeout(() => {
          resolve(_zip)
        }, 200)
      })
    }
    initZip(_zip, _data)

  })
}


export const start = (fileName = 'example') => {
  const zip = new JSZip();
  createZip(zip, jszipConfig).then(res => {
    res.generateAsync({ type: 'blob' })
      .then(content => {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(content);
        link.download = fileName + '.zip';
        link.click();
        URL.revokeObjectURL(link.href);
      })
  })
}