<template>
  <div class="container">
    <div class="header">
      <div class="left">
        <div class="title">编程题</div>
      </div>
      <div class="right"></div>
    </div>
    <div class="codeMain">
      <div class="codeLeft">
        <div class="codeTitle">
          <span>请基于下方题干内容编写代码</span>
          <div v-html="questionContent"></div>
        </div>
        <div class="codeHeader">
          <div class="codeHeaderLeft">
            <el-form>
              <el-form-item label="选择编程语言">
                {{ language }}
                <!-- <el-select
                  v-model="codeLanguageValue"
                  placeholder="请选择编程语言"
                  size="large"
                  style="width: 240px"
                  @change="handleCodeLanguageChange"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select> -->
              </el-form-item>
            </el-form>
          </div>
          <div class="codeHeaderRight">
            <el-button type="primary" :icon="CopyDocument" @click="copyCode"
              >复制代码</el-button
            >
            <el-button
              type="success"
              :icon="VideoPlay"
              :disabled="playloading"
              @click="runCode"
              >运行</el-button
            >
          </div>
        </div>

        <div class="codeLeftBg">
          <div class="textarea-container">
            <div class="line-numbers" id="lineNumbers"></div>
            <el-input
              id="styledTextarea"
              v-model="htmlContent"
              @input="updateLineNumbers"
              type="textarea"
              ref="textareaRef"
              autosize
              style="padding-left: 40px; border: none"
            ></el-input>
          </div>
        </div>
        <div>
          <question-hint
            v-if="status.status"
            :analysis="questionsData?.analysis"
            :sectionReferToData="questionsData?.sectionReferTo"
          ></question-hint>
          <!-- -->
        </div>
      </div>
      <div class="codeRight">
        <div class="codeRightTitle">运行结果</div>
        <div v-if="codeLanguageValue == 'html'" style="margin: 30px 0">
          <iframe ref="myIframe" :srcdoc="resultContent"></iframe>
        </div>
        <div v-else>
          <pre v-loading="loading">{{ resultContent }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, watch } from "vue";
import { CopyDocument, VideoPlay } from "@element-plus/icons-vue";
import QuestionHint from "@/views/reader/sub/PageModule/question-hint/questionHint";
import {
  addQuestionAnswer,
  getQuestionStem,
  editQuestionAnswer,
} from "@/api/book/reader";
import { chartAi } from "@/api/openApi/openApi";
import { ElMessage } from "element-plus";
// 用于存储用户输入的HTML内容
const htmlContent = ref("");
const codeLanguageValue = ref("html");
const answerInfo = ref(null);
const resultContent = ref("");
const route = useRoute();
const playloading = ref(false);
const questionsData = ref(null);
onMounted(() => {
  getQuestion();
  const localData = sessionStorage.getItem("questionsData");
  if (localData) {
    questionsData.value = JSON.parse(localData);
    console.log(questionsData.value);
    sessionStorage.removeItem("questionsData");
  }
});
const status = reactive({
  status: false,
});
const userQuestionId = computed(() => route.query.userQuestionId);
const bookQuestionId = computed(() => route.query.bookQuestionId);
const chapterId = computed(() => route.query.chapterId);
const bookId = computed(() => route.query.bookId);
const language = computed(() => route.query.language);

const loading = ref(false);

//题干
const questionContent = ref("");

const getQuestion = async () => {
  const params = {
    questionId: userQuestionId.value,
    bookQuestionId: bookQuestionId.value,
    chapterId: chapterId.value,
    bookId: bookId.value,
  };
  const res = await getQuestionStem(params);
  console.log(res.data.codeContent);
  if (res.code === 200) {
    questionContent.value = res.data.questionContent;
    answerInfo.value = res.data.answerInfo;
    const answerContent = res.data.answerInfo?.answerContent
      ? res.data.answerInfo?.answerContent
      : JSON.parse(res.data.codeContent);
    htmlContent.value =
      Object.prototype.toString.call(res.data.codeContent) === "[object Object]"
        ? res.data.codeContent?.code
        : JSON.parse(res.data.codeContent).code;
    codeLanguageValue.value = answerContent?.type || "html";
    updateLineNumbers();
  }
};

const copyCode = async () => {
  if (!htmlContent.value)
    return ElMessage({ message: "请输入代码", type: "warning" });
  try {
    await navigator.clipboard.writeText(htmlContent.value);
    ElMessage({ message: "复制成功", type: "success" });
  } catch (err) {
    ElMessage({ message: "复制失败", type: "warning" });
  }
};

const getQueryChapterContent = async () => {
  const params = {
    userQuestionId: userQuestionId.value,
    bookQuestionId: bookQuestionId.value,
    chapterId: chapterId.value,
    bookId: bookId.value,
    score: 0,
    answerContent: htmlContent.value,
  };
  const res = await addQuestionAnswer(params);
  if (res.code === 200) {
    ElMessage({
      message: "运行成功",
      type: "success",
    });
  }
};

const editQueryChapterContent = async () => {
  const params = {
    userQuestionId: userQuestionId.value,
    bookQuestionId: bookQuestionId.value,
    chapterId: chapterId.value,
    bookId: bookId.value,
    score: 0,
    answerContent: JSON.stringify({
      type: codeLanguageValue.value,
      code: htmlContent.value,
    }),
    answerId: answerInfo.value.answerId,
  };
  const res = await editQuestionAnswer(params);
  if (res.code === 200) {
    ElMessage({
      message: "运行成功",
      type: "success",
    });
  }
};

const runCode = () => {
  playloading.value = true;
  if (
    language.value == "html" ||
    language.value == "css" ||
    language.value == "js"
  ) {
    if (answerInfo.value) {
      editQueryChapterContent();
    } else {
      getQueryChapterContent();
    }
    resultContent.value = htmlContent.value;
  } else {
    loading.value = true;
    chartAi({
      ability: 25,
      question: htmlContent.value,
      developmentLanguage: language.value,
    }).then((res) => {
      loading.value = false;
      if (answerInfo.value) {
        editQueryChapterContent();
      } else {
        getQueryChapterContent();
      }
      console.log(res.body.content.result);
      resultContent.value = res.body.content;
      status.status = true;
      playloading.value = false;
    });
  }
};

function updateLineNumbers() {
  const textarea = document.getElementById("styledTextarea");
  const lines = textarea.value.split(/\r*\n/).length;

  const lineNumbers = document.getElementById("lineNumbers");
  lineNumbers.innerHTML = Array.from(
    { length: lines },
    (_, i) => `<div style="
    display: flex;
    line-height:24px;
    align-items: center;
    color:#237893;
    justify-content: center;">${i + 1}</div>`
  ).join("");
}
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f0f4fa;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    padding: 0 20px;
    background-color: #f9f9f9;
    .left {
      .title {
        font-size: 16px;
        font-weight: bold;
      }
    }
    .right {
      display: flex;
      align-items: center;
      div {
        margin-left: 20px;
      }
      .copyCode {
        display: flex;
        align-items: center;
        span {
          margin-left: 10px;
        }
      }
    }
  }

  .codeMain {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    min-height: calc(100% - 50px);

    .codeLeft {
      padding: 20px;
      .codeTitle {
        color: #666;
        font-size: 16px;
        span {
          font-weight: bold;
          color: #333;
        }
      }
      .codeHeader {
        margin: 20px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .codeHeaderRight {
          display: flex;
          align-items: center;
          gap: 20px;
        }
      }
      .codeLeftBg {
        width: 100%;
        border: 1px solid #f1f1f1;
        border-radius: 10px;
        overflow: hidden;
        padding: 20px;
        background-color: #fff;
        .textarea-container {
          position: relative;
          #styledTextarea {
            width: 100%;
            padding-left: 3em;
            border: none;
            outline: none;
            resize: both;
            font-size: 16px;
            line-height: 24px;
          }

          .line-numbers {
            position: absolute;
            left: 0;
            top: 0;
            width: 2.5em;
            padding: 4px 0;
            text-align: center;
            color: #666;
            user-select: none;
            z-index: 99;
            .line-number {
              background-color: red;
            }
          }

          .line-numbers span {
            display: flex;
            background-color: #666;
            counter-increment: line;
          }

          .line-numbers span::before {
            content: counter(line);
          }
        }
      }
    }
    .codeRight {
      background-color: #f8f8f8;
      padding: 30px;
      height: calc(100vh - 60px);
      .codeRightTitle {
        font-weight: bold;
        font-size: 16px;
        border-bottom: 1px solid #ccc;
        padding-bottom: 20px;
        &::before {
          content: "";
          display: inline-block;
          width: 4px;
          height: 16px;
          background-color: #409eff;
          margin-right: 10px;
        }
      }

      iframe {
        width: 100%;
        height: 100vh;
        border: none;
      }
    }
  }
}
</style>
