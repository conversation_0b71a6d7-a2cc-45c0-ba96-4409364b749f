<template>
  <div class="comp-card-con">
    <div class="search-content-con">
      <div class="search-content-header">
        <div class="search-content-label">
          <span class="search-content-label-text">
            找到约 <span>{{ total }}</span
            >条搜索结果
          </span>
        </div>
        <div class="order-sort-option-con">
          <div class="sort-about-option" @click="aboutOptionClick">
            <img src="@/assets/images/search/sort-about-option-icon.png" alt="" />
            <span>按相关性</span>
          </div>
          <div class="sort-arrow-option" @click="sortByTimeOptionClick">
            <div class="img-arrow">
              <div class="up-arrow" :class="{ 'active-up': sortByTimeUpActive }" />
              <div class="down-arrow" :class="{ 'active-down': sortByTimeDownActive }" />
            </div>
            <span>上架时间</span>
          </div>
          <div class="sort-arrow-option" @click="sortByPriceOptionClick">
            <div class="img-arrow">
              <div class="up-arrow" :class="{ 'active-up': sortByPriceUpActive }" />
              <div class="down-arrow" :class="{ 'active-down': sortByPriceDownActive }" />
            </div>
            <span>售价</span>
          </div>
          <div class="sort-arrow-option" @click="sortByReadNumOptionClick">
            <div class="img-arrow">
              <div class="up-arrow" :class="{ 'active-up': sortByReadNumUpActive }" />
              <div class="down-arrow" :class="{ 'active-down': sortByReadNumDownActive }" />
            </div>
            <span>阅读量</span>
          </div>
          <div class="divider-line" />
          <div class="sort-about-option" @click="contentListTypeHandler">
            <img src="@/assets/images/search/show-type-card-icon.png" alt="" v-if="contentListType === 'card'" />
            <img src="@/assets/images/search/show-type-list-icon.png" alt="" v-else />
            <span>{{ getContentListTypeLabel }}</span>
          </div>
        </div>
      </div>
      <div class="search-content-card-list-con" v-if="contentListType === 'card'">
        <searchContentCardList :itemList="itemList" />
      </div>
      <div class="search-content-line-list-con" v-else>
        <searchContentLineList :itemList="itemList" />
      </div>
      <div class="page-con">
        <pagination
          v-show="total > 0 && param === '0'"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getInfo"
        />
        <pagination
          v-show="total > 0 && param !== '0'"
          :total="total"
          v-model:page="parm.pageNum"
          v-model:limit="parm.pageSize"
          @pagination="getInfo"
        />
      </div>
    </div>
  </div>
</template>

<script setup name="SearchContentCard">
import searchContentCardList from '@/views/search/components/searchContentCardList/index.vue';
import searchContentLineList from '@/views/search/components/searchContentLineList/index.vue';
import { miSearchEducation } from '@/api/openApi/openApi';
import { onMounted } from 'vue';
import { searchBook } from '@/api/elasticsearch/elasticsearch';
import { useRoute } from 'vue-router';
import Pagination from '@/components/Pagination';
// 获取当前路由对象
const route = useRoute();
const total = ref(1);
const queryParams = ref({
  pageNum: 1,
  pageSize: 30,
});
// 获取当前 URL
const url = new URL(window.location.href);
// 获取查询参数 param1
const param = url.searchParams.get('param');
const props = defineProps({
  paramsObjectData: {
    type: Object,
    default: null,
  },
  bookType: {
    type: String,
    default: '',
  },
});
const parm = ref({
  pageNum: 1,
  pageSize: 30,
});
const contentListType = ref('card'); // 'card' or 'list'
const sortByTimeUpActive = ref(true);
const sortByTimeDownActive = ref(false);
const sortByPriceUpActive = ref(true);
const sortByPriceDownActive = ref(false);
const sortByReadNumUpActive = ref(true);
const sortByReadNumDownActive = ref(false);
const itemList = ref([]);
const originalItemList = ref([]); // 保存原始数据
function doClickItem(chItem, index, chIndex) {
  chItem.isChecked = !chItem.isChecked;
}
function contentListTypeHandler() {
  contentListType.value = contentListType.value === 'card' ? 'list' : 'card';
}
const getContentListTypeLabel = computed(() => {
  return contentListType.value === 'card' ? '卡片视图' : '列表视图';
});

function aboutOptionClick() {
  if (originalItemList.value.length > 0) {
    itemList.value = [...originalItemList.value];

    // 重置所有排序图标状态
    sortByTimeUpActive.value = true;
    sortByTimeDownActive.value = false;
    sortByPriceUpActive.value = true;
    sortByPriceDownActive.value = false;
    sortByReadNumUpActive.value = true;
    sortByReadNumDownActive.value = false;
  }
}
function sortByTimeOptionClick() {
  sortByTimeUpActive.value = !sortByTimeUpActive.value;
  sortByTimeDownActive.value = !sortByTimeDownActive.value;
  if (sortByTimeUpActive.value) {
    itemList.value.sort((a, b) => new Date(a.shelfTime) - new Date(b.shelfTime));
  } else {
    itemList.value.sort((a, b) => new Date(b.shelfTime) - new Date(a.shelfTime));
  }
  console.log('shelfTime values:', itemList.value.map(item => item.shelfTime));
}
function sortByPriceOptionClick() {
  sortByPriceUpActive.value = !sortByPriceUpActive.value;
  sortByPriceDownActive.value = !sortByPriceDownActive.value;
  if (sortByPriceUpActive.value) {
    itemList.value.sort((a, b) => new Date(a.priceSale) - new Date(b.priceSale));
  } else {
    itemList.value.sort((a, b) => new Date(b.priceSale) - new Date(a.priceSale));
  }
}
function sortByReadNumOptionClick() {
  sortByReadNumUpActive.value = !sortByReadNumUpActive.value;
  sortByReadNumDownActive.value = !sortByReadNumDownActive.value;
  if (sortByReadNumUpActive.value) {
    itemList.value.sort((a, b) => new Date(a.readQuantity) - new Date(b.readQuantity));
  } else {
    itemList.value.sort((a, b) => new Date(b.readQuantity) - new Date(a.readQuantity));
  }
}
const getInfo = () => {
  if (param === '0') {
    // 合并对象，保留 pageNum 和 pageSize
    queryParams.value = JSON.parse(localStorage.getItem('queryCriteria')); // 更新其他字段
    queryParams.value = queryParams.value ? { ...queryParams.value, pageNum: 1, pageSize: 30 } : { pageNum: 1, pageSize: 30 };
    if (queryParams.value.pushYearRange && Array.isArray(queryParams.value.pushYearRange) && queryParams.value.pushYearRange.length === 2) {
      queryParams.value.pushYearRange = [
        formatDate(queryParams.value.pushYearRange[0], 'YYYY-MM-DD'),
        formatDate(queryParams.value.pushYearRange[1], 'YYYY-MM-DD'),
      ];
    }
    miSearchEducation(queryParams.value).then((res) => {
      itemList.value = [...res.rows];
      originalItemList.value = [...res.rows]; // 保存原始副本
      total.value = res.total;
    });
  } else {
    searchBook(parm.value).then((response) => {
      itemList.value = [...response.data.list];
      originalItemList.value = [...response.data.list]; // 保存原始副本
      total.value = response.data.total;
    });
  }
  // else {
  //   // 高级搜索暂时弃用
  //   // 合并对象，保留 pageNum 和 pageSize
  //   queryParams.value = JSON.parse(localStorage.getItem('searchAdvItemCellList')); // 更新其他字段
  // }
};
const paramsObjectDataComputed = computed(() => props.paramsObjectData);

watch(
  paramsObjectDataComputed,
  (newVal, oldVal) => {
    if (!param) {
      if (newVal) {
        console.log('----newVal---');
        console.log(newVal);
        parm.value.topSubjectId = newVal.topSubjectId || '';
        parm.value.secondSubjectId = newVal.secondSubjectId || '';
        parm.value.thirdSubjectId = newVal.thirdSubjectId || '';
        parm.value.forthSubjectId = newVal.forthSubjectId || '';
        /*if (newVal.subjectLevel === 1) {
          parm.value.topSubjectId = newVal.parentId !== -1 && newVal.isChecked ? newVal.subjectId : null;
          parm.value.secondSubjectId = null
          parm.value.thirdSubjectId = null
          parm.value.forthSubjectId = null
        } else if (newVal.subjectLevel === 2) {
          parm.value.secondSubjectId = newVal.parentId !== -1 && newVal.isChecked ? newVal.subjectId : null;
          parm.value.thirdSubjectId = null
          parm.value.forthSubjectId = null
        } else if (newVal.subjectLevel === 3) {
          parm.value.thirdSubjectId = newVal.parentId !== -1 && newVal.isChecked ? newVal.subjectId : null;
          parm.value.forthSubjectId = null
        } else if (newVal.subjectLevel === 4) {
          parm.value.forthSubjectId = newVal.parentId !== -1 && newVal.isChecked ? newVal.subjectId : null;
        }*/
        searchBook(parm.value).then((response) => {
          itemList.value = [...response.data.list];
          originalItemList.value = [...response.data.list]; // 保存原始副本
          total.value = response.data.total;
        });
      }
    }
  },
  { deep: true, immediate: true } // 启用深度监听并立即执行
);
watch(
  () => route.query.searchKey,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      if (!param) {
        parm.value.bookName = route.query.searchKey;
        parm.value.authorValue = route.query.searchKey;
        parm.value.isbn = route.query.searchKey;
        searchBook(parm.value).then((response) => {
          itemList.value = [...response.data.list];
          originalItemList.value = [...response.data.list]; // 保存原始副本
          total.value = response.data.total;
        });
      }
    }
  }
);
watch(
  () => props.bookType,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      if (!param) {
        parm.value.bookType = props.bookType;
        searchBook(parm.value).then((response) => {
          itemList.value = [...response.data.list];
          originalItemList.value = [...response.data.list]; // 保存原始副本
          total.value = response.data.total;
        });
      }
    }
  }
);
// 时间转换方法
function formatDate(date, format = 'YYYY-MM-DD') {
  const padZero = (num) => (num < 10 ? '0' + num : num);
  const dateObj = new Date(date);

  const year = dateObj.getFullYear();
  const month = padZero(dateObj.getMonth() + 1);
  const day = padZero(dateObj.getDate());

  return format.replace('YYYY', year).replace('MM', month).replace('DD', day);
}

onMounted(() => {
  parm.value.bookName = route.query.searchKey;
  parm.value.authorValue = route.query.searchKey;
  parm.value.isbn = route.query.searchKey;
  getInfo();
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';

.comp-card-con {
  width: 100%;
  @extend .base-flex-row;
  justify-content: center;
  align-items: flex-start;

  .search-content-con {
    width: 1400px;
    min-height: 286px;
    margin-bottom: 44px;
    @extend .base-flex-column;
    justify-content: flex-start;
    align-items: center;

    .search-content-header {
      width: 100%;
      margin-bottom: 42px;
      @extend .base-flex-row;
      justify-content: space-between;
      align-items: center;

      .search-content-label {
        margin-left: 25px;
        .search-content-label-text {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #333333;
          text-align: center;

          span {
            color: #316eb7ff;
          }
        }
      }

      .order-sort-option-con {
        @extend .base-flex-row;
        justify-content: flex-end;
        align-items: center;

        .sort-about-option {
          margin-right: 20px;
          cursor: pointer;
          @extend .base-flex-row;
          justify-content: center;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
            margin-right: 8px;
          }

          span {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            text-align: center;
          }
        }

        .sort-about-option:hover {
          opacity: 0.8;

          span {
            color: #2162b1ff;
          }
        }

        .sort-arrow-option {
          margin-right: 20px;
          cursor: pointer;
          @extend .base-flex-row;
          justify-content: center;
          align-items: center;

          .img-arrow {
            margin-right: 6px;
            @extend .base-flex-column;
            justify-content: center;
            align-items: center;

            .up-arrow {
              width: 0;
              height: 0;
              border-left: 4.5px solid transparent;
              /* 左边框透明 */
              border-right: 4.5px solid transparent;
              /* 右边框透明 */
              border-top: 0 solid transparent;
              /* 上边框透明，实际上不需要设置，因为高度为0 */
              border-bottom: 5px solid #919497;
              /* 下边框设置三角形的高度和颜色 */
              margin-bottom: 3px;
            }

            .down-arrow {
              width: 0;
              height: 0;
              border-left: 4.5px solid transparent;
              /* 左边框透明 */
              border-right: 4.5px solid transparent;
              /* 右边框透明 */
              border-bottom: 0 solid transparent;
              /* 底边框透明，实际上不需要设置，因为高度为0 */
              border-top: 5px solid #919497;
              /* 上边框设置三角形的高度和颜色 */
            }

            .active-up {
              border-bottom: 5px solid #2768a5;
            }

            .active-down {
              border-top: 5px solid #2768a5;
            }
          }

          span {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            text-align: center;
          }
        }

        .sort-arrow-option:hover {
          opacity: 0.8;

          span {
            color: #2162b1ff;
          }
        }

        .divider-line {
          width: 1px;
          height: 12px;
          border: 1px solid #999999;
          margin-right: 20px;
        }

        .show-type-option {
        }
      }
    }

    .search-content-card-list-con {
      width: 100%;
      transition: all 0.3s ease-in-out;
    }

    .search-content-line-list-con {
      width: 100%;
      transition: all 0.3s ease-in-out;
    }

    .page-con {
      width: 100%;
      @extend .base-flex-row;
      justify-content: end;
      align-items: center;
      margin-top: 40px;

      .demonstration {
        margin-right: 40px;
      }
    }
  }
}
</style>
