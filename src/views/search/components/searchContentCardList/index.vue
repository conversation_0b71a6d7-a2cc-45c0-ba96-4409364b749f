<template>
  <div class="search-content-card-list">
    <div class="item-card" v-for="(item, index) in props.itemList" :key="index">
      <div class="list-item">
        <div class="img-con" @click="doClickItem(item)">
          <img class="item-shadow" :src="bookLeftShadow" alt="" />
          <div class="img-item-con">
            <img class="item-img" :src="item.cover ? item.cover : bookCoverDefault" alt="" />
            <img class="item-img-line" :src="bookFightLine" alt="" />
          </div>
        </div>
        <div class="item-info-con">
          <el-tooltip effect="dark" :content="item.bookName" placement="top" :disabled="!isTextOverflow">
            <div class="item-name">{{ item.bookName }}</div>
          </el-tooltip>
          <div class="item-author">{{ item.authorLabel ? item.authorLabel + '：' : '' }} {{ item.authorValue ? item.authorValue : '' }}</div>
          <div class="item-price">{{ item.priceSale ? '￥' + item.priceSale : '' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="SearchContentCardCell">
import bookLeftShadow from '@/assets/images/search/book-left-shadow.png';
import bookFightLine from '@/assets/images/search/book-right-line.png';
import bookCoverDefault from '@/assets/images/book-cover-default.png';
// import useSiteStore from '@/store/modules/site'
import { useRouter } from 'vue-router';
// import { encryptData } from '@/utils/encrypt.js';
const router = useRouter();

function isTextOverflow(e) {
  const el = e.target;
  isTextOverflow.value = el.scrollWidth > el.clientWidth;
}
async function doClickItem(item) {
  // // 获取后端的公钥
  // const publicKey = useSiteStore().publicKey;
  // if (!publicKey) {
  //   console.error('无法获取公钥');
  //   return;
  // }

  // // 使用公钥加密数据
  // const { encryptedData } = await encryptData(item.bookId, publicKey);

  // if (!encryptedData) {
  //   console.error('数据加密失败');
  //   return;
  // }
  const query = { key: item.bookId };
  router.push({ path: '/book-detail', query: query });
}
const props = defineProps({
  itemList: Object,
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.search-content-card-list {
  width: 100%;
  @extend .base-flex-row;
  // justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 20px;
  margin-left: 85px;
  .item-card {
    width: 252px;
    background: #ffffff;
    border-radius: 16px;
    margin-bottom: 32px;
    @extend .base-flex-column;
    justify-content: flex-start;
    align-items: flex-start;
    .list-item {
      width: 100%;
      margin-bottom: 20px;
      @extend .base-flex-column;
      justify-content: center;
      align-items: center;
      .img-con {
        width: 100%;
        margin-bottom: 20px;
        @extend .base-flex-row;
        justify-content: flex-start;
        align-items: center;
        .item-shadow {
          width: 39px;
          height: 66px;
          margin-right: -1px;
          align-self: flex-end;
        }
        .item-img-line {
          width: 12px;
          height: 258px;
          margin-left: -4px;
        }
        .img-item-con {
          width: 185px;
          height: 258px;
          border-top: 1px solid #e5e5e5;
          border-bottom: 1px solid #e5e5e5;
          border-top-right-radius: 15px;
          border-bottom-right-radius: 15px;
          @extend .base-flex-row;
          justify-content: flex-start;
          align-items: center;
          .item-img {
            width: 185px;
            height: 258px;
            border-radius: 4px;
            transition: transform 0.3s ease;
            transform-origin: bottom left; /* 设置旋转的基准点为右下角 */
            perspective: 5500px; /* 设置3D透视效果，使翻书更加自然 */
            backface-visibility: hidden; /* 避免翻面时显示背面 */
          }
          .item-img:hover {
            animation: flipPage 0.3s forwards; /* 启动翻页动画 */
          }
          @keyframes flipPage {
            0% {
              transform: scaleX(1) rotateY(0deg); /* 初始状态：不变 */
            }
            30% {
              transform: scaleX(0.95) rotateY(-12deg); /* 初步翻转，缩放并旋转 */
            }
            50% {
              transform: scaleX(0.8) rotateY(-25deg); /* 进一步翻转，模拟翻书中间 */
            }
            70% {
              transform: scaleX(0.9) rotateY(-12deg); /* 反向缩放并旋转 */
            }
            100% {
              transform: scaleX(1) rotateY(0deg); /* 结束时回到正常状态 */
            }
          }
        }
      }
      .item-info-con {
        width: 196px;
        min-height: 100px;
        @extend .base-flex-column;
        justify-content: flex-start;
        align-items: center;
        .item-name {
          width: 100%;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #333333;
          line-height: 28px;
          text-align: left;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 7px;
        }
        .item-author {
          width: 100%;
          height: 46px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #999999;
          line-height: 46px;
          text-align: left;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 0;
        }
        .item-price {
          width: 100%;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #0966b4;
          text-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
          text-align: left;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>
