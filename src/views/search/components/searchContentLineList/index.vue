<template>
  <div class="search-content-line-list">
    <div class="line-list-table">
      <div class="row-one">
        <div class="col-one">
          <span class="col-one-title">书名</span>
        </div>
        <div class="col-two">
          <div class="col-two-title">主编</div>
        </div>
        <div class="col-three">
          <div class="col-three-title">出版时间</div>
        </div>
        <div class="col-four">
          <div class="col-four-title">ISBN</div>
        </div>
        <div class="col-four">
          <div class="col-four-title">ISSN</div>
        </div>
        <div class="col-five">
          <div class="col-five-title">售价</div>
        </div>
      </div>
    </div>
    <div class="line-list-table">
      <div class="row-two" v-for="(item, index) in props.itemList" :item="item" :key="index">
        <div class="col-one">
          <div class="col-info-con" @click="doClickItem(item)">
            <img class="book-left-shadow" :src="bookLeftShadow" alt="" />
            <img class="book-img" :src="item.cover ? item.cover : bookCoverDefault" alt="" />
            <div class="book-name">{{ item.bookName }}</div>
          </div>
        </div>
        <div class="col-two">
          <div class="col-info-con">{{ item.authorValue ? item.authorValue : '' }}</div>
        </div>
        <div class="col-three">
          <div class="col-info-con">{{ item.publishDate ? item.publishDate : '' }}</div>
        </div>
        <div class="col-four">
          <div class="col-info-con">{{ item.isbn ? item.isbn : '' }}</div>
        </div>
        <div class="col-four">
          <div class="col-info-con">{{ item.issn ? item.issn : '' }}</div>
        </div>
        <div class="col-five">
          <div class="col-info-con">{{ item.shelfState == 1 ? '￥' + item.priceSale : '￥' + item.priceCounter }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="SearchContentCardList">
import { ref } from 'vue';
import bookCoverDefault from '@/assets/images/book-cover-default.png';
import bookLeftShadow from '@/assets/images/search/book-left-shadow.png';
import bookFightLine from '@/assets/images/search/book-right-line.png';
import { useRouter } from 'vue-router';
const show = ref(false);
const props = defineProps({
  itemList: Array,
});
const router = useRouter();
async function doClickItem(item) {
  // // 获取后端的公钥
  // const publicKey = useSiteStore().publicKey;
  // if (!publicKey) {
  //   console.error('无法获取公钥');
  //   return;
  // }

  // // 使用公钥加密数据
  // const { encryptedData } = await encryptData(item.bookId, publicKey);

  // if (!encryptedData) {
  //   console.error('数据加密失败');
  //   return;
  // }
  const query = { key: item.bookId };
  router.push({ path: '/book-detail', query: query });
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.search-content-line-list {
  width: 100%;
  .line-list-table {
    width: 100%;
    .row-one {
      width: 100%;
      height: 54px;
      background: #f1f7ff;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      text-align: left;
      @extend .base-flex-row;
      justify-content: space-between;
      align-items: center;
      .col-one {
        width: 22%;
        .col-one-title {
          width: 100%;
          text-align: left;
          padding-left: 40px;
        }
      }
      .col-two {
        width: 22%;
        .col-two-title {
          width: 100%;
          text-align: left;
        }
      }
      .col-three {
        width: 20%;
        .col-three-title {
          width: 100%;
          text-align: center;
        }
      }
      .col-four {
        width: 18%;
        .col-four-title {
          width: 100%;
          text-align: center;
        }
      }
      .col-five {
        width: 18%;
        .col-five-title {
          width: 100%;
          text-align: center;
        }
      }
    }
    .row-two {
      width: 100%;
      height: 100px;
      @extend .base-flex-row;
      justify-content: space-between;
      align-items: center;
      background: #ffffff;
      border-bottom: 1px solid #e5e6e7;
      .col-one {
        width: 22%;
        height: 100%;
        @extend .base-flex-row;
        justify-content: flex-start;
        align-items: center;
        .col-info-con {
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          @extend .base-flex-row;
          justify-content: center;
          align-items: flex-end;
          .book-left-shadow {
            width: 11px;
            height: 19px;
          }
          .book-img {
            width: 54px;
            height: 76px;
            margin-right: 32px;
          }
          .book-img:hover {
            // 透明度0.5
            opacity: 0.7;
          }
          .book-name {
            align-self: center;
            width: 176px;
            height: 28px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 16px;
            color: #333333;
            line-height: 28px;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .book-name:hover {
            // 透明度0.5
            opacity: 0.6;
          }
        }
      }
      .col-two {
        width: 22%;
        .col-info-con {
          width: 100%;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #999999;
          text-align: left;
        }
      }
      .col-three {
        width: 20%;
        .col-info-con {
          width: 100%;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #999999;
          text-align: center;
        }
      }
      .col-four {
        width: 18%;
        .col-info-con {
          width: 100%;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #999999;
          text-align: center;
        }
      }
      .col-five {
        width: 18%;
        .col-info-con {
          width: 100%;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 600;
          font-size: 20px;
          color: #0966b4;
          line-height: 46px;
          text-align: center;
        }
      }
    }
  }
}
</style>
