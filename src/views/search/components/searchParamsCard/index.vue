<template>
  <div class="search-criteria-card-con">
    <div class="search-criteria-card">
      <div class="search-criteria-row" v-for="(item, index) in searchParamsList" :key="index">
        <div class="row-label" v-show="item.show">{{ item.subjectName }}:</div>
        <div class="row-params" v-show="item.show">
          <div
            class="params-item"
            :class="{ active: chItem.isChecked }"
            v-if="item.children && item.children.length > 0"
            v-for="(chItem, chIndex) in item.children"
            :key="chIndex"
            @click="doClickItem(index, chIndex)"
          >
            <span>{{ chItem.subjectName }}</span>
          </div>
        </div>
      </div>
      <!-- <el-divider /> -->
      <!--招标结束，删除-->
      <!-- <div class="search-criteria-row">
        <div class="label">中图分类：</div>
        <div class="input-con">
          <el-tree-select
            :check-strictly="true"
            class="clc-tree-sty"
            v-model="bookType"
            :data="clcThreeList"
            filterable
            :props="{
              key: 'typeId',
              label: 'typeName',
              value: 'typeId',
            }"
            :render-after-expand="false"
            @change="changeBookType"
          />
        </div>
      </div>
      <el-divider /> -->
    </div>
  </div>
</template>

<script setup name="SearchCriteriaCard">
import { listDutpSubjectEducation } from '@/api/basic/subject';
import { SubjectLevelOptions } from '@/api/dic';
import useSiteConfigStore from '@/store/modules/siteConfig';
import { listBookTypeEducation } from '@/api/openApi/openApi';
import { cloneDeep } from 'lodash';
const emit = defineEmits(['paramsChanged', 'bookTypeChanged']);
const clcThreeList = ref([]);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 100,
    subjectName: null,
    sort: null,
  },
});
const bookType = ref('');
const { queryParams } = toRefs(data);
const tempResponseData = ref({}); // 页面缓存数据
const searchParamsList = ref([]);
const searchParamsListTemp = ref([]);

function initListRowShow() {
  const siteConfigStore = useSiteConfigStore();
  const siteConfigInfo = siteConfigStore.siteConfigInfo;
  const searchParams = searchParamsList.value;
  const visibleLevels = ref([true, true, true, true]);
  if (siteConfigInfo) {
    visibleLevels.value = [
      siteConfigInfo.firstLevelVisible === 1 ? true : false,
      siteConfigInfo.secondLevelVisible === 1 ? true : false,
      siteConfigInfo.thirdLevelVisible === 1 ? true : false,
      siteConfigInfo.fourthLevelVisible === 1 ? true : false,
    ];
  }
  for (let i = 0; i < Math.min(visibleLevels.value.length, searchParams.length); i++) {
    searchParams[i].show = Boolean(visibleLevels.value[i]);
  }
}

async function doClickItem(index, chIndex) {
  if (index < 0 || chIndex < 0 || index >= searchParamsList.value.length || chIndex >= searchParamsList.value[index].children.length) {
    console.error('Invalid index or chIndex');
    return;
  }
  let chItemIsChecked = searchParamsList.value[index].children[chIndex].isChecked;
  // console.log('----index-----:' + index);
  // console.log('----chIndex-----:' + chIndex);
  searchParamsList.value[index].children.forEach((item) => {
    item.isChecked = false;
  });
  searchParamsList.value[index].children[chIndex].isChecked = !chItemIsChecked;
  //
  if (index === 0) {
    // 不等于-1
    await updateSecondSubject(chIndex);
  } else if (index === 1) {
    let parrentSubjectIdList = getDisplayParrentSubjectIdArr(index, chIndex);
    await updateThirdSubject(parrentSubjectIdList);
  } else if (index === 2) {
    let parrentSubjectIdList = getDisplayParrentSubjectIdArr(index, chIndex);
    await updateFourthSubject(parrentSubjectIdList);
  }
  let subjectIdArr = getSelectedSubjectId();
  let currentSubject = {
    topSubjectId: subjectIdArr[0],
    secondSubjectId: subjectIdArr[1],
    thirdSubjectId: subjectIdArr[2],
    forthSubjectId: subjectIdArr[3],
  };
  // console.log('------subjectIdArr-------');
  // console.log(JSON.stringify(subjectIdArr));
  emit('paramsChanged', currentSubject);
}

function getSelectedSubjectId() {
  let subjectIdArr = ['', '', '', '', ''];
  if (searchParamsList.value && searchParamsList.value.length > 0) {
    searchParamsList.value.forEach((item, index) => {
      if (item.children && item.children.length > 0) {
        item.children.forEach((chItem) => {
          if (chItem.isChecked && chItem.subjectId) {
            subjectIdArr[index] = chItem.subjectId;
          }
        });
      }
    });
  }
  return subjectIdArr;
}

function getDisplayParrentSubjectIdArr(index, chIndex) {
  let childrenArr = searchParamsList.value[index].children;
  if (childrenArr && childrenArr.length > 0 && childrenArr[chIndex]) {
    if (childrenArr[chIndex].parentId == '-1') {
      return childrenArr.map((item) => item.subjectId);
    } else {
      return [childrenArr[chIndex].subjectId];
    }
  }
  return [];
}

// 更新二级菜单
async function updateSecondSubject(chIndex) {
  // chIndex 父类的 选项索引
  // 获取父选项参数
  let index = 0;
  let currentParentId = searchParamsList.value[index].children[chIndex].parentId;
  let currentSubjectId = searchParamsList.value[index].children[chIndex].subjectId;
  // console.log('----currentParentId---------:' + currentParentId);
  // console.log('----currentSubjectId---------:' + currentSubjectId);
  searchParamsList.value[index + 1].children = [];
  let tempList = searchParamsListTemp.value[index + 1].children.filter((chitem) => {
    if (currentParentId === '-1') {
      return true; // 全部
    }
    return [currentSubjectId].includes(chitem.parentId) || chitem.parentId === '-1';
  });
  searchParamsList.value[index + 1].children = cloneDeep(tempList);
  if (tempList && tempList.length > 0) {
    let parrentSubjectIdList = tempList.map((tempItem) => {
      return tempItem.subjectId;
    });
    updateThirdSubject(parrentSubjectIdList);
  } else {
    updateThirdSubject([]);
  }
}
// 更新三级菜单
function updateThirdSubject(parrentSubjectIdArr) {
  let index = 1;
  searchParamsList.value[index + 1].children = [];
  // 根据二级的选择
  if (parrentSubjectIdArr && parrentSubjectIdArr.length > 0) {
    let tempList = searchParamsListTemp.value[index + 1].children.filter((chitem) => {
      return parrentSubjectIdArr.includes(chitem.parentId) || chitem.parentId === '-1';
    });
    searchParamsList.value[index + 1].children = cloneDeep(tempList);
    let parrentSubjectIdList = tempList.map((tempItem) => {
      return tempItem.subjectId;
    });
    updateFourthSubject(parrentSubjectIdList);
  } else {
    updateFourthSubject([]);
  }
}
// 更新四级菜单
function updateFourthSubject(parrentSubjectIdArr) {
  let index = 2;
  // 根据三级的选择
  searchParamsList.value[index + 1].children = [];
  // 根据二级的选择
  if (parrentSubjectIdArr && parrentSubjectIdArr.length > 0) {
    let tempList = searchParamsListTemp.value[index + 1].children.filter((chitem) => {
      return parrentSubjectIdArr.includes(chitem.parentId) || chitem.parentId === '-1';
    });
    searchParamsList.value[index + 1].children = cloneDeep(tempList);
  } else {
    searchParamsList.value[index + 1].children = [];
  }
}

function getSubjectName(key) {
  let subjectName = '其他';
  SubjectLevelOptions.value.forEach((item) => {
    if (item.val === key) {
      subjectName = item.label;
    }
  });
  return subjectName;
}
function initOptions() {
  listDutpSubjectEducation(queryParams.value).then((response) => {
    if (!response.data) {
      return;
    }
    searchParamsList.value = [];
    tempResponseData.value = JSON.parse(JSON.stringify(response.data));
    const keys = Object.keys(tempResponseData.value);
    if (keys && keys.length > 0) {
      // key
      keys.forEach((key, index) => {
        searchParamsList.value.push({
          subjectName: getSubjectName(key),
          key: key,
          value: 0,
          isChecked: false,
          children: tempResponseData.value[key],
        });
      });
    }
    searchParamsListTemp.value = cloneDeep(searchParamsList.value);

    initListRowShow();
  });
}
const getListType = () => {
  const parm = {
    pageNum: 1,
    pageSize: 99999,
  };
  listBookTypeEducation(parm).then((res) => {
    clcThreeList.value = res.data;
  });
};
const changeBookType = () => {
  emit('bookTypeChanged', bookType.value);
};
// 监视searchParamsListTemp的变化
watch(
  searchParamsListTemp,
  (newVal, oldVal) => {
    // 在这里处理searchParamsListTemp的变化
  },
  { deep: true }
);

// 初始化数据

onMounted(() => {
  initOptions();
  getListType();
});
</script>
<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.search-criteria-card-con {
  width: 100%;
  @extend .base-flex-row;
  justify-content: center;
  align-items: flex-start;
  .search-criteria-card {
    width: calc(1400px - 35px);
    min-height: 286px;
    padding-left: 35px;
    padding-top: 32px;
    background: linear-gradient(180deg, #e9f5ff 0%, #ffffff 100%);
    border-radius: 12px;
    .search-criteria-row {
      width: 100%;
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: center;
      .row-label {
        min-width: 74px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #8c8c8c;
        line-height: 46px;
        text-align: left;
        margin-right: 20px;
      }
      .row-params {
        flex: 1;
        @extend .base-flex-row;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: wrap;
        .params-item {
          min-width: 50px;
          height: 24px;
          font-size: 14px;
          color: #333333;
          line-height: 24px;
          text-align: center;
          margin-right: 20px;
          padding: 10px;
          cursor: pointer;
          @extend .base-flex-row;
          justify-content: center;
          align-items: center;
        }
        .params-item:hover {
          opacity: 0.7;
        }
        .active {
          background: #0966b4;
          border-radius: 12px;
          color: #ffffff;
        }
      }
    }
  }
}
.clc-tree-sty {
  width: 293px;

  :deep(.el-select__wrapper) {
    min-height: 40px !important;
  }
}
.label {
  min-width: 74px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #8c8c8c;
  line-height: 46px;
  text-align: left;
  margin-right: 20px;
}
</style>
