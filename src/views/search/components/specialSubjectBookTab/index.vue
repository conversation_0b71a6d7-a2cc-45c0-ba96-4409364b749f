<template>
  <div class="special-text-book-con">
    <div class="special-text-book-type-list">
      <div class="list-scroll-btn" @click="rotateLeft" v-if="showLeftBtn">
        <img src="@/assets/images/home/<USER>" alt="" />
      </div>

      <TransitionGroup name="fade" tag="div" class="list-con">
        <!--  <div class="list-con">-->
        <div class="divider-line" />
        <div class="type-item" v-for="(item, index) in disPlayTypeList" :key="item.areaId">
          <div class="item-title" @click="doClickItem(item.areaId)">{{ item.areaName }}</div>
          <div class="divider-line" />
        </div>
        <!--  </div>-->
      </TransitionGroup>

      <div class="list-scroll-btn" @click="rotateRight" v-if="showRightBtn">
        <img src="@/assets/images/home/<USER>" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup name="SpecialSubjectBookTab">
import { bookAreaList } from '@/api/basic/bookArea';
const emit = defineEmits(['subjectClicked']);
const router = useRouter();
const { proxy } = getCurrentInstance();
const props = defineProps({
  selfAnchorChain: {
    type: Boolean,
    default: false,
  },
});
const typeList = ref([]);
const disPlayTypeList = ref([]);
// 分页
const page = ref(1);
const pageSize = ref(5);
const totalPage = ref(2);

const showLeftBtn = computed(() => {
  return typeList.value.length > 0 && page.value > 1;
});
const showRightBtn = computed(() => {
  return typeList.value.length > 0 && page.value < totalPage.value;
});
function rotateLeft() {
  if (page.value > 1) {
    page.value--;
    disPlayTypeList.value = typeList.value.slice((page.value - 1) * pageSize.value, page.value * pageSize.value);
  }
}
function rotateRight() {
  if (page.value < totalPage.value) {
    page.value++;
    disPlayTypeList.value = typeList.value.slice((page.value - 1) * pageSize.value, page.value * pageSize.value);
  }
}
const doClickItem = (typeVal) => {
  if (props.selfAnchorChain) {
    emit('scrollToAnchor', typeVal);
  } else {
    router.push({
      path: '/special-subject-books-list',
      query: {
        type: typeVal,
      },
    });
  }
};
function initDisplayTypeList() {
  // 根据page.value 分页截取
  disPlayTypeList.value = typeList.value.slice((page.value - 1) * pageSize.value, page.value * pageSize.value);
  totalPage.value = Math.ceil(typeList.value.length / pageSize.value);
}
const getInFo = () => {
  bookAreaList({}).then((res) => {
    typeList.value = res.data;
    //
    //console.log('----typeList.value-------');
    // console.log(typeList.value);
    // typeList.value.push(res.data[0]);
    // typeList.value.push(res.data[1]);
    //
    initDisplayTypeList();
  });
};

getInFo();
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.special-text-book-con {
  width: 100%;
  @extend .base-flex-row;
  justify-content: center;
  align-items: center;
  .special-text-book-type-list {
    width: 65%;
    @extend .base-flex-row;
    justify-content: space-around;
    align-items: center;
    .list-con {
      position: relative;
    }
    .fade-enter-active,
    .fade-leave-active {
      transition: all 0.01s ease;
      position: absolute; // 防止布局抖动
    }
    .fade-enter-from {
      opacity: 0;
      transform: translateX(-20px);
    }
    .fade-leave-to {
      opacity: 0;
      transform: translateX(20px);
    }

    .list-scroll-btn {
      width: 35px;
      height: 35px;
      background: #ffffff;
      border-radius: 20px;
      border: 1px solid #e9eaea;
      display: flex;
      cursor: pointer;
      justify-content: center;
      align-items: center;
      margin-right: 20px;
      img {
        width: 9px;
        height: 15px;
      }
    }
    .list-scroll-btn:hover {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    .left-btn {
    }
    .right-btn {
      margin-left: 20px;
    }
    .list-con {
      flex: 1;
      @extend .base-flex-row;
      justify-content: center;
      align-items: center;
      .divider-line {
        width: 1px;
        height: 30px;
        border: 1px solid #e5e6e7;
      }
      .type-item {
        height: 60px;
        line-height: 60px;
        text-align: center;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        cursor: pointer;
        @extend .base-flex-row;
        justify-content: center;
        align-items: center;
        .item-title {
          width: 160px;
          margin-left: 30px;
          margin-right: 30px;
        }
      }
      .type-item:hover {
        color: #0966b4;
        opacity: 0.8;
      }
      .active {
        color: #0966b4;
      }
    }
  }
}
</style>
