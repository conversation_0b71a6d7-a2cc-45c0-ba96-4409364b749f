<template>
  <!-- 搜索内容 -->
  <div class="comp-container">
    <searchContentCard :paramsObjectData="paramsObjectData" />
  </div>
</template>

<script setup name="SearchResultComp">
import searchContentCard from '@/views/search/components/searchContentCard/index.vue';
const props = defineProps({
  paramsObjectData: Object,
});
const show = ref(false);
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';

.comp-container {
  width: 100%;
  @extend .base-flex-row;
  justify-content: flex-start;
  aligin-items: flex-start;
  padding-top: 32px;
  padding-bottom: 32px;
}
</style>
