<template>
  <div class="search-content-line-list">
    <div class="row-item" v-for="(item, index) in props.itemList" :item="item" :key="index">
      <div class="title-info">《规范标志与符号（第二版）》</div>
      <div class="author-info">主编：（美）密斯·赫拉（<PERSON><PERSON> Hor</div>
      <div class="content-info">
        标题12.3 英语分化与英语规范 小结样<br />
        标题12.3 英语分化与英语规范 小结样<br />
        标题12.3 英语分化与英语规范 小结样<br />
      </div>
      <div class="content-html" v-html="htmlData"></div>
    </div>
  </div>
</template>

<script setup name="SearchContentCardList">
import { ref } from 'vue';
const htmlData = ref('<h4>asdfad</h4>');
const props = defineProps({
  itemList: Array,
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.search-content-line-list {
  width: 100%;
  .row-item {
    width: 100%;
    @extend .base-flex-column;
    justify-content: flex-start;
    align-items: flex-start;
    .title-info {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 20px;
      color: #3f3f3f;
      line-height: 28px;
      text-align: left;
    }
    .author-info {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #c0c0c0;
      line-height: 28px;
      text-align: left;
    }
    .content-info {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight:400;
      font-size: 14px;
      color: #797878;
      line-height: 28px;
      text-align: left;
      font-style: normal;
    }
    border-bottom: 1px solid #e5e6e7;
  }
}
</style>
