<template>
  <div class="page-comp-con">
    <!--搜索页签 -->
    <div class="page-comp">
      <div class="top-tab-bar-con">
        <div :class="getTabSty(0)" @click="currentActiveTabIndex = 0">
          <div class="tab-label">多维交叉</div>
          <div class="bottom-line" />
        </div>
        <!-- <div :class="getTabSty(1)" @click="currentActiveTabIndex = 1">
          <div class="tab-label">高级搜索</div>
          <div class="bottom-line" />
        </div> -->
      </div>
      <div class="search-params-con">
        <!-- 01 多维交叉 -->
        <div class="params-cell-con" v-if="currentActiveTabIndex === 0">
          <div class="search-item-cell">
            <div class="label">书名</div>
            <div class="input-con">
              <el-input class="input-sty" v-model="queryCriteria.bookName" placeholder="请输入书名" />
            </div>
          </div>

          <div class="search-item-cell">
            <div class="label">作者/主编</div>
            <div class="input-con">
              <el-input class="input-sty" v-model="queryCriteria.authorValue" placeholder="请输入作者/主编" />
            </div>
          </div>

          <div class="search-item-cell">
            <div class="label">教育层次</div>
            <div class="input-con">
              <el-select class="input-sty" v-model="queryCriteria.topSubjectId" placeholder="请选择教育层次" size="large" @change="selSubject(queryCriteria.topSubjectId)">
                <el-option class="input-sty" v-for="item in educationOptions" :key="item.subjectId" :label="item.subjectName"
                  :value="item.subjectId" 
                  />
              </el-select>
            </div>
          </div>

          <div class="search-item-cell">
            <div class="label">学科分类</div>
            <div class="input-con">
              <el-select class="input-sty" v-model="queryCriteria.thirdSubjectId" placeholder="请选择学科分类" size="large" @change="selEducation(queryCriteria.thirdSubjectId)">
                <el-option v-for="item in classification" :key="item.subjectId" :label="item.subjectName" :value="item.subjectId" />
              </el-select>
            </div>
          </div>

          <div class="search-item-cell">
            <div class="label">标准书号</div>
            <div class="input-con">
              <el-select class="short-select-sty" v-model="queryCriteria.isbnType" placeholder="ISBN" size="large">
                <el-option v-for="item in isbnOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-input class="short-input-sty" v-model="queryCriteria.isbnOrIssn" placeholder="请输入内容" />
            </div>
          </div>

          <div class="search-item-cell">
            <div class="label">中图分类</div>
            <div class="input-con">
              <el-tree-select :check-strictly="true" class="clc-tree-sty" v-model="queryCriteria.bookType" :data="clcThreeList" filterable
                :props="{
                  key: 'typeId',
                  label: 'typeName',
                  value: 'typeId'
                }"
                clearable
                :render-after-expand="false" />
            </div>
          </div>

          <div class="search-item-cell">
            <div class="label">纸书定价</div>
            <div class="input-con">
              <el-input-number class="price-input-sty" v-model="queryCriteria.bookStartPrice" :precision="2"
                :step="0.01" :min="0" placeholder="请输入定价" />
              <span class="price-range-middle">至</span>
              <el-input-number class="price-input-sty" v-model="queryCriteria.bookEndprice" :precision="2" :step="0.01"
                :min="0" placeholder="请输入定价" />
              <span class="price-unit">元</span>
            </div>
          </div>

          <div class="search-item-cell">
            <div class="label">出版日期</div>
            <div class="date-picker-input-con">
              <el-date-picker class="date-range-input-sty" v-model="queryCriteria.pushYearRange" type="daterange"
                unlink-panels range-separator="至" start-placeholder="请选择年份" end-placeholder="请选择年份"
                :shortcuts="shortcuts" />
              <span class="price-unit">年</span>
              <div class="close-year-btn" @click="fieldYearRange(1)">近一年</div>
              <div class="close-year-btn" @click="fieldYearRange(5)">近五年</div>
            </div>
          </div>
        <!--招标结束，删除-->
          <!-- <div class="search-item-cell">
          <div class="label">出版社</div>
            <div class="input-con">
              <el-select class="input-sty" v-model="queryCriteria.publishingHouse" placeholder="请选择出版社" size="large">
                <el-option v-for="item in publishingHouse" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </div> -->
        </div>

        <!-- 02 高级搜索（sql like） -->
        <!-- <div class="params-cell-con" v-if="currentActiveTabIndex === 1">
          <div class="search-adv-item-cell" v-for="(item, index) in searchAdvItemCellList" :key="index">
            <div class="add-sub-btn" v-if="index === searchAdvItemCellList.length - 1">
              <div class="add-btn" @click="handleAddSearchAdvItemCell"><span>+</span></div>
              <div class="sub-btn" @click="handleDelSearchAdvItemCell(index)"><span>-</span></div>
            </div>
            <div class="input-con">
              <el-select class="short-select-sty" v-model="item.logicalOperator" placeholder="ISBN" size="large">
                <el-option v-for="option in logicalOperatorOptions" :key="'logical-' + option.value"
                  :label="option.label" :value="option.value" />
              </el-select>
              <el-select class="short-select-sty" v-model="item.filterField" placeholder="ISBN" size="large">
                <el-option v-for="option in filterFieldOptions" :key="'filter-' + option.filterField"
                  :label="option.label" :value="option.value" />
              </el-select>
              <el-input class="short-input-sty" v-model="item.inputValue" placeholder="请输入内容" />
              <el-select class="short-select-sty" v-model="item.matchType" placeholder="请选择" size="large">
                <el-option v-for="option in matchTypeOptions" :key="'match-' + option.value" :label="option.label"
                  :value="option.value" />
              </el-select>
            </div>
          </div>
        </div> -->
        <!-- 03 底部按钮区 -->
        <div class="bottom-btn-con">
          <div class="reset-btn" @click="resetQueryCriteria">重置条件</div>
          <div class="search-btn" @click="doAdvSearch">搜索</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="SearchAdvanced">
import { listDutpSubjectEducation } from '@/api/basic/subject';
import { miSearchEducation, listBookTypeEducation } from '@/api/openApi/openApi';
import { onMounted } from 'vue';
import { ElMessage } from 'element-plus';
const router = useRouter();
const currentActiveTabIndex = ref(0);
const clcThreeList = ref([]);
const queryCriteria = ref(
      {
        bookName: '',
        authorValue: '',
        pushYearRange: [],
        isbnType: 1,
        topSubjectId: '',
        thirdSubjectId: '',
        isbnOrIssn: '',
        bookType: '',
        bookStartPrice: 0,
        bookEndprice: 0
    }
);
const educationOptions = ref([
]);
const classification = ref([
]);
const publishingHouse = ref([
  { label: '理工大学出版社', value: 1 }
]);
const logicalOperatorOptions = ref([
  { label: '并且', value: 1 },
  { label: '或者', value: 2 },
  { label: '不包含', value: 3 },
]);
const filterFieldOptions = ref([
  { label: '书名', value: 1 },
  { label: '作者', value: 2 },
  { label: '教育层次', value: 3 },
  { label: '学科分类', value: 4 },
  { label: '作者/编辑', value: 5 },
  { label: 'ISBN', value: 6 },
  { label: 'ISSN', value: 7 },
  { label: '中图分类', value: 8 },
  { label: '纸书定价', value: 9 },
  { label: '出版日期', value: 10 },
]);
const isbnOptions = ref([
  { label: 'ISBN', value: 1 },
  { label: 'ISSN', value: 2 },
]);
const matchTypeOptions = ref([
  { label: '精准', value: 1 },
  { label: '模糊', value: 2 },
]);
const searchAdvItemCellList = ref([
  {
    logicalOperator: 1,
    filterField: 1,
    inputValue: '',
    matchType: 1,
  },
]);
const tempeOptionsData = ref([]);
function handleDelSearchAdvItemCell(index) {
  if (searchAdvItemCellList.value.length > 1) {
    searchAdvItemCellList.value.splice(index, 1);
  }
  return;
}
function fieldYearRange(year) {
  const now = new Date();
  const otherYear = new Date(now.getFullYear() - year, now.getMonth(), now.getDate());
  if (year > 0) {
    if (year === 1) {
      const startOfYear = new Date(now.getFullYear(), 0, 1);
      queryCriteria.value.pushYearRange = [startOfYear, now];
    } else {
      queryCriteria.value.pushYearRange = [otherYear, now];
    }
  } else {
    // 将来预计发布的日期
    queryCriteria.value.pushYearRange = [now, otherYear];
  }
}
function handleAddSearchAdvItemCell() {
  if (searchAdvItemCellList.value.length < filterFieldOptions.value.length)
    searchAdvItemCellList.value.push({
      logicalOperator: 1,
      filterField: 1,
      inputValue: '',
      matchType: 1,
    });
}
function getTabSty(tabNum) {
  if (tabNum === currentActiveTabIndex.value) {
    return 'top-tab-bar-active';
  }
  return 'top-tab-bar';
}

function toLogin() {
  router.push({ path: '/login' });
}

function resetQueryCriteria() {
  if(currentActiveTabIndex.value == 0){
    localStorage.removeItem('queryCriteria');
    queryCriteria.value =
      {
        bookName: '',
        authorValue: '',
        pushYearRange: [],
        isbnType: 1,
        topSubjectId: '',
        thirdSubjectId: '',
        isbnOrIssn: '',
        bookType: '',
        bookStartPrice: 0,
        bookEndprice: 0
    }
  } else {
    localStorage.removeItem('searchAdvItemCellList');
    searchAdvItemCellList.value = [
      {
        logicalOperator: 1,
        filterField: 1,
        inputValue: '',
        matchType: 1,
      },
    ];
  }
}
// 从 localStorage 读取 检索条件
function loadQueryCriteriaFromCache() {
  const queryCriteriaItem = localStorage.getItem('queryCriteria');
  const searchAdvItemCellListItem = localStorage.getItem('searchAdvItemCellList');
  if (queryCriteriaItem) {

    queryCriteria.value = JSON.parse(queryCriteriaItem);

  } else {
    queryCriteria.value =
      {
        bookName: '',
        authorValue: '',
        pushYearRange: [],
        isbnType: 1,
        topSubjectId: '',
        thirdSubjectId: '',
        isbnOrIssn: '',
        bookType: '',
        bookStartPrice: 0,
        bookEndprice: 0
    }
  }
  if (searchAdvItemCellListItem) {

    searchAdvItemCellList.value = JSON.parse(searchAdvItemCellListItem);

  }
}
function doAdvSearch() {
  // 查询
  let param = 0;
  
  // 存储 检索条件 到 localStorage
  if (currentActiveTabIndex.value == 0) {
    // 检查多维交叉搜索是否有输入条件
    const criteria = queryCriteria.value;
    const hasInput = criteria.bookName.trim() || 
                     criteria.authorValue.trim() || 
                     criteria.topSubjectId || 
                     criteria.thirdSubjectId || 
                     criteria.isbnOrIssn.trim() || 
                     criteria.bookType || 
                     (criteria.bookStartPrice > 0) || 
                     (criteria.bookEndprice > 0) || 
                     (criteria.pushYearRange && criteria.pushYearRange.length > 0);
    
    if (!hasInput) {
      ElMessage.warning('请输入至少一个检索条件');
      return;
    }
    
    localStorage.setItem('queryCriteria', JSON.stringify(queryCriteria.value));
  } else {
    param = 1;
    // 检查高级搜索是否有输入条件
    const hasInput = searchAdvItemCellList.value.some(item => item.inputValue.trim());
    
    if (!hasInput) {
      ElMessage.warning('请输入至少一个检索条件');
      return;
    }
    
    localStorage.setItem('searchAdvItemCellList', JSON.stringify(searchAdvItemCellList.value));
  }
  
  window.open(`/search-result?param=${param}`, '_blank');
}
const shortcuts = ref([
  {
    text: '近一年',
    value: [new Date(), new Date()],
  },
  {
    text: '近五年',
    value: () => {
      const end = new Date();
      const start = new Date(new Date().setFullYear(new Date().getFullYear() - 5));
      return [start, end];
    },
  },
]);
const getDutpSubject = () => {
  listDutpSubjectEducation().then(res => {
    tempeOptionsData.value = res.data;
    educationOptions.value = res.data[1];
    classification.value = res.data[3];
  })
}
const selSubject = (subjectId) => {
  classification.value = tempeOptionsData.value[3];
  if(subjectId !== null){
    const subject2 = tempeOptionsData.value[2].filter(item => item.parentId === subjectId);
    const filteredClassification = [];
    subject2.forEach(item => {
      classification.value.forEach(option => {
        if (option.parentId === item.subjectId) {
          filteredClassification.push(option);
        }
      });
    });
    classification.value = filteredClassification;
    }
}
const selEducation = (subjectId) => {
  educationOptions.value = tempeOptionsData.value[1];
  // 当选择教育层次时，更新学科分类选项
  if (subjectId !== null) {
    const subject3 = tempeOptionsData.value[3].find(item => item.subjectId === subjectId);
    // 获取第二层数据（索引为2）
    const subject2 = tempeOptionsData.value[2].filter(item => item.subjectId === subject3.parentId);
    // 筛选出与第二层关联的第三层选项
    const filteredClassification = [];
    subject2.forEach(item => {
      educationOptions.value.forEach(option => {
        if (option.subjectId === item.parentId) {
          filteredClassification.push(option);
        }
      });
    });
    
    // 更新学科分类选项
    educationOptions.value = filteredClassification;
  }
}
const getListType = () => {
  const parm = {
    pageNum: 1,
    pageSize: 99999,
  }
  listBookTypeEducation(parm).then(res => {
    clcThreeList.value = res.data;
  })
}
onMounted(() => {
  loadQueryCriteriaFromCache();
  getDutpSubject();
  getListType();
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index';

.page-comp-con {
  width: 100%;
  min-height: 100%;
  padding-top: 50px;
  padding-bottom: 50px;
  background: linear-gradient(180deg, #c5deff 0%, #f5f5f5 100%);
  @extend .base-flex-row;
  justify-content: center;
  align-items: flex-start;

  .page-comp {
    width: 58%;
    min-height: 781px;

    .top-tab-bar-con {
      width: 100%;
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: flex-end;

      .top-tab-bar-base {
        margin-right: 20px;
        width: calc(182px - 10px);
        height: 56px;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
        background: #e9ebee;
        text-align: center;
        font-size: 16px;
        color: #999;
        position: relative;
        cursor: pointer;
        transition: all 0.1s ease-in-out;
        @extend .base-flex-column;
        justify-content: flex-end;
        align-items: center;

        .tab-label {
          margin-bottom: 14px;
        }

        .bottom-line {
          width: 68px;
          height: 4px;
        }
      }

      .top-tab-bar-transition {
        transition: all 0.1s ease-in-out;
      }

      .top-tab-bar {
        @extend .top-tab-bar-base;
        background: #e9ebee;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        text-align: center;
      }

      .top-tab-bar-active {
        @extend .top-tab-bar-base;
        @extend .top-tab-bar-transition;
        background: #ffffff;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #0966b4;
        text-align: center;

        .bottom-line {
          background: #0966b4;
        }
      }

      .top-tab-bar::after,
      .top-tab-bar-active::after {
        content: '';
        position: absolute;
        top: 5px;
        right: -20px;
        /* 调整斜度突出长度 */
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 51px 0 0 22px;
        /* 高度与标签高度一致 */
        border-color: transparent transparent transparent #e9ebee;
        /* 左侧颜色填充 */
        @extend .top-tab-bar-transition;
      }

      .top-tab-bar-active::after {
        border-color: transparent transparent transparent #ffffff;
        /* 激活标签的颜色 */
      }
    }

    .search-params-con {
      width: 100%;
      min-height: calc(781px - 130px);
      overflow: auto;
      background-color: #ffffff;
      border-radius: 0px 8px 8px 8px;
      @extend .base-flex-column;
      justify-content: flex-start;
      align-items: center;

      .params-cell-con {
        width: 90%;
        margin-top: 35px;
        padding-bottom: 14px;
        border-bottom: 2px solid #e5e6e7;

        .search-item-cell {
          width: 100%;
          margin-bottom: 18px;
          @extend .base-flex-row;
          justify-content: flex-start;
          align-items: center;

          .label {
            width: 85px;
            height: 20px;
            text-align: right;
            margin-left: 70px;
            margin-right: 22px;
          }

          .input-con {
            width: 100%;
            @extend .base-flex-row;
            justify-content: flex-start;
            align-items: center;

            .input-sty {
              width: 540px;
              height: 40px;
              border-radius: 4px;
            }

            .short-select-sty {
              width: 120px;
              height: 40px;
              margin-right: 18px;
            }

            .short-input-sty {
              width: 405px;
              height: 40px;
            }

            .price-input-sty {
              width: 230px;
              height: 40px;
              margin-right: 21px;
            }

            .clc-tree-sty {
              width: 293px;

              :deep(.el-select__wrapper) {
                min-height: 40px !important;
              }
            }

            .price-range-middle {
              margin-right: 21px;
            }

            .price-unit {
              /*margin-right: 22px;*/
            }
          }

          .date-picker-input-con {
            width: 100%;
            @extend .base-flex-row;
            justify-content: flex-start;
            align-items: center;

            :deep(.el-range-editor.el-input__wrapper) {
              max-width: 520px !important;
              min-height: 40px !important;
              border-radius: 4px;
            }

            .date-range-input-sty {
              /*
              max-width: 540px !important;
              min-height: 40px !important;
              border-radius: 4px;*/
            }

            .price-unit {
              margin-left: 21px;
              margin-right: 22px;
            }

            .close-year-btn {
              width: 96px;
              height: 40px;
              border-radius: 20px;
              border: 1px solid #e5e6e7;
              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 400;
              font-size: 14px;
              color: #333333;
              line-height: 40px;
              text-align: center;
              margin-right: 18px;
              cursor: pointer;
            }

            .close-year-btn:last-child {
              margin-right: 0;
            }

            .close-year-btn:hover {
              opacity: 0.8;
              border: 1px solid #cccdce;
              background: #d8e5fa;
              transition:
                transform 0.1s ease-in-out,
                opacity 0.1s ease-in-out;
            }
          }
        }

        .search-adv-item-cell {
          width: 100%;
          margin-bottom: 18px;
          transition:
            transform 0.1s ease-in-out,
            opacity 0.1s ease-in-out;
          transform: translateY(0);
          opacity: 1;
          @extend .base-flex-row;
          justify-content: flex-end;
          align-items: center;

          .add-sub-btn {
            margin-right: 10px;
            @extend .base-flex-row;
            justify-content: flex-start;
            justify-content: center;

            .add-btn {
              width: 26px;
              height: 26px;
              border-radius: 3px;
              text-align: center;
              color: #ffffff;
              background-color: #2255a2;
              margin-right: 20px;
              font-size: 26px;
              @extend .base-flex-row;
              justify-content: center;
              align-items: center;
              cursor: pointer;
            }

            .add-btn:hover {
              opacity: 0.8;
            }

            .sub-btn {
              width: 26px;
              height: 26px;
              border-radius: 3px;
              text-align: center;
              color: #ffffff;
              background-color: #7a8796ff;
              font-size: 36px;
              @extend .base-flex-row;
              justify-content: center;
              align-items: center;

              span {
                margin-bottom: 5px;
              }

              cursor: pointer;
            }

            .sub-btn:hover {
              opacity: 0.8;
            }
          }

          .input-con {
            width: 88%;
            @extend .base-flex-row;
            justify-content: flex-end;
            align-items: center;

            .input-sty {
              width: 540px;
              height: 40px;
              border-radius: 4px;
              /*border: 1px solid black;*/
            }

            .short-select-sty {
              width: 120px;
              height: 40px;
              margin-right: 18px;
            }

            .short-input-sty {
              width: 405px;
              height: 40px;
              margin-right: 20px;
            }
          }
        }
      }

      .bottom-btn-con {
        width: 100%;
        height: 120px;
        @extend .base-flex-row;
        justify-content: center;
        align-items: center;

        .reset-btn {
          width: 56px;
          height: 20px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          line-height: 20px;
          text-align: left;
          margin-right: 72px;
          cursor: pointer;
        }

        .reset-btn:hover {
          opacity: 0.8;
        }

        .search-btn {
          width: 160px;
          height: 40px;
          background: #f1914b;
          border-radius: 8px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 40px;
          text-align: center;
          cursor: pointer;
        }

        .search-btn:hover {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
