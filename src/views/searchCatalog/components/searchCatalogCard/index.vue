<template>
  <div class="comp-card-con">
    <div class="search-content-con">
      <div class="search-content-header">
        <div class="search-content-label">
          <span class="search-content-label-text">
            找到约 <span>{{ total }}</span
            >条搜索结果
          </span>
        </div>
        <div class="order-sort-option-con"></div>
      </div>
      <div class="search-content-line-list-con">
        <searchCatalogLineList :itemList="itemList" />
      </div>
      <div class="page-con">
        <div class="demonstration">共{{ total }}条</div>
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          v-model:current-page="pageNum"
          v-model:page-size="pageSize"
          @change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup name="SearchContentCard">
import searchCatalogLineList from "@/views/searchCatalog/components/searchCatalogLineList/index.vue";
import { homepageChapterSearch } from "@/api/openApi/openApi";
import { ref, watch, computed } from "vue";
import { useRoute } from "vue-router";
// 获取当前路由对象
const route = useRoute();
const contentListType = ref("card"); // 'card' or 'list'
const sortByTimeUpActive = ref(true);
const sortByTimeDownActive = ref(false);
const sortByPriceUpActive = ref(true);
const sortByPriceDownActive = ref(false);
const sortByReadNumUpActive = ref(true);
const sortByReadNumDownActive = ref(false);
const itemList = ref([]);
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
function doClickItem(chItem, index, chIndex) {
  chItem.isChecked = !chItem.isChecked;
}
function contentListTypeHandler() {
  contentListType.value = contentListType.value === "card" ? "list" : "card";
}
const getContentListTypeLabel = computed(() => {
  return contentListType.value === "card" ? "卡片视图" : "列表视图";
});

function aboutOptionClick() {}
function sortByTimeOptionClick() {
  sortByTimeUpActive.value = !sortByTimeUpActive.value;
  sortByTimeDownActive.value = !sortByTimeDownActive.value;
}
function sortByPriceOptionClick() {
  sortByPriceUpActive.value = !sortByPriceUpActive.value;
  sortByPriceDownActive.value = !sortByPriceDownActive.value;
}
function sortByReadNumOptionClick() {
  sortByReadNumUpActive.value = !sortByReadNumUpActive.value;
  sortByReadNumDownActive.value = !sortByReadNumDownActive.value;
}
function getListChapter() {
  const parm = {
    bookName: route.query.searchKey,
    authorValue: route.query.searchKey,
    isbn: route.query.searchKey,
    chapterName: route.query.searchKey,
    pageSize: pageSize.value,
    pageNum: pageNum.value,
  };
  homepageChapterSearch(parm).then((res) => {
    if (res.code === 200) {
      itemList.value = res.rows;
      total.value = res.total;
    }
  });
}
getListChapter();
watch(
  () => route.query.searchKey,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      getListChapter();
    }
  }
);
function handleSizeChange(val) {
  pageNum.value = val;
  getListChapter();
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/index.scss";
.comp-card-con {
  width: 100%;
  @extend .base-flex-row;
  justify-content: center;
  align-items: flex-start;
  .search-content-con {
    width: 1400px;
    min-height: 286px;
    margin-bottom: 44px;
    @extend .base-flex-column;
    justify-content: flex-start;
    align-items: center;
    .search-content-header {
      width: 100%;
      margin-bottom: 42px;
      @extend .base-flex-row;
      justify-content: space-between;
      align-items: center;
      .search-content-label {
        .search-content-label-text {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #333333;
          text-align: center;
          span {
            color: #316eb7ff;
          }
        }
      }
      .order-sort-option-con {
        @extend .base-flex-row;
        justify-content: flex-end;
        align-items: center;
        .sort-about-option {
          margin-right: 20px;
          cursor: pointer;
          @extend .base-flex-row;
          justify-content: center;
          align-items: center;
          img {
            width: 16px;
            height: 16px;
            margin-right: 8px;
          }
          span {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            text-align: center;
          }
        }
        .sort-about-option:hover {
          opacity: 0.8;
          span {
            color: #2162b1ff;
          }
        }

        .sort-arrow-option {
          margin-right: 20px;
          cursor: pointer;
          @extend .base-flex-row;
          justify-content: center;
          align-items: center;
          .img-arrow {
            margin-right: 6px;
            @extend .base-flex-column;
            justify-content: center;
            align-items: center;
            .up-arrow {
              width: 0;
              height: 0;
              border-left: 4.5px solid transparent; /* 左边框透明 */
              border-right: 4.5px solid transparent; /* 右边框透明 */
              border-top: 0 solid transparent; /* 上边框透明，实际上不需要设置，因为高度为0 */
              border-bottom: 5px solid #919497; /* 下边框设置三角形的高度和颜色 */
              margin-bottom: 3px;
            }
            .down-arrow {
              width: 0;
              height: 0;
              border-left: 4.5px solid transparent; /* 左边框透明 */
              border-right: 4.5px solid transparent; /* 右边框透明 */
              border-bottom: 0 solid transparent; /* 底边框透明，实际上不需要设置，因为高度为0 */
              border-top: 5px solid #919497; /* 上边框设置三角形的高度和颜色 */
            }
            .active-up {
              border-bottom: 5px solid #2768a5;
            }
            .active-down {
              border-top: 5px solid #2768a5;
            }
          }
          span {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            text-align: center;
          }
        }
        .sort-arrow-option:hover {
          opacity: 0.8;
          span {
            color: #2162b1ff;
          }
        }
        .divider-line {
          width: 1px;
          height: 12px;
          border: 1px solid #999999;
          margin-right: 20px;
        }
        .show-type-option {
        }
      }
    }
    .search-content-card-list-con {
      width: 100%;
      transition: all 0.3s ease-in-out;
    }
    .search-content-line-list-con {
      width: 100%;
      transition: all 0.3s ease-in-out;
    }
    .page-con {
      width: 100%;
      @extend .base-flex-row;
      justify-content: center;
      align-items: center;
      margin-top: 40px;
      .demonstration {
        margin-right: 40px;
      }
    }
  }
}
</style>
