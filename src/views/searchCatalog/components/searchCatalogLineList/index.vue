<template>
  <div class="search-content-line-list">
    <div class="row-item" v-for="(item, index) in props.itemList" :item="item" :key="index">
      <div class="title-info">{{item.bookName}}</div>
      <div class="author-info">{{item.authorLabel}}：{{item.authorValue}}</div>
      <div class="content-info" v-for="(data, index) in item.chapterList" :key="chapterIndex">
        <el-link :underline="false"  target="_blank" :disabled="data.free === '1' ? true:false" @click="goto(item)">{{ data.chapterName }}</el-link>
      </div>
    </div>
    <el-divider/>
  </div>
</template>

<script setup name="SearchContentCardList">
import { gotoReader } from '@/utils/reader'
const router = useRouter();
function goto(item) {
  gotoReader(item.bookId);
}
const props = defineProps({
  itemList: Array,
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/index.scss';
.search-content-line-list {
  width: 100%;
  .row-item {
    width: 100%;
    @extend .base-flex-column;
    justify-content: flex-start;
    align-items: flex-start;
    .title-info {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 20px;
      color: #3f3f3f;
      line-height: 28px;
      text-align: left;
      margin: 10px 0px 10px 0px;
    }
    .author-info {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #c0c0c0;
      line-height: 28px;
      text-align: left;
    }
    .content-info {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight:400;
      font-size: 14px;
      color: #3f3f3f;
      line-height: 28px;
      text-align: left;
      font-style: normal;
      margin-bottom: 10px;
    }
    border-bottom: 1px solid #e5e6e7;
  }
}
</style>
