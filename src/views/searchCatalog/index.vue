<template>
  <div class="page-nav-container">
    <div class="search-con">
      <searchBar @search="doSearch" :searchKey = 'searchKey' :isNoShadow="true" />
    </div>
    <!-- 01 特殊教材tab(国家优秀教材， -->
    <div class="special-text-book-tab-con">
      <specialSubjectBookTab @subjectClicked="doClickSubject" />
    </div>
    <!-- 02 搜索条件卡 -->
    <!-- <div class="search-params-card-con">
      <searchParamsCard @paramsChanged="paramsChangedHandler" />
    </div> -->
    <!-- 03 搜索内容 -->
    <div class="search-content-card-con">
      <searchCatalogCard/>
    </div>
  </div>
</template>

<script setup name="SearchCatalogPage">
import { onMounted, ref } from 'vue';

import searchBar from '@/views/home/<USER>/SearchBar/index.vue';
import specialSubjectBookTab from '@/views/search/components/specialSubjectBookTab/index.vue';
// import searchParamsCard from '@/views/search/components/searchParamsCard/index.vue';
import searchCatalogCard from '@/views/searchCatalog/components/searchCatalogCard/index.vue';
import { useRouter } from 'vue-router';
const router = useRouter();
const paramsObjectData = ref();
const searchKey = ref('');
function doClickSubject(typeVal) {
  router.push({
    path: '/special-subject-books-list',
    query: {
      type: typeVal,
    },
  });
}
function paramsChangedHandler(paramsObject) {
  paramsObjectData.value = paramsObject;
}
function toLogin() {
  router.push({ path: '/login' });
}

const doSearch = (queryCriteria) => {
};
onMounted(() => {
  // 获取路由参数
  const query = router.currentRoute.value.query;
  searchKey.value = query.searchKey;
});
</script>

<style scoped lang="scss">
@import '@/assets/styles/index.scss';
.search-page-sty {
  width: 100%;
  .page-nav-container {
    width: 100%;
    .nav-con {
      width: 100%;
    }
    .search-con {
      width: 100%;
    }
    .special-text-book-tab-con {
      width: 100%;
      margin-bottom: 19px;
    }
    .search-params-card-con {
      width: 100%;
      margin-bottom: 13px;
    }
    .search-content-card-con {
      width: 100%;
      margin-bottom: 13px;
    }
  }
}
</style>
