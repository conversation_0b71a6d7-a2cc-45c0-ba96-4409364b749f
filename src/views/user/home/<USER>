<template>
  <div class="home-container-sty">
    <!-- 头部导航栏 -->
    <div class="user-home-head-con">
      <headNavComp @toLogin="toLogin" :showBackground="true" />
    </div>
    <!-- 主体内容 -->
    <div class="content-body-con">
      <div class="content-title">
        <span style="margin-right: 30px">个人中心</span>
        <div
          v-if="userInfo?.userType === '2'"
          style="display: flex; align-items: center"
        >
          <img src="@/assets/icons/svg/authentication.svg" /><span
            style="color: #ff8a00; font-weight: 400"
            >认证教师</span
          >
        </div>
        <div
          v-if="userInfo?.userType === '1'"
          style="display: flex; align-items: center"
        >
          <img src="@/assets/icons/svg/doctorialHat.svg" /><span
            style="color: #1059a5; font-weight: 400"
            >学生</span
          >
        </div>
      </div>
      <div class="content-body">
        <!-- 左侧菜单栏 -->
        <div class="content-left-con">
          <!-- 顶部菜单区域 -->
          <el-menu
            class="el-l-menu"
            mode="vertical"
            router
            :default-active="$route.path"
          >
            <div class="left-top-menu-con">
              <template v-for="(item, index) in leftTopMenuList" :key="index">
                <router-link :to="`${item.path}`">
                  <el-menu-item
                    :index="`${item.path}`"
                    style="height: 45px; margin-left: 15px"
                  >
                    <el-icon>
                      <img :src="getIconUrl(item.icon)" />
                    </el-icon>
                    <span>{{ item.name }}</span>
                  </el-menu-item>
                </router-link>
              </template>
            </div>
          </el-menu>
          <!-- 中部菜单区域 -->
          <el-menu
            class="el-l-menu"
            mode="vertical"
            router
            :default-active="$route.path"
          >
            <div class="left-middle-menu-con">
              <template
                v-for="(item, index) in leftMiddleMenuList"
                :key="index"
              >
                <router-link :to="`${item.path}`">
                  <el-menu-item
                    :index="`${item.path}`"
                    style="height: 20px; margin-left: 15px"
                  >
                    <el-icon>
                      <img :src="getIconUrl(item.icon)" />
                    </el-icon>
                    <span>{{ item.name }}</span>
                  </el-menu-item>
                </router-link>
              </template>
            </div>
          </el-menu>
          <!-- 底部菜单区域 -->
          <el-menu
            class="el-l-menu"
            mode="vertical"
            router
            :default-active="$route.path"
          >
            <div class="left-bottom-menu-con">
              <template
                v-for="(item, index) in leftBottomMenuList"
                :key="index"
              >
                <router-link :to="`${item.path}`">
                  <el-menu-item
                    :index="`${item.path}`"
                    style="height: 20px; margin-left: 15px"
                  >
                    <el-icon>
                      <img :src="getIconUrl(item.icon)" />
                    </el-icon>
                    <span>{{ item.name }}</span>
                  </el-menu-item>
                </router-link>
              </template>
            </div>
          </el-menu>
        </div>
        <!-- 右侧内容区域，通过 router-view 动态加载组件内容 -->
        <router-view class="router-view" />
      </div>
    </div>
    <footComp />
  </div>
</template>
<script setup name="HomePage">
import headNavComp from "@/views/home/<USER>/headNavComp/index.vue";
import footComp from "@/views/home/<USER>/footComp/index.vue";
import { ref, onMounted, onBeforeMount } from "vue";
import useUserStore from "@/store/modules/user.js";
import { useRouter } from "vue-router";
const router = useRouter();

const userStore = useUserStore();
const userInfo = ref();
// 定义顶部菜单列表，包含菜单项名称和路径
const leftTopMenuList = ref([
  { name: "我的消息", icon: "my-message", path: "/my-message" },
  { name: "基本信息", icon: "basic-information", path: "/basic-information" },
  { name: "我的书架", icon: "purchase-history", path: "/purchase-history" },
  { name: "我的收藏", icon: "collect", path: "/collect" },
  { name: "浏览历史", icon: "browsing-history", path: "/browsing-history" },
  {
    name: "我的购书码",
    icon: "book-purchase-code",
    path: "/book-purchase-code",
  },
  { name: "我的订单", icon: "my-order", path: "/my-order" },
  // { name: '我的慧点', icon: 'my-smartdot', path: '/error-correction' }, // 二期
  // { name: '学习报告', icon: 'book-purchase-code', path: '/basic-information' }, // 二期
  { name: "我的纠错", icon: "error-correction", path: "/error-correction" },
  { name: "发票中心", icon: "fapiaoguanli", path: "/invoice-header" },
]);

// 定义中部菜单列表
const leftMiddleMenuList = ref([
  // { name: '我的班级', icon: 'my-class', path: '/basic-information' }, // 二期
  { name: "身份认证", icon: "identity", path: "/identity" },
  {
    name: "申请历史",
    icon: "application-history",
    path: "/application-history",
  },
]);

// 定义底部菜单列表
const leftBottomMenuList = ref([
  { name: "意见反馈", icon: "feedback", path: "/feedback" },
]);
const toLogin = () => {};
onBeforeMount(() => {});
onMounted(async () => {
  const res = await userStore.getInfo();
  userInfo.value = res.user;
});
const getIconUrl = (iconName) => {
  return new URL(`../../../assets/icons/svg/${iconName}.svg`, import.meta.url)
    .href;
};
</script>

<style scoped lang="scss">
@import "@/assets/styles/index.scss";

.list-item {
  padding: 10px;
  margin: 5px 0;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  cursor: move;
  /* 确保鼠标指针显示为拖动图标 */
}

.home-container-sty {
  width: 100%;
  @extend .base-flex-column;
  justify-content: flex-start;
  align-items: center;

  .user-home-head-con {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
  }

  .content-body-con {
    min-height: calc(100vh - 300px);
    @extend .base-comp-card;
    margin-bottom: 23px;

    .content-title {
      width: 100%;
      margin-top: 100px;
      margin-bottom: 25px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 18px;
      color: #333333;
      line-height: 25px;
      text-align: right;
      font-style: normal;
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: center;
    }

    .content-body {
      display: flex;
      height: 100%;
      /* 确保内容区域高度为 100% */
      @extend .base-flex-row;
      justify-content: flex-start;
      align-items: flex-start;

      .content-left-con {
        width: 250px;
        /* 设置左侧菜单栏的宽度 */
        background-color: #f0f0f0;
        /* 背景颜色可以根据需求调整 */
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        /* 如果内容超出容器高度，允许滚动 */

        .left-top-menu-con {
          width: 191px;
          max-height: 495px;
          background: #f0f0f0;
          @extend .base-flex-column;
          justify-content: flex-start;
          align-items: flex-start;
          margin-bottom: 5px;
          flex: 1;
          /* 让每个菜单区域根据内容自动分配空间 */
        }

        .left-middle-menu-con {
          width: 191px;
          height: 80px;
          background: #f0f0f0;
          @extend .base-flex-column;
          justify-content: space-around;
          align-items: flex-start;
          margin-bottom: 5px;
          flex: 1;
          /* 让每个菜单区域根据内容自动分配空间 */
        }

        .left-bottom-menu-con {
          width: 191px;
          height: 45px;
          background: #f0f0f0;
          @extend .base-flex-column;
          justify-content: space-around;
          align-items: flex-start;
          margin-bottom: 5px;
          flex: 1;
          /* 让每个菜单区域根据内容自动分配空间 */
        }

        .menu-item {
          width: 100px;
          height: 16px;
          margin-left: 29px;
          margin-bottom: 33px;
          @extend .base-flex-row;
          justify-content: flex-start;
          align-items: center;

          .menu-icon {
            width: 16px;
            height: 16px;
            margin-right: 14px;
          }

          span {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            line-height: 50px;
            text-align: left;
            font-style: normal;
          }
        }

        .menu-item:nth-child(1) {
          margin-top: 30px;
        }
      }
    }
  }
}

/* 确保父容器高度为 100% */
html,
body,
#app {
  height: 100%;
  margin: 0;
}

.router-view {
  min-height: calc(100vh - 400px);
  flex: 1;
  /* 让右侧内容区域占据剩余空间 */
  padding: 20px;
  /* 根据需要添加内边距 */
}

.el-l-menu {
  background-color: var(--el-menu-bg-color);
  border-right: 1px solid var(--el-menu-border-color);
  box-sizing: border-box;
  list-style: none;
  margin: 0;
  padding-left: 0;
  position: initial;
}
</style>
