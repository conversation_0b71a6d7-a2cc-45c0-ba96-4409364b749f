<template>
  <el-dialog
  v-model="dialogVisible"
  title="智慧教育云平台——注销须知"
    width="50%"
    :before-close="handleClose"
  >
    <div class="dialog-content">
      <p>《智慧教育云平台用户中心注销须知》</p>
      <p>亲爱的用户：</p>
      <p>您在申请注销过程中点击同意后，应该认真阅读《智慧教育云平台用户注销须知》（以下称《注销须知》）。</p>
      <p>《注销须知》中包含注销所需的相关要求和注意事项，请您仔细阅读，理解并接受《注销须知》的全部内容。阅读《注销须知》的过程中，若您有任何疑问，请通过*************或客服电话联系。</p>
      <p>【特别提示】当您按照注销页面提示填写信息，阅读并同意《注销须知》的过程中，若您有任何疑问，请通过*************或客服电话联系。</p>
      <p>1、如果您同意注销申请并完成全部注销程序后，即表示您已充分阅读、理解并接受《注销须知》的全部内容。在阅读《注销须知》的过程中，若您有任何疑问，请通过*************或客服电话联系。</p>
      <p>2、账号一旦被注销将不可恢复，请确保注销操作前备份好账号内的所有信息和数据。</p>
      <p>3、在此注销期间，如果您的账户涉及违规行为，账号仍然会受到暂停使用等处罚，且注销操作将不予处理。</p>
      <p>4、注销账号后，您将无法再使用该账号，且将无法访问与该账号相关的任何内容信息，包括但不限于：个人资料、历史记录、购买记录、浏览记录等。</p>
      <p>5、注销本账户并不代表本账户注销后与本账户相关的其他账户操作已经完全清除，相关数据仍然可能存储在云平台内。</p>
      <p>如果您同意上述条款并希望继续注销，请点击确认按钮；如果您不同意任何条款，请停止注销操作。</p>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue';

const dialogVisible = ref(false);

const handleClose = () => {
  dialogVisible.value = false;
};

defineExpose({
  open: () => {
    dialogVisible.value = true;
  }
});
</script>

<style scoped>
.dialog-content {
  font-size: 14px;
  line-height: 1.8;
  color: #333;
  padding: 10px;
}
</style>