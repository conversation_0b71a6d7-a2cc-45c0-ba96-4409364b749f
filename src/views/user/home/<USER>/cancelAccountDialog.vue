<template>
  <el-dialog
    :modelValue="modelValue"
    title="注销账号"
    :before-close="handleClose"
    width="30%"
  >
    <!-- 警告内容 -->
    <div class="warning-text">
      <div class="icon-text-container">
        <el-icon :size="30" style="margin-right: 20px"
          ><WarningFilled
        /></el-icon>
        <span
          >平台中购买的教材、充值点、学习记录、所建班课、教学资源等所有数据将会永久删除，且不可恢复。</span
        >
      </div>
    </div>
    <el-divider />
    <div v-if="!showSecurityVerification">
      <div style="text-align: center">
        <h3 style="font-weight: bold">为了安全起见，请验证您的身份</h3>
        <!-- 验证表单 -->
        <el-form :model="form" label-width="120px" class="verify-form">
          <el-form-item label="注册手机号">
            <el-input
              v-model="form.phonenumber"
              autocomplete="off"
              placeholder="请输入手机号"
              class="input-with-button"
              style="width: 350px"
              prefix-icon="Iphone"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="验证码">
            <el-input
              v-model="form.code"
              autocomplete="off"
              placeholder="请输入4位验证码"
              class="input-with-button"
              style="width: 350px"
            >
              <template #prefix>
                <img src="@/assets/icons/svg/shield.svg" />
              </template>
              <template #suffix>
                <el-button
                  type="primary"
                  link
                  :disabled="isSendingCode"
                  @click="sendVerificationCode(form.phonenumber)"
                >
                  <span :class="{ 'gray-text': isSendingCode }">{{
                    sendCodeText
                  }}</span>
                </el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-form>

        <!-- 协议勾选 -->
        <el-checkbox v-model="checked">勾选即代表您同意并接受</el-checkbox>
        <el-link
          type="primary"
          :underline="false"
          style="margin: 0px 0px 8px 10px"
          @click="openNoticeDialog"
          >注销须知</el-link
        >
      </div>
    </div>
    <div class="flex-col" style ="margin: 17px 15px 0 84px;" v-else>
      <div class="flex-col justify-between">
        <el-icon
          style="font-size: 25px"
          :size="size"
          :color="color"
          @click="showSecurityVerification = !showSecurityVerification"
          class="pointer"
        >
          <Back />
        </el-icon>
        <div class="slide-verify-box_4 flex-col">
          <span class="slide-verify-text_11">请完成下列验证后继续</span>
          <div class="slide-verify-box_5 flex-col">
            <slide-verify
              :width="300"
              :height="150"
              :imgs="slideVerifyImages"
              sliderText="按住左边按钮拖动完成上方拼图"
              @success="onSuccess"
              @fail="onFail"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- 操作按钮 -->
    <template #footer>
      <el-button  v-if="!showSecurityVerification" @click="closeDialog">取消</el-button>
      <el-button  v-if="!showSecurityVerification" type="danger" @click="confirmLogout">确认注销</el-button>
    </template>
  </el-dialog>
  <CancellationNoticeDialog ref="noticeDialog" />
</template>

<script setup>
import { ref, computed } from "vue";
import SlideVerify from "vue3-slide-verify";
import "vue3-slide-verify/dist/style.css";
import { ElNotification, ElMessage } from "element-plus";
import { sendAliyun } from "@/api/login";
import CancellationNoticeDialog from "./CancellationNoticeDialog.vue";
import {cancelAccount } from '@/api/edu/dutpUser';
import useUserStore from '@/store/modules/user';
const userStore = useUserStore()
const noticeDialog = ref(null);

const openNoticeDialog = () => {
  noticeDialog.value.open();
};
const codeAttempts = ref(0);
const maxCodeAttempts = 5;
const slideVerifyImages = ref([
  new URL("@/assets/images/img.jpg", import.meta.url).href,
  new URL("@/assets/images/img1.jpg", import.meta.url).href,
  new URL("@/assets/images/img2.jpg", import.meta.url).href,
  new URL("@/assets/images/img3.jpg", import.meta.url).href,
  new URL("@/assets/images/img4.jpg", import.meta.url).href,
  new URL("@/assets/images/img5.jpg", import.meta.url).href,
  // 其他图片路径
]);
const showSecurityVerification = ref(false);
const verificationPhone = ref("");
const msg = ref("");
const block = ref();
const emit = defineEmits(["update"]);
const sendCodeText = ref("获取验证码");
const isSendingCode = ref(false);
const props = defineProps({
  form: Object,
  modelValue: Boolean,
});
const handleClose = (done) => {
  emit("update:modelValue", false);
  done();
};

const closeDialog = () => {
  emit("update:modelValue", false);
};
const form = ref({});
const onFail = () => {
  msg.value = "验证不通过";
  // 刷新
  block.value?.refresh();
};
const checked = ref(false);
const countdown = ref(60);

// 验证码按钮状态计算
const codeBtnText = computed(() =>
  countdown.value > 0 ? `${countdown.value}秒后重发` : "获取验证码"
);
const validatePhone = (phone) => {
  const phoneRegex = /^1\d{10}$/;
  if (!phoneRegex.test(phone)) {
    ElNotification({
      title: "操作提示",
      message: "手机号格式不正确",
      type: "error",
    });
    return false;
  }
  return true;
};
const sendVerificationCode = (phone) => {
  if (!phone) {
    ElNotification({
      title: "操作提示",
      message: "请输入手机号",
      type: "error",
    });
    return;
  }
  if (validatePhone(phone)) {
    verificationPhone.value = phone;
    showSecurityVerification.value = true;
  }
};

// 确认注销
const confirmLogout = () => {
  if (!form.value.phonenumber || !form.value.code) {
    ElMessage.error("请填写完整信息");
    return;
  }
  if (!checked.value) {
    ElMessage.error("请先阅读并同意协议");
    return;
  }
  cancelAccount(form.value).then((res) => {
    if (res.code === 200) {
      ElNotification({
        title: "注销成功",
        type: "success",
      });
      closeDialog();
      userStore.logOut().then(() => {
        setTimeout(() => {
          location.href = "/index"; // 注销后跳转到首页
        }, 3000); // 延迟3秒
      });
    } else {
      ElNotification({
        title: "注销失败",
        message: res.msg,
        type: "error",
      });
    }
  });
};
const onSuccess = async () => {
  msg.value = "验证通过";
  showSecurityVerification.value = false;
  if (codeAttempts.value >= maxCodeAttempts) {
    ElNotification({
      title: "操作提示",
      message: "超过最大发送次数，请稍后再试",
      type: "error",
    });
    return;
  }
  if (isSendingCode.value) return;
  isSendingCode.value = true;
  sendCodeText.value = `${countdown.value}秒后重新发送`;
  let timer = setInterval(() => {
    countdown.value--;
    sendCodeText.value = `${countdown.value}秒后重新发送`;
    if (countdown.value <= 0) {
      clearInterval(timer);
      isSendingCode.value = false;
      sendCodeText.value = "获取验证码";
      countdown.value = 60;
    }
  }, 1000);
  codeAttempts.value++;
  await sendAliyun({ phone: verificationPhone.value }).then((res) => {
    if (res.code === 200) {
      ElNotification({
        title: "操作提示",
        message: "验证码已发送，请查收短信",
        type: "success",
      });
    }
  });
};
onMounted(() => {
  form.value = props.form;
});
</script>
<style src="@/assets/styles/common.css" />
<style src="@/assets/styles/index.css" />
<style scoped lang="scss">
.warning-text {
  color: #f56c6c;
  margin: 0px 20px 20px 20px;
  line-height: 1.5;
}
.pointer {
  cursor: pointer;
}
.verify-form {
  margin-top: 20px;
}

.code-input {
  display: flex;
  align-items: center;
}

.agreement {
  display: flex;
  align-items: center;
  margin-top: 20px;
}
.icon-text-container {
  display: flex;
  align-items: center;
}
</style>
