<template>
  <div class="content-right-con">
    <div class="right-top-con">
      <div class="menu-con">
        <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect">
          <el-menu-item index="0">全部购书码</el-menu-item>
          <!-- <el-menu-item index="1">已兑换</el-menu-item>
          <el-menu-item index="2">待兑换</el-menu-item> -->
        </el-menu>
      </div>
      <el-icon style="margin-left: 400px;">
        <Tickets />
      </el-icon><el-button link style="margin-right: 61px;font-size: 14px;" @click="open()">兑换码使用规则</el-button>
    </div>
    <div style="height: 550px;">
      <div style="margin-left: 950px;margin-bottom: 10px;">
        <el-input v-model="bookName" style="width: 240px" placeholder="请输入教材名称/ISBN/ISSN">
          <template #append>
            <el-button @click="getList()" :icon="Search" />
          </template>
        </el-input>
      </div>
      <el-table v-loading="loading" :data="tableList" height="450px"
        :header-cell-style="{ background: '#EDF4FD !important', color: '#666666', textAlign: 'left' }"
        :cell-style="{ textAlign: 'left' }">
        <el-table-column label="教材名称" align="center" prop="bookName" width="350" />
        <el-table-column label="ISBN/ISSN" align="center" prop="isbn" width="230" />
        <el-table-column label="购书码" align="center" prop="maskCode" width="240" />
        <el-table-column label="发放时间" align="center" prop="bindDate" width="180" />
        <el-table-column label="兑换时间" align="center" prop="exchangeDate" width="180" />
        <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button v-if="scope.row.exchangeDate === '-'" link type="primary" @click="handlePreview(scope.row)"
              v-hasPermi="['tool:gen:preview']">兑换</el-button>
            <span v-else style="color: gray;">已兑换</span>
          </template>
        </el-table-column> -->
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
  </div>
</template>
<script setup>
// 引入图片
import { onMounted, ref } from 'vue';
import { ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { listPurchaseCode } from '@/api/shop/purchaseCode';
import { listConfig } from '@/api/system/config';
import { listLog } from '@/api/system/log';
import { ElNotification } from 'element-plus';
import { useRouter } from 'vue-router';
const router = useRouter()
const filterData = ref([])
const exchangeLimit = ref(0);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    bookName: undefined,
  }
})
const { queryParams } = toRefs(data)
const loading = ref(false)
const activeIndex = ref('0');
const total = ref(0)
const bookName = ref('')
const tableList = ref([])
const handleSelect = (key, path) => {
  activeIndex.value = parseInt(key);
  if (parseInt(key) === 1) {
    tableList.value = filterData.value.filter(book => book.exchangeDate !== '-');
  } else if (parseInt(key) === 2) {
    tableList.value = filterData.value.filter(book => book.exchangeDate === '-');
  } else {
    tableList.value = filterData.value;
  }
};
const open = () => {
  ElMessageBox.alert(`
    <p>兑换码使用规则:</p>
    <p>1. 兑换码不能转让，仅供领取用户使用;</p>
    <p>2. 兑换码使用期限为永久;</p>
    <p>3. 每次兑换只可以使用1张兑换码，每个用户每天最多兑换${exchangeLimit.value}次;</p>
  `, '兑换码使用规则', {
    center: true,
    confirmButtonText: '确认',
    dangerouslyUseHTMLString: true,
    customClass: 'custom-message-box'
  });
};
/** 查询表集合 */
async function getList() {
  loading.value = true;
  try {
    activeIndex.value = 0
    queryParams.value.bookName = bookName.value
    queryParams.value.isbn = bookName.value
    queryParams.value.issn = bookName.value
    const response = await listPurchaseCode(queryParams.value);
    response.rows.forEach(item => {
      item.maskCode = maskCode(item.code)
      item.exchangeDate = item.exchangeDate ? item.exchangeDate : '-'
      item.isbn = item.isbn ? item.isbn  : item.issn
    })
    tableList.value = response.rows; // 假设接口返回的数据在 response.data 中
    filterData.value = response.rows;
    total.value = response.total;
  } catch (error) {
    console.error('获取书籍信息失败:', error);
    tableList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};
const getConfig = () => {
  const params = {
    pageNum: 1,
    pageSize: 10
  }
  listConfig(params).then(res => {
    if (res.code === 200) {
      exchangeLimit.value = res.rows[0].exchangeLimit
    }
  })
}
const handlePreview = (row) => {
  // 获取当前日期
  const currentDate = new Date();

  // 格式化日期
  const year = currentDate.getFullYear();
  const month = String(currentDate.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1
  const day = String(currentDate.getDate()).padStart(2, '0');
  // 组合成所需的格式
  const formattedDate = `${year}-${month}-${day}`;
  const params = {
    exchangeDate: formattedDate
  }
  listLog(params).then(res => {
    if (res.code === 200) {
      if (res.data.length >= exchangeLimit.value) {
        ElNotification({
          title: '错误',
          message: '超出今日兑换次数，请明日再次兑换',
          type: 'error'
        });
      }else{
        //跳转页面
        router.push('/book-codes')
      }
    }
  })
}
const maskCode = (code) => {
  if (!code || code.length < 16) {
    return code; // 如果码长度小于等于16，则直接返回原码
  }
  const prefix = code.slice(0, 6);
  const suffix = code.slice(-6);
  const maskedMiddle = '****';
  return prefix + maskedMiddle + suffix;

}
onMounted(() => {
  getList()
  getConfig()
})
</script>

<style scoped lang="scss">
@import '@/assets/styles/index.scss';

.content-right-con {
  width: 1239px;
  min-height: 700px;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(182, 182, 182, 1.0);;
  margin-left: -70px;

  .right-top-con {
    width: 100%;
    border-bottom: 1px solid #e5e6e7;
    margin-bottom: 10px;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: center;

    .menu-con {
      margin-left: 57px;

      .el-menu-demo {
        width: 600px;
        border-bottom: none !important;
      }
    }

    .filter-con {
      margin-right: 61px;
    }
  }
}
</style>
