<template>
  <div class="content-right-con">
    <div v-if="certificationStatus" class="form-container">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formRef"
        label-width="150px"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-alert
              :title="
                formData.authStateText
                  ? formData.authStateText
                  : '请填写相关信息，我们的工作人员会及时处理，通过认证后您将享有我们为教师提供的各项服务!'
              "
              :type="
                formData.authState === 0
                  ? 'warning'
                  : formData.authState === 1
                    ? 'success'
                    : 'error'
              "
              show-icon
              :closable="false"
            />
          </el-col>
        </el-row>

        <!-- 姓名单独一行 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="姓名" :prop="formData.isReadOnly ? '' : 'realName'">
              <el-input
                v-model="formData.realName"
                placeholder="请输入您的姓名"
                :disabled="formData.isReadOnly"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="手机号">
              <el-input
                v-model="formData.phone"
                placeholder="请输入您的手机号"
                disabled="disabled"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" :prop="formData.isReadOnly ? '' : 'email'">
              <el-input
                v-model="formData.email"
                placeholder="请输入您的邮箱"
                :disabled="formData.isReadOnly"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="学校/院系/专业" prop="schoolDepartmentMajor">
              <el-cascader
                v-model="formData.schoolDepartmentMajor"
                :options="cascaderOptions"
                :props="{
                  value: 'id',
                  label: 'schoolName',
                  children: 'children'
                }"
                value-key="id"
                placeholder="请选择您的学校/院系/专业"
                style="width: 100%"
                @change="handleCascaderChange"
                :disabled="formData.isReadOnly"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="职业" prop="positionName">
              <el-input
                v-model="formData.positionName"
                placeholder="请输入您的职业"
                :disabled="formData.isReadOnly"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职称" prop="titleName">
              <el-input
                v-model="formData.titleName"
                placeholder="请输入您的职称"
                :disabled="formData.isReadOnly"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 职务单独一行 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="职务" prop="dutyName">
              <el-input
                v-model="formData.dutyName"
                style="width: 40%"
                placeholder="请输入您的职务"
                :disabled="formData.isReadOnly"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 专业单独一行 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="选书依据" prop="bookPreference">
              <el-input
                type="textarea"
                v-model="formData.bookPreference"
                placeholder="请输入内容"
                :disabled="formData.isReadOnly"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="教师证明" prop="authenticationImageArray">
              <el-upload
                v-model:file-list="formData.authenticationImageArray"
                :http-request="upload"
                :action="uploadUrl"
                :before-upload="handleBeforeUpload"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemove"
                :disabled="formData.isReadOnly"
              >
                <el-icon>
                  <Plus />
                </el-icon>
              </el-upload>
              <el-dialog v-model="dialogVisible" width="800px">
                <img
                  w-full
                  :src="dialogImageUrl"
                  style="width: 100%; height: 100%"
                  alt="Preview Image"
                />
              </el-dialog>
              <div class="upload-instructions">
                <p style="color: red">
                  请上传您的工作证，需体现您的姓名、工作院系等信息，或者其他能证明您教师身份的证件。不超过20M
                </p>
                <p style="color: gray">
                  1. 证明件类型包括但不限于：工作证、职称证书、聘书;
                </p>
                <p style="color: gray">
                  2. 请勿上传教师资格证，其上不能体现当前工作单位;
                </p>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item>
              <el-checkbox
                v-model="formData.agree"
                @change="handleCheckboxChange"
                :disabled="formData.isReadOnly"
              >
                勾选即代表您同意并接受
                <el-button
                  type="text"
                  style="margin-bottom: 2px"
                  @click="handleAgreementClick"
                  :disabled="formData.isReadOnly"
                  >教师认证服务协议与隐私政策</el-button
                >
              </el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24" style="text-align: center">
            <el-button
              type="primary"
              :disabled="!formData.agree || formData.isReadOnly || !sbmFlg "
              style="width: 200px"
              @click="submitForm"
              >提交</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div v-else class="img-center">
      <img
        src="@/assets/images/home/<USER>"
        style="width: 50%; margin-bottom: 100px"
      />
      <span v-if="userInfo.userType === '2' && formData.userId === ''" style="margin-bottom: 100px; text-align: center"
        >智慧教育云平台为教师提供便捷、实用的各项教学资源服务。<br />
        您的账号是由后台管理员创建的教师账号，无需进行教师认证！</span
      >
      <span v-else-if="userInfo.userType !== '2' && formData.userId === ''" style="margin-bottom: 100px; text-align: center"
        >智慧教育云平台为教师提供便捷、实用的各项教学资源服务。<br />
        教师可以在线阅读，试用申请，下载/使用课件，专属校本教材、教学平台专属功能。</span
      >
      <el-button
        v-if="userInfo.userType !== '2' && formData.userId === ''" 
        type="primary"
        style="width: 200px"
        @click="immediatelyAuthenticate"
        >立即认证</el-button
      >
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElNotification } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import {
  addAuthentication,
  listAuthentication,
} from "@/api/edu/authentication";
import { listSchool } from "@/api/basic/school";
import { OssService } from "@/utils/aliOss.js";
import useUserStore from "@/store/modules/user.js";
import { nextTick } from "vue";
const handleCascaderChange = (value, path) => {
  console.log("formData.schoolDepartmentMajor", formData.schoolDepartmentMajor);
};
const userStore = useUserStore();
const userInfo = ref({});
const { proxy } = getCurrentInstance();
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/upload"); // 上传的图片服务器地址
const formRef = ref(null);
const cascaderOptions = ref([]);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const formData = reactive({
  userId: "", //用户ID
  realName: "", // 真实姓名
  phone: "", // 电话号
  email: "", // 邮箱
  schoolDepartmentMajor: [], // 学校、院系、专业的组合数组
  positionName: "", // 职位
  titleName: "", // 职称
  dutyName: "", // 职务
  bookPreference: "", // 选书依据
  authenticationImageArray: [], // 上传的认证图片数组
  agree: false, // 同意条款
  userName: "", // 用户账号
  nickName: "", // 用户昵称
  workNo: null, // 工号
  schoolId: null, // 教师认证学校的学校表id
  academyId: null, // 院系ID
  specialityId: null, // 专业表id
  authState: null, // 认证状态 (0未审核1通过2拒绝)
  courseName: "", // 课程信息
  classmateTotal: null, // 学生人数
  termBegin: null, // 学期开始日期
  termEnd: null, // 学期结束日期
  authenticationImage: null, // 上传的认证图片数组
  isReadOnly: false, // 新增属性，用于控制表单是否只读
  authStateText: "", // 新增属性，用于存储审核状态的文字描述
});
const certificationStatus = ref(false);
const sbmFlg = ref(false);
const schoolQueryParams = {
  pageNum: 1,
  pageSize: 9999,
  schoolName: null,
  schoolCode: null,
};
const isImgLoading = ref(false);
const rules = {
  realName: [{ required: true, message: "请输入您的姓名", trigger: "blur" }],
  email: [
    { required: true, message: "请输入您的邮箱", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱格式", trigger: "blur" },
  ],
  schoolDepartmentMajor: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value || value.length < 1) {
          callback(new Error("请选择学校、院系和专业"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
  authenticationImageArray: [
    { required: true, message: "请上传您的教师证明", trigger: "change" },
  ],
};

// 上传前校检格式和大小
function handleBeforeUpload(file) {
  const imageType = ["image/jpeg", "image/jpg", "image/png", "image/svg"];
  const isImage = imageType.includes(file.type);
  //检验文件格式
  if (!isImage) {
    proxy.$modal.msgError(`文件格式错误!`);
    return false;
  }else{
    sbmFlg.value = false;
  }
  // 文件大小限制（20MB）
  const isValidSize = file.size <= 20 * 1024 * 1024; // 20MB
  if (!isValidSize) {
    proxy.$modal.msgError(`文件大小超过限制（最大20MB）`);
    return false;
  }
  return true;
}
// 上传成功处理
function handleUploadSuccess(res, file) {
  sbmFlg.value = true;
}

// 上传失败处理
function handleUploadError() {}

const syncFile = async (file) => {
  try {
    // 工具类引用
    const res = await OssService(file.file);
    return res;
  } catch (e) {
    throw e;
  }
};
function upload(file) {
  return syncFile(file);
}
const handleRemove = (uploadFile, uploadFiles) => {
  sbmFlg.value = false;
};

const handlePictureCardPreview = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url;
  dialogVisible.value = true;
};

const handleCheckboxChange = (value) => {
  formData.agree = value;
};

const handleAgreementClick = () => {
  window.open("/agreement", "_blank");
};

const submitForm = () => {
  // if (!isImgLoading.value) {
  //   ElMessage.error("等待图片上传完成");
  //   return;
  // }
  formRef.value.validate((valid) => {
    if (valid) {
      formData.userId = userInfo.value.userId;
      formData.userName = userInfo.value.userName;
      formData.nickName = userInfo.value.nickName;
      formData.schoolId = formData.schoolDepartmentMajor[0];
      formData.academyId = formData.schoolDepartmentMajor[1]
        ? formData.schoolDepartmentMajor[1]
        : null;
      formData.specialityId = formData.schoolDepartmentMajor[2]
        ? formData.schoolDepartmentMajor[2]
        : null;
      const authenticationImageCopy = [];
      formData.authenticationImageArray.forEach((item) => {
        // 确保 item.response 存在并且有 url 属性
        if (item.response && item.response.url) {
          authenticationImageCopy.push(item.response.url);
        }
        // 确保 item.url 存在
        else if (item.url) {
          authenticationImageCopy.push(item.url);
        }
      });
      formData.authenticationImage = authenticationImageCopy.join(",");

      // 使用 nextTick 确保数据更新
      nextTick(() => {
        addAuthentication(formData)
          .then((res) => {
            if (res.code === 200) {
              ElMessage.success("表单提交成功");
              // 重新获取表单数据
              getInfo();
            }
          })
          .catch((error) => {
            ElMessage.error("表单提交失败，请稍后再试");
            console.error(error);
          });
      });
    } else {
      ElMessage.error("表单验证失败，请检查输入");
      return false;
    }
  });
};
const getSchoolList = () => {
  listSchool(schoolQueryParams).then((response) => {
    cascaderOptions.value = response.data;
    const res = cascaderOptions.value.filter((item) => item.id === formData.schoolDepartmentMajor[0]);
    console.log("id",  formData.schoolDepartmentMajor[0]);
    console.log("res",  res);
    console.log("cascaderOptions", cascaderOptions.value );
  });
};
const getInfo = () => {
  const parm = {
    userId: userInfo.value.userId,
  };
  listAuthentication(parm).then((response) => {
    if (response.code === 200 && response.rows) {
      if (response.rows?.length > 0 ) {
        certificationStatus.value = true;
        Object.assign(formData, response.rows[0]);
        formData.authenticationImageArray = (
          response.rows[0].authenticationImage || ""
        )
          .split(",")
          .map((url) => ({ url }));
        formData.schoolDepartmentMajor = [
          response.rows[0].schoolId,
          response.rows[0].academyId,
          response.rows[0].specialityId,
        ].filter((id) => id != null);

        console.log("schoolDepartmentMajor", formData.schoolDepartmentMajor);
        // 根据 authState 设置 isReadOnly 和 authStateText
        formData.isReadOnly = formData.authState !== 2;
      }
      switch (formData.authState) {
        case 0:
          formData.authStateText = "审核中!";
          formData.agree = true;
          break;
        case 1:
          formData.authStateText = "审核通过!";
          formData.agree = true;
          break;
        case 2:
          formData.authStateText = "审核拒绝! 请填写相关信息,进行提交!";
          formData.agree = false;
          break;
        default:
          formData.authStateText = "";
      }
    }
  });
};
const immediatelyAuthenticate = () => {
  certificationStatus.value = true;
};
onMounted(async () => {
  const response = await userStore.getInfo();
  userInfo.value = response.user;
  formData.phone = userInfo.value.phonenumber;
  formData.realName = userInfo.value.realName;
  getSchoolList();
  getInfo();
});
</script>

<style scoped>
.form-container {
  margin: 20px;
}

.el-row {
  margin-bottom: 20px;
}

.content-right-con {
  width: 1239px;
  min-height: 700px;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(182, 182, 182, 1);
  margin-left: -90px;
  overflow: auto;
}

.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}

.upload-instructions {
  width: 100%;
  display: block;
}

.upload-instructions p {
  margin: 0 0 10px;
  display: block;
}
.img-center {
  display: flex;
  flex-direction: column; /* 垂直排列子元素 */
  justify-content: center; /* 垂直居中 */
  align-items: center; /* 水平居中 */
}
</style>
