<template>
  <div class="content-right-con" style="margin-left: -88px" v-if="isDetailShow">
    <el-breadcrumb :separator-icon="ArrowRight">
      <el-breadcrumb-item style="cursor: pointer" @click="goBack(detailData)"
        >我的纠错</el-breadcrumb-item
      >
      <el-breadcrumb-item>纠错详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="group_detail flex-row justify-between">
      <div class="image-wrapper_1 flex-col">
        <img
          class="label_1"
          referrerpolicy="no-referrer"
          src="@/assets/icons/svg/error-correction.svg"
        />
      </div>
      <div class="group_5_e flex-col justify-between">
        <span class="text_4_e">{{ detailData.bookName }}</span>
        <div class="block_1 flex-row justify-between">
          <div class="box_1 flex-col justify-between">
            <span class="paragraph_2">
              <span class="paragraph_1">问题描述：</span>{{ detailData.comment
              }}<br /><span class="paragraph_1">纠错内容：</span
              >{{ detailData.faultText }}<br /><span class="paragraph_1"
                >错误类型：</span
              >{{ detailData.faultType }}</span
            >
            <div class="image-wrapper_2 flex-row">
              <span class="paragraph_1">图片：</span>
              <el-image
                style="width: 100px; height: 100px"
                show-progress
                :preview-src-list="[image.fileUrl]"
                v-for="image in detailData.images"
                referrerpolicy="no-referrer"
                :src="image.fileUrl"
              />
            </div>
            <span class="paragraph_2">
              <span class="paragraph_1">反馈内容：</span>{{ detailData.content
              }}<br
            /></span>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="text-wrapper_2_e flex-row justify-between">
      <span class="text_5_e">反馈内容：</span>
      <span class="text_6_e">{{ detailData.content }} </span>
    </div> -->
  </div>
  <div class="content-right-con" v-else>
    <el-row :gutter="20">
      <el-col
        :span="12"
        v-for="feedback in tableList"
        :key="feedback.feedBackId"
      >
        <el-card
          shadow="always"
          class="card-style"
          @click="handleRowClick(feedback)"
        >
          <el-space alignment="left" direction="vertical" style="gap: 0px 0px">
            <el-text tag="b" size="large" style="width: 514px">
              你提交了纠错
              <el-text
                style="
                  color: #205ca4;
                  font-size: 16px;
                  padding: 0px;
                  margin-bottom: 2px;
                "
                >{{ feedback.faultType }}</el-text
              >
              <el-text
                style="float: right; font-weight: 400; margin-right: 50px"
                :class="getStatusClass(feedback.auditStatus)"
                >{{ feedback.auditStatus }}</el-text
              >
            </el-text>
            <br />
            <!-- <el-popover placement="bottom" title="详细内容" width="300" trigger="hover" :content="feedback.faultText">
              <template #reference> -->
            <el-text
              class="text-ellipsis"
              style="
                color: black;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 514px;
              "
              >文本：{{ feedback.faultText }}</el-text
            >
            <!-- </template>
            </el-popover> -->
            <br />
            <!-- <el-popover placement="top" title="备注" width="300" trigger="hover" :content="feedback.comment">
              <template #reference> -->
            <el-text
              class="text-ellipsis"
              style="
                color: black;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 520px;
              "
              >备注：{{ feedback.comment }}</el-text
            >
            <!-- </template>
            </el-popover> -->
            <br />
            <el-text class="text-ellipsis" style="color: black">{{
              feedback.bookName
            }}</el-text>
            <br />
            <el-text class="text-ellipsis" style="color: gray">{{
              feedback.createTime
            }}</el-text>
          </el-space>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { listFeedback } from "@/api/message/feedback";
import {
  FAULT_TYPE,
  getLabelByValue,
  getValueByLabel,
} from "@/utils/dictionary";
// import { decryptData } from '@/utils/encrypt.js';
const isDetailShow = ref(false);
const route = useRoute();
const bookId = route.query.key;
const detailData = ref({});
// 定义响应式变量
const loading = ref(false);
const tableList = ref([]);
const queryParams = ref({
  // 其他查询参数
});
function goBack() {
  isDetailShow.value = false;
}
const handleRowClick = (item) => {
  detailData.value = item;
  isDetailShow.value = true;
};
// 获取状态对应的类名
const getStatusClass = (status) => {
  switch (status) {
    case "未处理":
      return "status-unprocessed";
    case "已处理":
      return "status-processed";
    default:
      return "";
  }
};
// 查询反馈列表
const getFeedbackList = () => {
  loading.value = true;
  listFeedback(queryParams.value)
    .then((res) => {
      if (res.code === 200) {
        res.data.forEach((element) => {
          element.auditStatus = element.auditStatus === 0 ? "未处理" : "已处理";
          const faultType = element.faultType.split(",");

          element.faultTypeLabels = getLabelByValue(FAULT_TYPE, faultType);
          element.faultType = element.faultTypeLabels.join(",");
        });
        tableList.value = res.data;
        detailData.value = res.data[0];
        loading.value = false;
      }
    })
    .catch((error) => {
      console.error("获取反馈数据失败:", error);
      loading.value = false;
    });
};
onMounted(async () => {
  // const bookIdDecrypt = await decryptData({ encryptedData: bookId });
  queryParams.value.bookId = bookId;
  await getFeedbackList();
  if (queryParams.value.bookId) {
    isDetailShow.value = true;
  }
  queryParams.value.bookId = null;
});
</script>

<style scoped lang="scss">
.card-style {
  box-shadow: 0px 0px 12px 0px rgba(192, 192, 192, 0.51);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 514px;
  height: 217px;
  margin: 64px 102px 0 40px;
  cursor: pointer;
}

.content-right-con {
  width: 1239px;
  min-height: 700px;
  overflow: auto;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(182, 182, 182, 1);
  margin-left: -90px;
}

.status-unprocessed {
  color: red;
}

.status-processed {
  color: green;
}

.status-processing {
  color: blue;
}

.text_5_e {
  width: 78px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 14px;
  font-family: PingFang SC-Bold;
  font-weight: 700;
  text-align: left;
  line-height: 34px;
}

.text_6_e {
  width: 929px;
  height: 84px;
  overflow-wrap: break-word; /* 确保长单词换行 */
  word-break: break-all; /* 强制换行 */
  color: rgba(102, 102, 102, 1);
  font-size: 14px;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 28px;
  margin-top: 3px;
}

.group_4 {
  width: 1086px;
  min-height: 700px;
  margin: 39px 0 0 44px;

  .image-wrapper_1 {
    background-color: rgba(221, 240, 255, 1);
    border-radius: 50%;
    height: 58px;
    width: 58px;

    .label_1 {
      width: 24px;
      height: 24px;
      margin: 17px 0 0 17px;
    }
  }

  .group_5_e {
    width: 1009px;
    height: 260px;
    margin-top: 1px;

    .text_4_e {
      width: 469px;
      height: 28px;
      overflow-wrap: break-word;
      color: rgba(51, 51, 51, 1);
      font-size: 20px;
      font-family: PingFang SC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 28px;
    }

    .block_1 {
      width: 1009px;
      height: 211px;
      margin-top: 21px;

      .paragraph_1 {
        width: 78px;
        height: 136px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 14px;
        font-family: PingFang SC-Bold;
        font-weight: 700;
        text-align: left;
        line-height: 34px;
      }

      .box_1 {
        width: 929px;
        height: 211px;

        .paragraph_2 {
          width: 929px;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 14px;
          font-family: PingFang SC-Regular;
          font-weight: normal;
          text-align: left;
          line-height: 34px;
        }

        .image-wrapper_2 {
          width: 402px;
          height: 99px;
          margin-top: 10px;

          .image_2 {
            width: 90px;
            height: 90px;
          }

          .image_3 {
            width: 90px;
            height: 90px;
            margin-left: 14px;
          }

          .image_4 {
            width: 90px;
            height: 90px;
            margin-left: 14px;
          }

          .image_5 {
            width: 90px;
            height: 90px;
            margin: 9px 0 0 14px;
          }
        }
      }
    }
  }
}

.group_detail {
  width: 1086px;
  min-width: 700px;
  margin: 39px 0 0 44px;

  .image-wrapper_1 {
    background-color: rgba(221, 240, 255, 1);
    border-radius: 50%;
    height: 58px;
    width: 58px;

    .label_1 {
      width: 24px;
      height: 24px;
      margin: 17px 0 0 17px;
    }
  }

  .group_5_e {
    width: 1009px;
    height: 260px;
    margin-top: 1px;

    .text_4_e {
      width: 469px;
      height: 28px;
      overflow-wrap: break-word;
      color: rgba(51, 51, 51, 1);
      font-size: 20px;
      font-family: PingFang SC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 28px;
    }

    .block_1 {
      width: 1009px;
      height: 211px;
      margin-top: 21px;

      .paragraph_1 {
        width: 78px;
        height: 136px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 14px;
        font-family: PingFang SC-Bold;
        font-weight: 700;
        text-align: left;
        line-height: 34px;
      }

      .box_1 {
        width: 929px;
        height: 211px;

        .paragraph_2 {
          width: 929px;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 14px;
          font-family: PingFang SC-Regular;
          font-weight: normal;
          text-align: left;
          line-height: 34px;
        }

        .image-wrapper_2 {
          width: 402px;
          height: 99px;
          margin-top: 10px;

          .image_2 {
            width: 90px;
            height: 90px;
          }

          .image_3 {
            width: 90px;
            height: 90px;
            margin-left: 14px;
          }

          .image_4 {
            width: 90px;
            height: 90px;
            margin-left: 14px;
          }

          .image_5 {
            width: 90px;
            height: 90px;
            margin: 9px 0 0 14px;
          }
        }
      }
    }
  }
}
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.text-wrapper_2_e {
  width: 1009px;
  height: 87px;
  margin: 11px 0 498px 121px;

  .text_5_e {
    width: 78px;
    height: 34px;
    overflow-wrap: break-word;
    color: rgba(51, 51, 51, 1);
    font-size: 14px;
    font-family: PingFang SC-Bold;
    font-weight: 700;
    text-align: left;
    line-height: 34px;
  }
}
.text-ellipsis {
  max-width: 480px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.text-ellipsis-single {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
