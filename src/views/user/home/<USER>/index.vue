<template>
  <div v-if="isShow" class="content-right-con">
    <div class="right-top-con-order">
      <div class="menu-con">
        <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect">
          <el-menu-item index="1">全部订单</el-menu-item>
          <el-menu-item index="2">待支付</el-menu-item>
          <el-menu-item index="3">已购买</el-menu-item>
          <el-menu-item index="4">已取消</el-menu-item>
          <el-menu-item index="5">退款中</el-menu-item>
          <el-menu-item index="6">已退款</el-menu-item>
        </el-menu>
      </div>
      <el-input v-model="selParm" style="width: 240px" placeholder="请输入教材名称/ISBN">
        <template #append>
          <el-button @click="getInfo" :icon="Search" />
        </template>
      </el-input>
      <el-select v-model="filterDate" style="width: 150px" placeholder="今年内订单" @change="handleFilterChange">
        <el-option v-for="year in yearsData" :key="year" :label="year" :value="year"></el-option>
      </el-select>
    </div>
    <div class="order-list-con">
      <div class="order-list">
        <div class="order-item" v-for="(item, index) in orderData" :key="index">
          <div class="item-top">
            <div class="top-left">
              <div class="order-time">订单支付时间：{{ item.payTime }}</div>
              <div class="order-num">订单编号：{{ item.orderNo }}</div>
            </div>
            <div class="top-right">
              <!--待支付-->
              <span v-if="item.orderStatus === 'pending'" class="cancel-order" @click="cancellationOfOrder(item)">取消订单</span>
              <!--已取消-->
              <el-button v-if="item.orderStatus === 'cancelled'" class="cancel-order" :icon="Delete" text bg @click="delCode(item)"></el-button>
              <!-- 已购买 -->
              <span
                v-if="item.orderStatus === 'paid' && item.refundStatus !== 1 && item.refundStatus !== 3"
                class="cancel-order"
                style="margin-right: 15px"
              >
                购书码：{{ item.code }}
                <el-button v-if="item.shelfState !== 3" :icon="Delete" text bg @click="delCode(item)"></el-button>
              </span>
              <!-- 退款中 -->
              <span v-else-if="item.orderStatus === 'paid' && item.refundStatus === 1" class="cancel-order" style="margin-right: 35px">
                购书码：{{ item.code }}
              </span>
              <!-- 已退款 -->
              <span v-else-if="item.orderStatus === 'paid' && item.refundStatus === 3" class="cancel-order" style="margin-right: 15px">
                购书码：{{ item.code }}
                <el-button :icon="Delete" text bg @click="delCode(item)"></el-button>
              </span>
            </div>
          </div>
          <div class="item-middle-bottom-con">
            <div class="item-middle">
              <div class="middle-left">
                <div class="book-img">
                  <div v-if="item.shelfState === 3">
                    <div class="image-container">
                      <img src="@/assets/images/home/<USER>" class="recall-icon">
                      <img class="disabled-div" style="width: 100%;height: 100%;" :src="item.cover ? item.cover : bookCoverDefault" />
                    </div>
                  </div>
                  <div v-else class="image-container">
                    <img style="width: 100%;height: 100%;" :src="item.cover ? item.cover : bookCoverDefault" @click="goToBookDetails(item)"/>
                  </div>
                </div>
                <div class="book-info">
                  <div class="book-name">{{ item.bookName }}</div>
                  <div class="book-isbn">ISBN：{{ item.isbn }}</div>
                  <div class="book-price">定价：{{ item.price }}</div>
                </div>
              </div>
              <div class="middle-right">
                <span class="text_order_23" :style="{ color: getColorByStatus(item) }">
                  {{ getTextByStatus(item) }}
                </span>
              </div>
            </div>
            <div class="item-bottom">
              <div class="bottom-left">
                <div class="banner-con" v-if="item.deputyBookList && item.deputyBookList.length > 0">
                  <supportingTextbooksBanner :deputyBookList="item.deputyBookList"  @update="goToBookDetails"/>
                </div>
              </div>
              <div class="bottom-right">
                <span class="bottom-right-time" v-if="item.orderStatus === 'pending'">剩余时间：{{ remainingTimes[index] }}</span>
                <span
                  class="bottom-right-time"
                  v-if="item.orderStatus === 'paid' && item.invoiceStatus === 0 && item.refundStatus === 0 && item.shelfState !== 3"
                  @click="applyForInvoice(item)" >申请发票</span
                >
                <span
                  class="bottom-right-time"
                  v-if="item.orderStatus === 'paid' && item.invoiceStatus !== 0 && item.refundStatus === 0 && item.shelfState !== 3"
                  @click="viewInvoice(item)" >查看发票</span
                >
                <span
                  class="text_order_pay"
                  :style="{
                    marginLeft:
                      (item.orderStatus === 'paid' && item.refundStatus === 3) ||
                      (item.orderStatus === 'paid' && item.refundStatus === 1) ||
                      item.orderStatus === 'cancelled' ||
                      item.shelfState === 3
                        ? '630px'
                        : '0',
                  }"
                  >{{ item.orderStatus === 'cancelled' ? '应付款：' : '实际支付：' }}</span
                >
                <div class="text-wrapper_6">
                  <span class="text_order_20">{{ item.payAmount }}&nbsp;元</span>
                </div>
                <el-button class="status-text_btn" v-if="item.orderStatus !== 'cancelled'" type="primary" :plain="getButtonPlain(item)"  @click="orderButtonClick(item)" >
                  {{ getButtonTextByStatus(item) }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--  -->
  <div v-else class="content-right-con img-center">
    <img src="@/assets/images/home/<USER>" style="width: 50%; margin-bottom: 100px" />
    <span style="margin-bottom: 100px; text-align: center">
      <span style="color: gray; font-size: 16px">暂时没有订单哦，快去读几本好书给自己充电吧!</span>
      <el-button type="text" @click="goIndex" style="margin-top: -2px; font-size: 16px; margin-left: 15px">去找书~</el-button>
    </span>
  </div>
  <RefundDialog
    :form="orderFormItem"
    v-model="dialogVisibleRefund"
    :is-order-business="true"
  />
  <ApplyForInvoice
    :form="orderForm"
    :businessFlg = "businessFlg"
    v-model="isApplyForInvoice"/>
  <InvoiceDetailed
    :form="invoiceDetailed"
    v-model="isInvoiceDetailed"/>
    <PaymentOrder :form="paymentOrderForm" v-model="isShowContinuePayment"/>
</template>
<script setup>
import supportingTextbooksBanner from '@/views/book/components/supportingTextbooksBanner/smallBanner.vue';
// 引入图片
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { Search, Delete } from '@element-plus/icons-vue';
import { getOrderInfo, cancelDtbBookOrder, updateOrder } from '@/api/shop/order';
import { gotoReader } from '@/utils/reader'
import { ElNotification } from 'element-plus';
import { addRefundOrder } from '@/api/shop/refundOrder';
import RefundDialog from '@/components/Refund/index.vue';
import ApplyForInvoice from '@/components/ApplyForInvoice/index.vue';
import InvoiceDetailed from '@/components/InvoiceDetailed/index.vue';
import { ElMessageBox } from 'element-plus';
import { getInfoEducation } from '@/api/shop/apply';
import bookCoverDefault from '@/assets/images/book-cover-default.png';
// import { encryptData } from '@/utils/encrypt.js';
// import useSiteStore from '@/store/modules/site'
// import { decryptData } from '@/utils/encrypt.js';
const router = useRouter();
const route = useRoute();
let orderNo = route.query.orderNo;
let orderId = route.query.key;
const activeIndex = ref('1');
const items = ref(['Item 1', 'Item 2', 'Item 3', 'Item 4', 'Item 5', 'Item 6']);
const selParm = ref('');
const startDate = ref(null);
const endDate = ref(null);
const orderData = ref([]);
const isApplyForInvoice = ref(false);
const businessFlg = ref(0);
const isInvoiceDetailed = ref(false);
const orderForm = ref({});
const invoiceDetailed = ref({});
let currentBookItemIndex = ref(0);
const hoverBookIndex = ref(); // 鼠标悬停的教材索引
// 计算剩余时间
const remainingTimes = ref([]);
const intervalIds = ref([]);
const param = reactive({
  startDate: null,
  endDate: null,
  selParm: '',
  orderstatus: null,
  refundStatus: null,
  orderNo:null
});
const isShowContinuePayment = ref(false);
const orderFormItem = ref({
});
const dialogVisibleRefund = ref(false);
const paymentOrderForm = ref({});
// 当前年份
const currentYear = new Date().getFullYear();
// 下拉框选项：今年内订单 + 近五年年份
const yearsData = computed(() => {
  const years = [];
  for (let i = 1; i < 5; i++) {
    years.push(currentYear - i);
  }
  return ['今年内订单', ...years];
});

// 选中的值
const filterDate = ref('今年内订单');
import PaymentOrder  from '@/components/payment/paymentOrder.vue';
// 处理选项变化
const handleFilterChange = (value) => {
  if (value === '今年内订单') {
    // 如果是“今年内订单”，计算今年的开始和结束日期
    startDate.value = new Date(currentYear, 0, 1, 0, 0, 0); // 今年1月1日 00:00:00
    endDate.value = new Date(currentYear, 11, 31, 23, 59, 59); // 今年12月31日 23:59:59
  } else {
    // 如果是具体年份，计算该年的开始和结束日期
    const year = parseInt(value, 10);
    startDate.value = new Date(year, 0, 1, 0, 0, 0); // 该年1月1日 00:00:00
    endDate.value = new Date(year, 11, 31, 23, 59, 59); // 该年12月31日 23:59:59
  }
  getInfo();
};

const getInfo = async () => {
    param.startDate = startDate.value ? formatDate(startDate.value) : null,
    param.endDate = endDate.value ? formatDate(endDate.value) : null,
    param.selParm = selParm.value
    param.orderNo = orderNo
  await getOrderInfo(param).then((response) => {
    response.rows.forEach((item) => {
      // 计算 dueTime，即 createDateTime 加 30 分钟
      const createDateTime = new Date(item.createTime);
      createDateTime.setMinutes(createDateTime.getMinutes() + 30);
      item.dueTime = formatDate(createDateTime);
    });
    orderData.value = response.rows;
  });
};

const handleSelect = (key, path) => {
  activeIndex.value = key;
  if(activeIndex.value === '1'){
    param.orderStatus = null
    param.refundStatus = null
  }else if(activeIndex.value === '2'){
    param.orderStatus = 'pending'
    param.refundStatus = 0
  }else if(activeIndex.value === '3'){
    param.orderStatus = 'paid'
    param.refundStatus = 0
  }else if(activeIndex.value === '4'){
    param.orderStatus = 'cancelled'
    param.refundStatus = 0
  }else if(activeIndex.value === '5'){
    param.orderStatus = 'paid'
    param.refundStatus = 1
  }else{
    param.orderStatus = 'paid'
    param.refundStatus = 3
  }
  getInfo(); // 调用后台查询接口
};

const onEnd = (event) => {
};

const goIndex = () => {
  router.push('/');
};

const delCode = (item) => {
  ElMessageBox.confirm(
    '确定要删除订单吗？',
    '提醒',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    item.deleted = 2
    updateOrder(item).then(response => {
      if (response.code === 200) {
          ElNotification({
            title: '操作提示',
            message: '删除订单成功',
            type: 'success',
          });
          getInfo();
        } else {
          ElNotification({
            title: '操作提示',
            message: '删除订单失败',
            type: 'error',
          });
        }
      })
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消',
      })
    })
};

// 辅助函数：将日期格式化为 "yyyy-MM-dd HH:mm:ss"
function formatDate(date) {
  const d = new Date(date); // 创建日期对象
  const year = d.getFullYear(); // 获取年份
  const month = String(d.getMonth() + 1).padStart(2, '0'); // 获取月份，注意月份从 0 开始，需要加 1
  const day = String(d.getDate()).padStart(2, '0'); // 获取日期
  const hours = String(d.getHours()).padStart(2, '0'); // 获取小时
  const minutes = String(d.getMinutes()).padStart(2, '0'); // 获取分钟
  const seconds = String(d.getSeconds()).padStart(2, '0'); // 获取秒
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`; // 返回格式化后的日期
}

function getColorByStatus(item) {
  if (item.orderStatus === 'paid' && item.refundStatus === 1) {
    return '#F59629';
  } else if (item.orderStatus === 'paid' && item.refundStatus === 3) {
    return '#00FF00';
  } else if (item.orderStatus === 'cancelled') {
    return '#999999';
  } else if (item.orderStatus === 'paid') {
    return '#39A184';
  } else {
    return '#0966B4';
  }
}

function getTextByStatus(item) {
  if (item.orderStatus === 'paid' && item.refundStatus === 1) {
    return '退款中';
  } else if (item.orderStatus === 'paid' && item.refundStatus === 3) {
    return '已退款';
  } else if (item.orderStatus === 'cancelled') {
    return '已取消';
  } else if (item.orderStatus === 'paid') {
    return '已购买';
  } else {
    return '待支付';
  }
}

function getButtonPlain(item) {
  return item.orderStatus !== 'pending';
}

function getButtonTextByStatus(item) {
  if (item.orderStatus === 'pending') {
    return '继续支付';
  } else if (item.orderStatus === 'paid') {
    if (item.refundStatus === 1) {
      return '退款详情';
    } else if (item.refundStatus === 3) {
      return '退款详情';
    } else if (item.shelfState === 3) {
      return '申请退款';
    } else {
      return '去学习';
    }
  }
}
function orderButtonClick(item) {
  if (item.orderStatus === 'pending') {
    paymentOrderForm.value = item;
    isShowContinuePayment.value = true;

  } else if (item.orderStatus === 'paid') {
    if (item.refundStatus === 1) {
      orderFormItem.value = item
      dialogVisibleRefund.value = true;
    } else if (item.refundStatus === 3) {
      orderFormItem.value = item
      dialogVisibleRefund.value = true;
    } else if (item.shelfState === 3){
      addRefundOrder(item).then(async (res) => {
        if (res.code === 200) {
          ElNotification({
            title: '退款申请',
            message: "款申请已成功提交，审核通过，您将通过消息中心收到通知，请留意查看。",
            type: 'warning',
            duration: 3000,
          });
          item.refundStatus = 1
          await updateOrder(item);
          getInfo(); // 调用后台查询接口
        } else {
          ElNotification({
            title: '操作提示',
            message: '退货单生成失败',
            type: 'error',
          });
        }
      });
    } else {
      gotoReader(item.bookId);
    }
  }
}
function prevBookItem() {
  if (currentBookItemIndex.value > 0) {
    currentBookItemIndex.value--;
  }
}

function hoverBookItem(index) {
  hoverBookIndex.value = index;
}

async function supportingTextbooks(item) {
  masterFlag.value = false;
  await queryBookChapterListByBookDetail({ bookId: item.bookId }).then((res) => {
    if (res.code === 200) {
      chapterData.value = res.data;
      //  Object.assign(chapterData.value, res.data);
    }
  });
}

// 更新当前配套教材索引
function updateCurrentBookItemIndex(index) {
  currentBookItemIndex.value = index;
}

function nextBookItem(item) {
  if (currentBookItemIndex.value < item.value.deputyBookList.length - 1) {
    currentBookItemIndex.value++;
  }
}

const calculateRemainingTime = (item) => {
  const now = new Date();
  const end = new Date(item.dueTime);
  const diff = end - now;
  if (diff <= 0) {
    return '00:00:00';
  }
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
};
const startCountdown = () => {
  intervalIds.value.forEach((id) => clearInterval(id)); // 清除之前的定时器
  intervalIds.value = []; // 清空定时器数组

  orderData.value.forEach((item, index) => {
    const intervalId = setInterval(async () => {
      if (item.orderStatus === 'pending') {
        remainingTimes.value[index] = await calculateRemainingTime(item);
        if (remainingTimes.value[index] === '00:00:00') {
          clearInterval(intervalId);
          cancelDtbBookOrder(item).then(response => {
          if (response.code === 200) {
              ElNotification({
                title: '操作提示',
                message: '订单已取消',
                type: 'success',
              });
              getInfo();
            } else {
              ElNotification({
                title: '操作提示',
                message: '取消订单失败',
                type: 'error',
              });
            }
          });
        }
      }
    }, 1000);
    intervalIds.value.push(intervalId); // 存储定时器 ID
  });
};
// 取消订单
const cancellationOfOrder = (item) => {
  ElMessageBox.confirm(
    '确定取消订单吗？',
    '提醒',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    cancelDtbBookOrder(item).then(response => {
      if (response.code === 200) {
          ElNotification({
            title: '操作提示',
            message: '取消订单成功',
            type: 'success',
          });
          getInfo();
        } else {
          ElNotification({
            title: '操作提示',
            message: '取消订单失败',
            type: 'error',
          });
        }
      })
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消',
      })
    })
};

function applyForInvoice(item) {
  orderForm.value.orderNo = item.orderNo
  orderForm.value.orderId = item.orderId
  orderForm.value.invoiceAmount = item.payAmount
  isApplyForInvoice.value = true
}

async function viewInvoice(item) {
  const res = await getInfoEducation({ orderId: item.orderId });
  if (res.code === 200) {
    invoiceDetailed.value = res.data[0];
    isInvoiceDetailed.value = true
  }
}
async function goToBookDetails(book){
  // 获取后端的公钥
  // const publicKey = useSiteStore().publicKey;
  // if (!publicKey) {
  //   console.error('无法获取公钥');
  //   return;
  // }

  // 使用公钥加密数据
    if(book.masterBookId){
      // const masterBookId = await encryptData(book.masterBookId, publicKey);
      // const bookId  = await encryptData(book.bookId, publicKey);
      router.push({ path: '/book-detail', query: { key: book.masterBookId, assistantKey: book.bookId } });
    } else {
      // const { encryptedData } = await encryptData(book.bookId, publicKey);
      router.push({ path: '/book-detail', query: { key: book.bookId } });
    }

    // let deputyBookId;
    // if(book.masterBookId){
    //   const { id } = await encryptData(book.masterBookId, publicKey);
    //   deputyBookId = book.masterBookId
    // }

}
onUnmounted(() => {
  intervalIds.value.forEach(id => clearInterval(id)); // 清除所有定时器
  intervalIds.value = []; // 清空定时器数组
});
const isShow =ref(false);
onMounted(async () => {
  // const orderIdDecrypt= await decryptData({ encryptedData: orderId });
  param.orderId = orderId
  await getInfo();
  orderNo = null
  param.orderId = null
  if(orderData.value.length > 0){
    isShow.value = true;
  }
  startCountdown(); // 启动倒计时
});
</script>

<style scoped lang="scss">
@import '@/assets/styles/index.scss';
.content-right-con {
  width: 1239px;
  min-height: 700px;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(182, 182, 182, 1);
  margin-left: -70px;
  .right-top-con-order {
    width: 98%;
    border-bottom: 1px solid #e5e6e7;
    margin-bottom: 10px;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: center;
    .menu-con {
      margin-left: 57px;
      .el-menu-demo {
        width: 600px;
        border-bottom: none !important;
      }
    }
    .filter-con {
      margin-right: 61px;
    }
  }
  .order-list-con {
    width: 100%;
    min-height: 550px;
    @extend .base-flex-row;
    justify-content: center;
    .order-list {
      width: 96%;
      .order-item {
        width: 100%;
        @extend .base-flex-column;
        justify-content: flex-start;
        align-items: stretch;
        margin-bottom: 20px;
        .item-top {
          width: 100%;
          background-color: #f3f3f3;
          @extend .base-flex-row;
          justify-content: space-between;
          align-items: center;
          .top-left {
            padding-left: 10px;
            @extend .base-flex-row;
            justify-content: flex-start;
            align-items: center;
            .order-time {
              color: rgba(153, 153, 153, 1);
              font-size: 14px;
              font-weight: normal;
              text-align: left;
              line-height: 50px;
              margin-right: 50px;
            }
            .order-num {
              color: rgba(153, 153, 153, 1);
              font-size: 14px;
              font-weight: normal;
              text-align: left;
              line-height: 50px;
            }
          }
          .top-right {
            .cancel-order {
              /* 将“取消订单”按钮推到最右边 */
              color: rgba(153, 153, 153, 1);
              font-size: 14px;
              font-weight: normal;
              line-height: 50px;
              margin-right: 15px;
              cursor: pointer;
            }
          }
        }
        .item-middle-bottom-con {
          width: 100%;
          border: 1px solid #e5e6e7;
          @extend .base-flex-column;
          justify-content: flex-start;
          align-items: stretch;
          .item-middle {
            width: 100%;
            padding-top: 10px;
            padding-left: 30px;
            @extend .base-flex-row;
            justify-content: space-between;
            align-items: center;
            .middle-left {
              padding-top: 15px;
              @extend .base-flex-row;
              justify-content: flex-start;
              align-items: center;
              .book-img {
                margin-right: 20px;
                height: 110px;
                width: 85px;
                .img {
                  width: 100%;
                  height: 100%;
                  border-radius: 2px;
                  // 阴影
                  box-shadow: 0px 10px 12px 0px rgba(182, 182, 182, 1);
                }
              }
              .book-info {
                @extend .base-flex-column;
                justify-content: center;
                align-items: flex-start;
                .book-name {
                  color: rgba(51, 51, 51, 1);
                  font-size: 16px;
                  font-family: PingFangSC-Medium;
                  font-weight: 500;
                  text-align: left;
                }
                .book-isbn {
                  color: rgba(153, 153, 153, 1);
                  font-size: 14px;
                  font-weight: normal;
                  text-align: left;
                  line-height: 65px;
                }
                .book-price {
                  color: rgba(51, 51, 51, 1);
                  font-size: 14px;
                  font-weight: normal;
                  text-align: left;
                }
              }
            }
            .middle-right {
              margin-right: 30px;
              .order-state-text {
                color: rgba(9, 102, 180, 1);
                font-size: 14px;
                font-weight: normal;
                text-align: left;
              }
            }
          }
          .item-bottom {
            width: 100%;
            padding-bottom: 10px;
            padding-right: 20px;
            @extend .base-flex-row;
            justify-content: space-between;
            align-items: flex-end;
            .bottom-left {
              width: 45%;
              padding-left: 120px;
              .banner-con {
                padding-top: 15px;
              }
            }
            .bottom-right {
              width: 55%;
              @extend .base-flex-row;
              justify-content: flex-end;
              align-items: center;
              .bottom-right-time {
                width: 183px;
                color: rgb(245, 150, 41);
                height: 50px;
                overflow-wrap: break-word;
                font-size: 14px;
                font-weight: normal;
                text-align: left;
                line-height: 50px;
                margin-right: 35px;
                cursor: pointer;
              }
              .text_order_pay {
                min-width: 70px;
                height: 50px;
                overflow-wrap: break-word;
                color: rgba(102, 102, 102, 1);
                font-size: 14px;
                font-weight: normal;
                text-align: left;
                line-height: 50px;
              }
              .text-wrapper_6 {
                width: 68px;
                height: 50px;
                overflow-wrap: break-word;
                font-size: 0;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                line-height: 50px;
                margin-right: 30px;
              }
              .text_order_20 {
                display: inline-block;
                width: 168px;
                height: 50px;
                overflow-wrap: break-word;
                color: rgba(9, 102, 180, 1);
                font-size: 20px;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                line-height: 50px;
              }
              .status-text_btn {
                width: 120px;
                height: 35px;
                overflow-wrap: break-word;
                font-size: 14px;
                font-weight: normal;
                text-align: right;
                line-height: 50px;
              }
            }
          }
        }
      }
    }
  }
}

.text-wrapper_17 {
  background-color: rgba(243, 243, 243, 1);
  height: 46px;
  width: 1155px;
  position: relative;
  margin: 0px 0 0 41px;
  display: flex;
  flex-direction: column;
  /* 保持原有的列布局 */
}

.cancel-order {
  margin-left: auto;
  /* 将“取消订单”按钮推到最右边 */
  color: rgba(153, 153, 153, 1);
  font-size: 14px;
  font-weight: normal;
  line-height: 50px;
}

.group_22 {
  background-color: rgba(255, 255, 255, 1);
  height: 156px;
  border: 1px solid rgba(229, 230, 231, 1);
  width: 1155px;
  position: relative;
  margin: 0px 0 0 41px;
}

.text-wrapper_5 {
  width: 173px;
  height: 50px;
  margin: 39px 0 0 131px;
}

.text_17 {
  width: 173px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  line-height: 65px;
}

.section_3 {
  width: 1087px;
  height: 50px;
  margin: 3px 0 14px 35px;
}

.section_4 {
  width: 76px;
  height: 7px;
  background-size: 89px 22px;
  margin-left: 90px;
}

.text_order_18 {
  width: 73px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  margin-left: -68px;
}

.text_order_21 {
  width: 183px;
  color: rgb(245, 150, 41);
  height: 50px;
  overflow-wrap: break-word;
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  line-height: 50px;
  margin-left: 430px;
}

.text_order_19 {
  width: 70px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(102, 102, 102, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  line-height: 50px;
}

.text-wrapper_6 {
  width: 68px;
  height: 50px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  line-height: 50px;
}

.text_order_20 {
  display: inline-block;
  width: 168px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 20px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  line-height: 50px;
}

.section_5 {
  background-color: rgba(9, 102, 180, 1);
  border-radius: 4px;
  width: 98px;
  height: 34px;
  margin: 7px 0 0 30px;
}

.text-wrapper_7 {
  position: absolute;
  left: 131px;
  top: 14px;
  width: 991px;
  height: 50px;
}

.text_order_22 {
  width: 240px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 50px;
}

.text_order_23 {
  width: 42px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(9, 102, 180, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  line-height: 50px;
}

.section_6 {
  border-radius: 2px;
  height: 109px;
  width: 81px;
  position: absolute;
  left: 32px;
  top: 22px;
}

.group_23 {
  border-radius: 2px;
  width: 81px;
  height: 109px;
  border: 0.5px solid rgba(229, 230, 231, 1);
}

.text_btn {
  position: absolute;
  left: 1010px;
  top: 100px;
  width: 120px;
  height: 35px;
  overflow-wrap: break-word;
  font-size: 14px;
  font-weight: normal;
  text-align: right;
  line-height: 50px;
  margin: 4px 12px 0 5px;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.text_48 {
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  line-height: 50px;
  margin: -4px 0 0 14px;
}

.img-center {
  display: flex;
  flex-direction: column;
  /* 垂直排列子元素 */
  justify-content: center;
  /* 垂直居中 */
  align-items: center;
  /* 水平居中 */
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.image-container {
  position: relative;
  width: 100%; /* 根据需要调整宽度 */
  height: 100%; /* 根据需要调整高度 */
  cursor: pointer;
}

.recall-icon {
  position: absolute;
  left: 4px;
  top: 45px;
}

.cover-image {
  width: 100%;
  height: 100%;
}
.disabled-div {
    background-color: #ccc; /* 浅灰色背景 */
    color: #666; /* 浅灰色文字 */
    border: 1px solid #aaa; /* 浅灰色边框 */
    cursor: not-allowed; /* 禁止点击光标 */
    opacity: 0.3; /* 降低透明度 */
    pointer-events: none; /* 使按钮不可点击 */
}
/* 水平垂直居中 */
.custom-notification {
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  right: auto !important; /* 覆盖默认的 right 定位 */
}

/* 可选：去除默认的右侧边距 */
.el-notification {
  margin-right: 0 !important;
}
</style>
