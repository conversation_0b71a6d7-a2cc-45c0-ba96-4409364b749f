<template>
  <div class="content-right-con">
    <div v-if="!dialogVisible">
      <div style="margin-left: 950px;margin-bottom: 10px;">
        <el-input v-model="title" style="width: 240px" placeholder="请输入关键词">
          <template #append>
            <el-button @click="getList()" :icon="Search" />
          </template>
        </el-input>
      </div>
      <el-table :data="tableList" v-loading="loading" style="width: 100%;height: 550px;">
        <!-- 动态列 -->
        <el-table-column v-for="column in columns" :key="column.prop" :prop="column.prop" :label="column.label"
          :width="column.width">
          <!-- 特殊处理“处理状态”列 -->
          <template #default="scope" v-if="column.prop === 'status'">
            <span :class="getStatusClass(scope.row.status)">
              {{ scope.row.status }}
            </span>
          </template>
        </el-table-column>
        <!-- 操作列 -->
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type='text' @click="handleEdit(scope.$index, scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
    <!-- 详情对话框 -->
    <div v-if="selectedRow && dialogVisible">
    <el-button type="text" plain size="large" @click="dialogVisible = false" style="padding: 0px; color: black; font-size: 16px;">
      <el-icon>
        <ArrowLeft />
      </el-icon>返回
    </el-button>
    <el-card style="margin-top: 20px;">
      <template #header>
        <h4 class="label">意见反馈详情</h4>
      </template>
      <el-row style="margin-bottom: 10px;">
        <el-col :span="14">
          <span class="label">反馈编号:</span> <span class="content">{{ selectedRow.ticketId }}</span>
        </el-col>
        <el-col :span="4">
          <span class="label">处理状态:</span> <span class="content">{{ selectedRow.status }}</span>
        </el-col>
        <el-col :span="6" v-if="selectedRow.status !== '未处理'">
          <span class="label">处理时间:</span> <span class="content">{{ selectedRow.createTime }}</span>
        </el-col>
      </el-row>
      <el-row style="margin-bottom: 10px;">
        <el-col :span="24">
          <span class="label">反馈标题:</span> <span class="content">{{ selectedRow.title }}</span>
        </el-col>
      </el-row>
      <el-row style="margin-bottom: 10px;">
        <el-col :span="24">
          <span class="label">反馈内容:</span> <span class="content">{{ selectedRow.description }}</span>
        </el-col>
      </el-row>
      <el-row style="margin-bottom: 10px;">
        <el-col :span="24" >
          <span class="label">反馈回馈:</span> <span class="content">{{ selectedRow.processDescription }}</span>
        </el-col>
      </el-row>
    </el-card>
  </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted } from 'vue';
import { listOrder } from "@/api/message/order";
import { ArrowLeft } from '@element-plus/icons-vue';
import { Search } from '@element-plus/icons-vue'
// import { decryptData } from '@/utils/encrypt.js';
// 获取当前路由对象
const route = useRoute();
const ticketId = route.query.key;
const total = ref(0);
const tableList = ref([]);
const loading = ref(false);
const dialogVisible = ref(false); // 控制对话框的显示和隐藏
const selectedRow = ref(null); // 存储选中的行数据
const title = ref('');
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ticketId: '',
    title: ''
  }
});

const { queryParams } = toRefs(data);

// 列配置
const columns = ref([
  { prop: 'ticketId', label: '反馈编号', width: 200 },
  { prop: 'title', label: '标题', width: 500 },
  { prop: 'createTime', label: '提交时间', width: 200 },
  { prop: 'status', label: '处理状态' }
]);

// 获取状态对应的类名
const getStatusClass = (status) => {
  switch (status) {
    case '未处理':
      return 'status-unprocessed';
    case '已处理':
      return 'status-processed';
    case '处理中':
      return 'status-processing';
    default:
      return '';
  }
};

// 处理编辑操作
const handleEdit = (index, row) => {
  selectedRow.value = row; // 将选中的行数据赋值给 selectedRow
  dialogVisible.value = true; // 显示对话框
};

// 查询表集合
const getList = () => {
  loading.value = true;
  queryParams.value.title = title.value;
  listOrder(queryParams.value)
    .then(res => {
      if (res.code === 200) {
        res.rows.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
        res.rows.forEach(element => {
          element.status = element.status === 'pending' ? '未处理' : element.status === 'resolved' ? '已处理' : '处理中';
        });
        tableList.value = res.rows;
        if(ticketId != null){
          selectedRow.value = tableList.value[0];
          dialogVisible.value = true; // 显示对话框
        }
        total.value = res.total;
        loading.value = false;
      }
    });
};
onMounted(async () => {
  queryParams.value.ticketId = ticketId;
  await getList();
  queryParams.value.ticketId = null
});
</script>

<style scoped lang="scss">
@import '@/assets/styles/index.scss';

.content-right-con {
  width: 1239px;
  min-height: 700px;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(182, 182, 182, 1.0);
  ;
  margin-left: -90px;
}

.status-unprocessed {
  color: red;
}

.status-processed {
  color: green;
}

.status-processing {
  color: blue;
}

/* 对话框样式 */
.el-dialog__body {
  p {
    margin: 10px 0;
  }

  strong {
    font-weight: bold;
  }
}
.label {
  font-weight: bold; /* 加粗标题 */
  color: #333; /* 深色标题 */
}

.content {
  font-weight: normal; /* 正常内容 */
  color: #666; /* 浅色内容 */
}
</style>