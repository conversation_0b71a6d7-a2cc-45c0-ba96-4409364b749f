<template>
  <div class="content-right-con">
    <div class="right-top-con">
      <div class="menu-con">
        <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal">
          <el-menu-item index="1">发票抬头管理</el-menu-item>
        </el-menu>
      </div>
      <el-icon style="margin-left: 400px;">
        <Plus />
      </el-icon>
      <el-button type='text' link style="margin-right: 61px; font-size: 14px;" @click="addTitle">添加发票抬头</el-button>
    </div>
    <div class="right-bottom-con">
      <div v-for="(group, groupKey) in groupedTitles" :key="groupKey" class="invoice-Title-management">
        <h4 style="font-weight: bold;">
          {{ getInvoiceTitle(group[0]) }}
        </h4>
        <div v-for="(element, index) in group" :key="index" style="margin-bottom: 20px;">
          <el-row>
            <el-col :span="22">
              <span>{{ element.titleName }}</span>
            </el-col>
            <el-col :span="2">
              <el-button type="text" @click="editTitle(element)">编辑</el-button>
              <el-button type="text" @click="deleteTitle(element)">删除</el-button>
            </el-col>
            <el-col :span="24">
              <span style="color: rgba(153, 153, 153, 1);">{{ element.taxNo }}</span>
            </el-col>
          </el-row>
        </div>
        <el-divider />
      </div>
    </div>
    <ApplyForInvoice :form="invoiceForm" :businessFlg="businessFlg" v-model="dialogVisible"
      @update-completed="getInfo" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import ApplyForInvoice from '@/components/ApplyForInvoice/index.vue';
import { getInvoiceTitleEducation, removeEducation } from '@/api/shop/title';
import { ElMessageBox, ElNotification } from 'element-plus';
const businessFlg = ref(1);
const activeIndex = ref('1');
const invoiceForm = ref({
  orderNo: '',
  invoiceAmount: '',
  invoiceType: '',
  titleType: '',
  applyType: '',
  titleName: '',
  taxNo: '',
  registAddress: '',
  registTel: '',
  accountBank: '',
  accountNo: '',
  userEmail: '',
  titleId: ''
});
const dialogVisible = ref(false);
const currentTitle = ref([]);

const editTitle = (title) => {
  businessFlg.value = 2;
  invoiceForm.value =  JSON.parse(JSON.stringify(title));
  dialogVisible.value = true;
};

const deleteTitle = (title) => {
  ElMessageBox.confirm('是否确定删除发票抬头？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    removeEducation([title.titleId]).then((res) => {
      if (res.code === 200) {
        ElNotification({
          title: '操作提示',
          message: '删除成功',
          type: 'success',
        });
        getInfo();
      } else {
        ElNotification({
          title: '操作提示',
          message: '删除失败',
          type: 'error',
        });
      }
    });
  })
};

const addTitle = () => {
  businessFlg.value = 1;
  dialogVisible.value = true;
};

const getInfo = () => {
  getInvoiceTitleEducation({}).then(res => {
    if (res.code === 200) {
      currentTitle.value = res.data;
    }
  });
};

getInfo();

const groupedTitles = computed(() => {
  return currentTitle.value.reduce((acc, element) => {
    const key = `${element.invoiceType}-${element.titleType}`;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(element);
    return acc;
  }, {});
});

const getInvoiceTitle = (element) => {
  if (element.invoiceType === 2) {
    return '专用发票抬头';
  } else if (element.invoiceType === 1 && element.titleType === 2) {
    return '普通发票抬头-单位';
  } else {
    return '普通发票抬头-个人';
  }
};
</script>

<style scoped lang="scss">
@import '@/assets/styles/index.scss';

.content-right-con {
  width: 1239px;
  min-height: 750px;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(182, 182, 182, 1.0);
  ;
  margin-left: -90px;
}

.right-bottom-con {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.right-top-con {
  width: 100%;
  border-bottom: 1px solid #e5e6e7;
  margin-bottom: 10px;
  @extend .base-flex-row;
  align-items: center;

  .menu-con {
    margin-left: 57px;

    .el-menu-demo {
      width: 600px;
      border-bottom: none !important;
    }
  }

  .filter-con {
    margin-right: 61px;
  }
}

.invoice-Title-management {
  width: 1065px;
  margin: 0px 0px 0px 80px;
}
</style>