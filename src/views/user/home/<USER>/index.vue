<template>
  <div v-if="tabData.length > 0" class="content-right-con">
    <div class="right-top-con">
      <div class="menu-con">
        <el-menu
          :default-active="activeIndex"
          class="el-menu-demo"
          mode="horizontal"
        >
          <el-menu-item index="1">我的收藏</el-menu-item>
        </el-menu>
      </div>
    </div>
    <div style="height: 550px; overflow: auto">
      <div class="right-bottom-con">
        <div
          class="group_browsingHistory flex-col"
          v-for="(o, index) in tabData"
          :key="index"
        >
          <div class="section_10 flex-col"></div>
          <span class="text_19">{{ o.bookName }}</span>
          <div class="section_11 flex-col">
            <div class="box_22 flex-col">
              <img
                referrerpolicy="referrer"
                style="width: 122px; height: 176px"
                :src="o.cover ? o.cover : bookCoverDefault"
              />
            </div>
          </div>
          <div class="section_12 flex-col">
            <div class="section_13 flex-row">
              <div
                class="image-text_3 flex-row justify-between"
                @click="deleteSelectedItems(o.bookId)"
              >
                <img
                  class="thumbnail_43"
                  referrerpolicy="referrer"
                  :src="isCollected ? cancelCollection : collects"
                />
                <span class="text-group_1">{{
                  isCollected ? "取消收藏" : "添加收藏"
                }}</span>
              </div>
            </div>
          </div>
          <div class="section_14 flex-row">
            <span
              class="text_20"
              @click="handleViewProfile(o.bookId)"
              @loading="readLoading"
              >查看主页</span
            >
          </div>
          <div class="section_15 flex-row">
            <span
              class="text_21"
              @click="handleContinueReading(o.bookId)"
              @loading="readLoading"
              >继续阅读</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else class="content-right-con img-center">
    <img
      src="@/assets/images/home/<USER>"
      style="width: 50%; margin-bottom: 100px"
    />
    <span style="margin-bottom: 100px; text-align: center">
      <span style="color: gray; font-size: 16px">您的收藏空空如也，</span>
      <el-button
        type="text"
        @click="goIndex"
        style="margin-top: -1px; font-size: 16px"
        >去找书~</el-button
      >
    </span>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from "vue";
import { useRouter } from "vue-router";
import { listCollect, delCollect } from "@/api/book/collect";
import { ElNotification } from "element-plus";
import { gotoReader } from "@/utils/reader";
import useSiteStore from "@/store/modules/site";
import bookCoverDefault from "@/assets/images/book-cover-default.png";
import cancelCollection from "@/assets/icons/svg/cancel-collection.svg";
import collects from "@/assets/icons/svg/collects.svg";
// import { encryptData } from '@/utils/encrypt.js';

const checkShow = ref(false);
const router = useRouter();
const tabData = ref([]); // 存储书籍数据及选择状态
const isCollected = ref(true);
const { proxy } = getCurrentInstance();
const activeIndex = ref("1");
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 9999,
  },
  totalPageSize: 10,
});
const fetchData = async () => {
  const response = await listCollect(data.queryParams);
  if (response.code === 200) {
    tabData.value = response.rows.map((item) => ({
      ...item,
      checked: false, // 添加选择属性
    }));
  } else {
    console.error("请求失败", response.message);
  }
};
function handleContinueReading(bookId) {
  gotoReader(bookId);
}
// 删除选中的项
const deleteSelectedItems = async (bookId) => {
  try {
    await delCollect(bookId);
    ElNotification({
      title: "取消收藏成功",
      type: "success",
    });
    // 更新列表，将已删除项移除
    tabData.value = tabData.value.filter((item) => !item.checked);
    checkShow.value = false;
  } catch (error) {
    ElNotification({
      title: "错误",
      message: `取消收藏失败: ${error.message}`,
      type: "error",
    });
  }
  fetchData();
};

const handleViewProfile = async (bookId) => {
  // const publicKey = useSiteStore().publicKey;
  //     if (!publicKey) {
  //       console.error('无法获取公钥');
  //       return;
  //     }
  //   // 使用公钥加密数据
  //   const { encryptedData } = await encryptData(bookId, publicKey);

  //   if (!encryptedData) {
  //     console.error('数据加密失败');
  //     return;
  //   }
  router.push({ path: "/book-detail", query: { key: bookId } });
};
const goIndex = () => {
  router.push("/");
};
onMounted(() => {
  fetchData();
});
</script>

<style scoped lang="scss">
@import "@/assets/styles/index.scss";

.content-right-con {
  width: 1239px;
  min-height: 700px;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(182, 182, 182, 1);
  margin-left: -90px;
}

.group_browsingHistory {
  background-color: rgba(247, 247, 247, 1);
  border-radius: 8px;
  position: relative;
  width: 216px;
  height: 250px;
  max-width: 16%;
  box-sizing: border-box;
  margin: 20px 15px 0px 20px;
  cursor: pointer;
  .section_12,
  .section_14,
  .section_15 {
    visibility: hidden;
  }

  &:hover {
    .section_12,
    .section_14,
    .section_15 {
      visibility: visible;
    }
  }
}

.section_10 {
  width: 114px;
  height: 12px;
  background-size: 128px 26px;
  margin: 185px 0 0 51px;
}

.text_19 {
  width: 190px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 50px;
  margin-left: 10px;
  white-space: nowrap; /* 确保文本不换行 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.section_11 {
  border-radius: 2px;
  height: 176px;
  width: 122px;
  position: absolute;
  left: 37px;
  top: 17px;
}

.box_22 {
  border-radius: 2px;
  width: 122px;
  height: 176px;
  border: 0.5px solid rgba(229, 230, 231, 1);
}

.section_12 {
  background-color: rgb(247 243 243 / 8%);
  border-radius: 8px 8px 0px 0px;
  position: absolute;
  left: 0;
  top: 0;
}

.section_13 {
  background-color: rgba(51, 51, 51, 0.5);
  border-radius: 4px;
  width: 92px;
  height: 32px;
  margin: 13px 0 0 105px;
}

.image-text_3 {
  width: 77px;
  height: 20px;
  margin: 6px 0 0 7px;
}

.thumbnail_43 {
  width: 16px;
  height: 16px;
  margin-top: 1px;
}

.text-group_1 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.section_14 {
  background-color: rgba(34, 34, 34, 0.4);
  position: absolute;
  left: 0;
  top: 166px;
  width: 98px;
  height: 38px;
}

.section_15 {
  background-color: rgba(34, 34, 34, 0.4);
  position: absolute;
  left: 98px;
  top: 166px;
  width: 98px;
  height: 38px;
}

.text_20 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
  margin: 8px 0 0 21px;
}

.image_3 {
  width: 1px;
  height: 38px;
  margin-left: 26px;
}

.text_21 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
  margin: 8px 26px 0 21px;
}

.image-text_3:hover,
.section_13:hover,
.section_14:hover,
.section_15:hover {
  background-color: rgba(9, 102, 180, 1);
}

.right-bottom-con {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.book-list-con {
  flex: 0 0 20%;
  max-width: 20%;
  box-sizing: border-box;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.right-top-con {
  width: 100%;
  border-bottom: 1px solid #e5e6e7;
  margin-bottom: 10px;
  @extend .base-flex-row;
  align-items: center;

  .menu-con {
    margin-left: 57px;

    .el-menu-demo {
      width: 600px;
      border-bottom: none !important;
    }
  }

  .filter-con {
    margin-right: 61px;
  }
}

:deep(.el-checkbox__inner:after) {
  border: 1px solid transparent;
  border-left: 0;
  border-top: 0;
  box-sizing: content-box;
  content: "";
  height: 11px;
  left: 7px;
  position: absolute;
  top: 1px;
  transform: rotate(45deg) scaleY(0);
  transform-origin: center;
  transition: transform 0.15s ease-in 0.05s;
  width: 3px;
}

:deep(.el-checkbox .el-checkbox__inner) {
  height: 20px;
  width: 20px;
}
.img-center {
  display: flex;
  flex-direction: column; /* 垂直排列子元素 */
  justify-content: center; /* 垂直居中 */
  align-items: center; /* 水平居中 */
}
</style>
