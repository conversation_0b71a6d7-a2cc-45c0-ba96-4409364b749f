<template>
  <div class="content-right-con">
    <div class="right-top-con">
      <div class="menu-con">
        <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect">
          <el-menu-item index="0">全部</el-menu-item>
          <el-menu-item index="1">已购买</el-menu-item>
          <el-menu-item index="3">试用教材</el-menu-item>
          <el-menu-item index="2">校本教材</el-menu-item>
        </el-menu>
      </div>
      <div class="filter-con">
        <img src="@/assets/icons/svg/del.svg" style="width: 25px" />
        <el-button style="color: black" type="text" @click="delBookshelf"
          ><span style="font-size: 15px">{{ isDelBookshelf ? '确认' : '删除' }}</span></el-button
        >
        <img src="@/assets/icons/svg/Slice-69.svg" />
        <el-button style="color: black" type="text" @click="toggleDraggable"
          ><span style="font-size: 15px">{{ isDraggable ? '保存' : '排序' }}</span></el-button
        >
        |
        <el-select v-model="sortOption" placeholder="请选择排序方式" @change="applySort">
          <el-option label="按照最新学习排序" value="latestStudy"></el-option>
          <el-option label="按照加入日期排序" value="joinDate"></el-option>
        </el-select>
      </div>
    </div>
    <!-- 全部 -->
    <div style="min-height: 550px">
      <el-alert v-for="item in orderForm" type="warning" class="alert-con" effect="dark" closable>
        <template #default>
          <div class="alert-content">
            <img src="../../../../assets/icons/svg/notice-purchase-history.svg" />
            <span class="span-class"
              >召回提醒: 您购买的教材订单编号: {{ item.orderNo }}，《 {{ item.bookName }}》正在被召回，为您提供全额退款。</span
            >
            <div class="alert-buttons">
              <el-button type="text" style="color: #e6a23c" @click="handleViewDetails(item.orderNo)">查看</el-button>
              <el-button type="text" style="color: #e6a23c" @click="handleRefund(item)">申请退款</el-button>
            </div>
          </div>
        </template>
      </el-alert>
      <div>
        <div class="book-list-con-back">
          <div
            v-for="(o, index) in items.length"
            :key="index"
            class="group_browsingHistory flex-col"
            :class="getBookshelfClass(index)"
          ></div>
          <draggable
            v-model="items"
            @end="onEnd"
            ghost-class="ghost-style"
            :move="onMove"
            tag="div"
            item-key="bookId"
            :disabled="!isDraggable"
            class="draggable-container"
          >
            <template #item="{ element, index }">
              <div class="group_browsingHistory flex-col" :key="element.bookId">
                <el-tooltip v-if="element.bookId > 0" class="box-item" effect="dark" :content="element.bookName" placement="bottom">
                  <span v-if="element.bookId > 0" class="text_19">{{ element.bookName }}</span>
                </el-tooltip>
                <div v-if="element.bookId > 0" class="section_11 flex-col">
                  <div>
                    <img
                      :class="{ 'disabled-div': element.shelfState === 3 }"
                      referrerpolicy="referrer"
                      class="box_22"
                      :src="element.cover ? element.cover : bookCoverDefault"
                      @click="draggableClick(element)"
                    />
                    <img src="@/assets/images/home/<USER>" />
                    <el-progress style="left: 66px; top: -42px; z-index: 4" :percentage="element.readRate ? element.readRate : 0" />
                  </div>
                </div>
                <div v-if="element.shelfState === 3 && element.bookId > 0" class="section_15 flex-row">
                  <img src="@/assets/images/home/<USER>" @click="handleContinueReading" />
                </div>
                <div v-else-if="element.expireDateFlg && element.bookId > 0 && element.addWay !== 1" class="section_15 flex-row">
                  <img src="@/assets/images/home/<USER>" @click="handlExpire(element)" />
                </div>
                <div v-else-if="element.currentVersionId !== element.versionId && element.bookId > 0" class="section_16 flex-row">
                  <img src="@/assets/images/home/<USER>" @click="handleViewProfile(element)" />
                </div>
                <el-checkbox
                  v-if="isDelBookshelf && element.bookId > 0"
                  v-model="element.isChecked"
                  :key="element.userBookId"
                  style="position: absolute"
                  @change="checked => handleCheckChange(checked, element)"
                />
              </div>
            </template>
          </draggable>
        </div>
        <!-- <draggable
        v-model="items"
        @end="onEnd"
        :move="onMove"
        tag="div"
        item-key="bookId"
        class="book-list-con"
        :disabled="!isDraggable"
      >
        <template #item="{ element, index }">
          <div class="group_browsingHistory flex-col" :key="element.bookId">
            <span v-if="element.bookId > 0" class="text_19">{{
              element.bookName
            }}</span>
            <div v-if="element.bookId > 0" class="section_11 flex-col">
              <div>
                <img
                  :class="{ 'disabled-div': element.shelfState === 3 }"
                  referrerpolicy="no-referrer"
                  class="box_22"
                  :src="element.cover ? element.cover : bookCoverDefault"
                  @click="draggableClick(element)"
                />
                <img src="@/assets/images/home/<USER>" />
                <el-progress
                  style="left: 66px; top: -42px; z-index: 4"
                  :percentage="element.readRate ? element.readRate : 0"
                />
              </div>
            </div>
            <div
              v-if="element.shelfState === 3 && element.bookId > 0"
              class="section_15 flex-row"
            >
              <img
                src="@/assets/images/home/<USER>"
                @click="handleContinueReading"
              />
            </div>
            <div
              v-else-if="
                element.expireDateFlg &&
                element.bookId > 0 &&
                element.addWay === 3
              "
              class="section_15 flex-row"
            >
              <img
                src="@/assets/images/home/<USER>"
                @click="handlExpire(element)"
              />
            </div>
            <div
              v-else-if="
                element.currentVersionId !== element.versionId &&
                element.bookId > 0
              "
              class="section_16 flex-row"
            >
              <img
                src="@/assets/images/home/<USER>"
                @click="handleViewProfile(element)"
              />
            </div>
          </div>
        </template>
      </draggable> -->
        <!-- <div class="book-list-con">
        <div v-for="(o, index) in 10" :key="index" class="group_browsingHistory flex-col">
          <img v-if="index % 5 === 0" src="@/assets/images/home/<USER>">
          <img v-else-if="index % 5 === 4" src="@/assets/images/home/<USER>">
          <img v-else src="@/assets/images/home/<USER>">
        </div>
      </div> -->
      </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog v-model="dialogVisible" title="修正详情">
      <div>
        <p><strong>版本号：</strong>{{ dialogData.versionNo }}</p>
        <p><strong>教材名称：</strong>{{ dialogData.bookName }}</p>
        <p><strong>版本说明：</strong>{{ dialogData.versionIntroduce }}</p>
      </div>
    </el-dialog>
    <RefundDialog :form="orderFormItem" v-model="dialogVisibleRefund" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { listBook, updateBook, delBook, updateBookVersion } from '@/api/book/userBook'
import draggable from 'vuedraggable'
import { ElNotification, ElDialog } from 'element-plus'
import { gotoReader } from '@/utils/reader'
import { useRouter } from 'vue-router'
import RefundDialog from '@/components/Refund/index.vue'
import { getOrderInfo } from '@/api/shop/order'
import { ElMessage, ElMessageBox } from 'element-plus'
import bookCoverDefault from '@/assets/images/book-cover-default.png'
const route = useRoute()
const bookId = route.query.key
const bookIdDecrypt = ref()
const router = useRouter()
const activeIndex = ref('0')
const items = ref([])
const isDraggable = ref(false)
const isDelBookshelf = ref(false)
const sortOption = ref('latestStudy')
const dialogVisible = ref(false)
const dialogData = ref({})
const dialogVisibleRefund = ref(false)
const orderForm = ref([])
const orderFormItem = ref({})
const addWay = ref()
const checkList = ref([])
// 定义占位项
const placeholderItem = {
  bookId: -1,
  bookName: '',
  cover: '',
  shelfState: 0,
  currentVersionId: 0,
  addWay: 0,
  lastSeeDate: '',
  createTime: ''
}
const handleCheckChange = (checked, element) => {
  if (checked) {
    // 选中时添加
    checkList.value.push(element.userBookId)
  } else {
    // 取消选时移除
    checkList.value = checkList.value.filter(id => id !== element.userBookId)
  }
}
const getInfo = () => {
  listBook({
    bookName: '',
    isbn: '',
    bookId: bookIdDecrypt.value ? bookIdDecrypt.value : null,
    addWay: addWay.value
  }).then(response => {
    response.data.forEach(item => {
      item.isChecked = false
      // 获取当前日期
      const today = new Date()
      if (new Date(item.expireDate) < today && item.addWay !== 1) {
        item.expireDateFlg = true
      } else {
        item.expireDateFlg = false
      }
    })
    let data = response.data
    const remainder = data.length % 5
    if (remainder !== 0) {
      const paddingCount = 5 - remainder
      for (let i = 0; i < paddingCount; i++) {
        data.push({ ...placeholderItem, bookId: -1 - i }) // 确保 bookId 唯一
      }
    }
    items.value = data
  })
}

const handleSelect = (key, path) => {
  activeIndex.value = key // 确保 key 是字符串
  addWay.value = activeIndex.value
  if (activeIndex.value === '0') {
    addWay.value = null
  }
  getInfo()
}

const onEnd = event => {
  if (!isDraggable.value) {
    items.value = items.filter(item => item.bookId > 0) // 过滤掉占位项
    // 更新排序
    const updatedItems = items.value.map((item, index) => ({
      ...item,
      sort: index + 1
    }))
    updateBook(updatedItems)
      .then(response => {
        if (response.code === 200) {
          ElNotification({
            title: '操作提示',
            message: '保存成功',
            type: 'success'
          })
        } else {
          ElNotification({
            title: '操作提示',
            message: '保存失败',
            type: 'error'
          })
        }
        getInfo()
      })
      .catch(error => {
        console.error('Error updating book:', error) // 添加错误处理
      })
  }
}

const toggleDraggable = () => {
  isDelBookshelf.value = false
  checkList.value = []
  isDraggable.value = !isDraggable.value

  if (!isDraggable.value) {
    // 更新排序
    const updatedItems = items.value.map((item, index) => ({
      ...item,
      sort: index + 1
    }))
    updateBook(updatedItems)
      .then(response => {
        if (response.code === 200) {
          ElNotification({
            title: '操作提示',
            message: '保存成功',
            type: 'success'
          })
        } else {
          ElNotification({
            title: '操作提示',
            message: '保存失败',
            type: 'error'
          })
        }
        getInfo()
      })
      .catch(error => {
        console.error('Error updating book:', error) // 添加错误处理
      })
  }
}
const delBookshelf = () => {
  if (!isDelBookshelf.value) {
    // 确认删除提示框
    ElMessageBox.confirm('注意，删除后不可恢复，是否继续删除?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      isDelBookshelf.value = !isDelBookshelf.value
      isDraggable.value = false
    })
  }
  if (isDelBookshelf.value) {
    if (checkList.value.length === 0) {
      ElMessage({
        message: '请选择要删除的教材',
        type: 'warning'
      })
    } else {
      delBook(checkList.value).then(response => {
        if (response.code === 200) {
          ElNotification({
            title: '操作提示',
            message: '删除成功',
            type: 'success'
          })
          checkList.value = []
        } else {
          ElNotification({
            title: '操作提示',
            message: '删除失败',
            type: 'error'
          })
        }
        getInfo()
      })
    }
  }
}
const applySort = () => {
  if (sortOption.value === 'latestStudy') {
    items.value.sort((a, b) => new Date(b.lastSeeDate) - new Date(a.lastSeeDate))
  } else if (sortOption.value === 'joinDate') {
    items.value.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
  }
}

const handleViewProfile = element => {
  ElMessageBox.confirm('教材已有最新修正和更新，点击更新以获取最新内容和改进', '提醒', {
    confirmButtonText: '更新',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const param = {
        userBookId: element.userBookId,
        bookId: element.bookId,
        versionId: element.currentVersionId
      }
      updateBook([param]).then(response => {
        if (response.code === 200) {
          ElNotification({
            title: '操作提示',
            message: '更新成功',
            type: 'success'
          })
          updateBookChapterId({ bookId: element.bookId })
          getInfo()
        } else {
          ElNotification({
            title: '操作提示',
            message: '更新失败',
            type: 'error'
          })
        }
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消'
      })
    })
}
// 教材更新后调用
const updateBookChapterId = async e => {
  await updateBookVersion(e)
}
const handleRefund = item => {
  orderFormItem.value = item
  dialogVisibleRefund.value = true
}
const handleContinueReading = evt => {}
const handleViewDetails = orderNo => {
  router.push({ path: '/my-order', query: { orderNo: orderNo } })
}
const handlExpire = item => {
  if (item.addWay !== 2) {
    router.push({ path: '/book-detail', query: { key: item.bookId } })
  }
}
const draggableClick = element => {
  if (element.addWay == 2 && element.expireDateFlg) {
    return
  } else if (element.expireDateFlg && element.bookId > 0 && element.addWay !== 1) {
    router.push({ path: '/book-detail', query: { key: element.bookId } })
  } else {
    gotoReader(element.bookId)
  }
}
// :move 事件处理函数
const onMove = evt => {
  const item = evt.draggedContext.element
  // 禁止占位项拖动
  if (item.bookId <= -1) {
    return false
  }
  return true
}
const getRefundOrderInfo = () => {
  getOrderInfo({ shelfState: 3 }).then(response => {
    orderForm.value = response.rows
  })
}
const getBookshelfClass = index => {
  if (index % 5 === 0) {
    return 'bookshelf-head'
  } else if (index % 5 === 4) {
    return 'bookshelf-foot'
  } else {
    return 'bookshelf-medium'
  }
}
onMounted(async () => {
  bookIdDecrypt.value = bookId
  await getInfo()
  bookIdDecrypt.value = null
  getRefundOrderInfo()
})
</script>

<style scoped lang="scss" name="purchaseHistory">
@import '@/assets/styles/index.scss';

.content-right-con {
  width: 1239px;
  min-height: 700px;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(182, 182, 182, 1);
  margin-left: -70px;

  .right-top-con {
    width: 100%;
    border-bottom: 1px solid #e5e6e7;
    margin-bottom: 10px;
    @extend .base-flex-row;
    align-items: center;

    .menu-con {
      margin-left: 57px;

      .el-menu-demo {
        width: 600px;
        border-bottom: none !important;
      }
    }

    .filter-con {
      width: 300px;
      margin-left: 260px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .right-bottom-con {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .flex-row {
    display: flex;
    flex-direction: row;
  }

  .flex-col {
    display: flex;
    flex-direction: column;
  }

  .group_browsingHistory {
    width: 20%; /* 每行显示五个项目，每个项目占 20% 宽度 */
    height: 282px; /* 与背景图高度一致 */
    position: relative;
    cursor: pointer;

    .section_14 {
      visibility: hidden;
    }

    &:hover {
      .section_14 {
        visibility: visible;
      }
    }

    .text_19 {
      margin-top: 10px;
      width: 100%;
      height: 250px;
      overflow-wrap: break-word;
      color: rgb(51, 51, 51);
      font-size: 16px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: center;
      line-height: 465px;
      @extend .base-text-ellipsis;
    }

    .section_11 {
      border-radius: 2px;
      height: 176px;
      width: 122px;
      position: absolute;
      .box_22 {
        border-radius: 2px;
        height: 176px;
        width: 122px;
        border: 0.5px solid rgba(229, 230, 231, 1);
        width: 122px;
        height: 176px;
        position: absolute;
        z-index: 1;
        left: 55px;
        top: 20px;
      }
    }

    .text_21 {
      width: 56px;
      height: 20px;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 14px;
      font-weight: normal;
      text-align: center;
      white-space: nowrap;
      line-height: 20px;
      margin: 8px 26px 0 15px;
    }
  }

  .disabled-div {
    background-color: #ccc; /* 浅灰色背景 */
    color: #666; /* 浅灰色文字 */
    border: 1px solid #aaa; /* 浅灰色边框 */
    cursor: not-allowed; /* 禁止点击光标 */
    opacity: 0.3; /* 降低透明度 */
    pointer-events: none; /* 使按钮不可点击 */
  }

  .section_10 {
    width: 114px;
    height: 12px;
    background-size: 128px 26px;
    margin: 185px 0 0 51px;
  }

  .group_9 {
    box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.3);
    background-color: rgba(12, 175, 118, 1);
    width: 73px;
    height: 26px;
    position: relative; /* 使用相对定位，使 top 生效 */
    top: -240px;

    .thumbnail_46 {
      width: 14px;
      height: 14px;
      margin: 6px 0 0 10px;
    }

    .text_update {
      width: 28px;
      height: 20px;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 14px;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 20px;
      margin: 3px 17px 0 4px;
    }
  }
}
.alert-con {
  background-color: rgba(255, 243, 226, 1);
  width: 1154px;
  height: 52px;
  margin: 10px 0 0 20px;
  .alert-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .alert-buttons {
    display: flex;
    gap: 10px; // 按钮之间的间距
  }
  // 添加关闭按钮样式
  :deep(.el-alert__close-btn) {
    color: #e6a23c; // 设置关闭按钮颜色
    margin-top: 6px;
  }
}
.span-class {
  width: 950px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  line-height: 50px;
  margin: 1px 0 0 13px;
}
.section_15 {
  position: absolute;
  left: 78px;
  top: 100px;
  width: 76px;
  height: 28px;
  z-index: 1;
}
.section_16 {
  position: absolute;
  left: 48px;
  top: 13px;
  z-index: 2;
  width: 65px;
  height: 25px;
  img {
    filter: drop-shadow(0px 8px 4px rgba(0, 0, 0, 0.25));
    opacity: 1;
  }
}
.bookshelf-head {
  width: 242px;
  height: 242px;
  background-image: url('@/assets/images/home/<USER>');
}

.bookshelf-medium {
  width: 231px;
  height: 242px;
  background-image: url('@/assets/images/home/<USER>');
}

.bookshelf-foot {
  width: 242px;
  height: 242px;
  background-image: url('@/assets/images/home/<USER>');
}

.draggable-container {
  position: absolute; /* 绝对定位，确保 draggable 组件覆盖在背景图上 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.book-list-con-back {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  position: relative; /* 确保背景图和 draggable 组件在同一位置 */
  width: 100%; /* 确保宽度占满父容器 */
}

.bookshelf-head,
.bookshelf-medium,
.bookshelf-foot {
  width: 20%; /* 每行显示五个背景图，每个占 20% 宽度 */
  height: 242px; /* 与 draggable 项目高度一致 */
}
.tooltip-base-box .box-item {
  width: 110px;
  margin-top: 10px;
}
.tooltip-base-box {
  width: 600px;
}
.tooltip-base-box .row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.tooltip-base-box .center {
  justify-content: center;
}
// 去除默认半透明背景
:deep(.ghost-style) {
  opacity: 1 !important;
  background: transparent !important;
}

// 如果仍有拖拽阴影可添加
.draggable-container {
  :deep(.sortable-chosen) {
    box-shadow: none !important;
  }

  :deep(.sortable-ghost) {
    background: transparent !important;
  }
}
</style>
