<template>
  <div class="content-right-con">
    <div class="right-top-con">
      <div class="menu-con">
        <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal">
          <el-menu-item index="1">浏览管理</el-menu-item>
        </el-menu>
      </div>
      <el-icon style="margin-left: 400px;">
        <Delete />
      </el-icon>
      <el-button v-if="checkShow" link style="margin-right: 61px; font-size: 14px;" @click="deleteSelectedItems">确认</el-button>
      <el-button v-else link style="margin-right: 61px; font-size: 14px;" @click="toggleDelete">删除</el-button>
    </div>
    <div style="min-height: 550px;">
      <div class="right-bottom-con">
        <div class="group_browsingHistory flex-col" v-for="(o, index) in tabData" :key="index">
          <div class="section_10 flex-col"></div>
          <span class="text_19">{{ o.bookName }}</span>
          <div class="section_11 flex-col">
            <div class="box_22 flex-col">
              <img referrerpolicy="referrer" style="width: 122px;height: 176px;" :src="o.cover ? o.cover : bookCoverDefault" />
            </div>
          </div>
          <div class="section_12 flex-col" v-if="checkShow">
            <el-checkbox v-model="o.checked" style="margin: 10px;" />
          </div>
          <div class="section_14 flex-row">
            <span class="text_20" @click="handleViewProfile(o.bookId)" @loading="readLoading">查看主页</span>
          </div>
          <div class="section_15 flex-row">
            <span class="text_21" @click="handleContinueReading(o.bookId)" @loading="readLoading">继续阅读</span>
          </div>
        </div>
      </div>
    </div>
    <!-- <div style="z-index: 1; margin: -240px 0px 0px 505px; position: absolute;" v-if="checkShow">
      <el-button style="width: 200px; height: 50px;" type="primary" @click="deleteSelectedItems">确定</el-button>
    </div> -->
    <div style="z-index: 1; margin: 20px 0px 0px 567px;">
      <el-button v-if="isShow" plain type="primary" @click="loadMore">加载更多</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { listPrint, delPrint } from '@/api/book/print';
import { ElNotification } from 'element-plus';
import { gotoReader } from '@/utils/reader';
import bookCoverDefault from '@/assets/images/book-cover-default.png';
// import useSiteStore from '@/store/modules/site'
// import { encryptData } from '@/utils/encrypt.js';
const activeIndex = ref('1');
const checkShow = ref(false);
const delShow = ref(false);
const router = useRouter();
const tabData = ref([]); // 存储书籍数据及选择状态
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
  totalPageSize: 10
});
const isShow = ref(true);
const fetchData = async () => {
  const response = await listPrint(data.queryParams);
  if (response.code === 200) {
    tabData.value = response.rows.map(item => ({
      ...item,
      checked: false // 添加选择属性
    }));
    if(response.total === tabData.value.length){
      isShow.value = false;
    }
    else{
      isShow.value = true;
    }
  } else {
    console.error('请求失败', response.message);
  }
};

const loadMore = () => {
  data.queryParams.pageSize = data.totalPageSize + 10;
  data.totalPageSize = data.queryParams.pageSize;
  fetchData();
};

const handleViewProfile = async (bookId) => {
    // const publicKey = useSiteStore().publicKey;
    //   if (!publicKey) {
    //     console.error('无法获取公钥');
    //     return;
    //   }
    // // 使用公钥加密数据
    // const { encryptedData } = await encryptData(bookId, publicKey);

    // if (!encryptedData) {
    //   console.error('数据加密失败');
    //   return;
    // }
    router.push({ path: '/book-detail', query: { key: bookId } });
};

const handleContinueReading = (bookId) => {
  gotoReader(bookId);
};

// 切换显示删除复选框
const toggleDelete = () => {
  checkShow.value = !checkShow.value;
};

// 删除选中的项
const deleteSelectedItems = async () => {
  const selectedItems = tabData.value.filter(item => item.checked);
  if (selectedItems.length > 0) {
    try {
      const footPrintIds = []
      for (const item of selectedItems) {
        footPrintIds.push(item.footPrintId)
      }
      await delPrint(footPrintIds);
      ElNotification({
        title: '删除成功',
        type: 'success'
      });
      // 更新列表，将已删除项移除
      tabData.value = tabData.value.filter(item => !item.checked);
      checkShow.value = false;
    } catch (error) {
      ElNotification({
        title: '错误',
        message: `删除失败: ${error.message}`,
        type: 'error'
      });
    }
  } else {
    ElNotification({
      title: '提示',
      message: '请至少选择一项进行删除',
      type: 'warning'
    });
  }
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped lang="scss">
@import '@/assets/styles/index.scss';

.content-right-con {
  width: 1239px;
  min-height: 700px;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(182, 182, 182, 1.0);;
  margin-left: -90px;
}

.group_browsingHistory {
  background-color: rgba(247, 247, 247, 1);
  border-radius: 8px;
  position: relative;
  width: 216px;
  height: 250px;
  max-width: 16%;
  box-sizing: border-box;
  margin: 20px 15px 0px 20px;
  cursor: pointer;

  .section_14,
  .section_15 {
    visibility: hidden;
  }

  &:hover {

    .section_14,
    .section_15 {
      visibility: visible;
    }
  }
}

.section_10 {
  width: 114px;
  height: 12px;
  background-size: 128px 26px;
  margin: 185px 0 0 51px;
}

.text_19 {
  width: 180px; /* 确保宽度足够 */
  white-space: nowrap; /* 防止折行 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 显示省略号 */
  color: rgba(51, 51, 51, 1);
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: center;
  line-height: 50px;
  margin-left: 10px;
}

.section_11 {
  border-radius: 2px;
  height: 176px;
  width: 122px;
  position: absolute;
  left: 37px;
  top: 17px;
}

.box_22 {
  border-radius: 2px;
  width: 122px;
  height: 176px;
  border: 0.5px solid rgba(229, 230, 231, 1);
}

.section_12 {
  background-color: rgb(247 243 243 / 8%);
  border-radius: 8px 8px 0px 0px;
  position: absolute;
  left: 0;
  top: 0;
}

.section_13 {
  background-color: rgba(51, 51, 51, 0.5);
  border-radius: 4px;
  width: 92px;
  height: 32px;
}

.image-text_3 {
  width: 77px;
  height: 20px;
  margin: 6px 0 0 7px;
}

.thumbnail_43 {
  width: 16px;
  height: 16px;
  margin-top: 1px;
}

.text-group_1 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.section_14 {
  background-color: rgba(34, 34, 34, 0.4);
  position: absolute;
  left: 0;
  top: 166px;
  width: 98px;
  height: 38px;
}

.section_15 {
  background-color: rgba(34, 34, 34, 0.4);
  position: absolute;
  left: 98px;
  top: 166px;
  width: 98px;
  height: 38px;
}

.text_20 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
  margin: 8px 0 0 21px;
}

.image_3 {
  width: 1px;
  height: 38px;
  margin-left: 26px;
}

.text_21 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
  margin: 8px 26px 0 21px;
}

.image-text_3:hover,
.section_13:hover,
.section_14:hover,
.section_15:hover {
  background-color: rgba(9, 102, 180, 1);
}

.right-bottom-con {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.book-list-con {
  flex: 0 0 20%;
  max-width: 20%;
  box-sizing: border-box;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.right-top-con {
  width: 100%;
  border-bottom: 1px solid #e5e6e7;
  margin-bottom: 10px;
  @extend .base-flex-row;
  align-items: center;

  .menu-con {
    margin-left: 57px;

    .el-menu-demo {
      width: 600px;
      border-bottom: none !important;
    }
  }

  .filter-con {
    margin-right: 61px;
  }
}

:deep(.el-checkbox__inner:after) {
  border: 1px solid transparent;
  border-left: 0;
  border-top: 0;
  box-sizing: content-box;
  content: "";
  height: 11px;
  left: 7px;
  position: absolute;
  top: 1px;
  transform: rotate(45deg) scaleY(0);
  transform-origin: center;
  transition: transform .15s ease-in .05s;
  width: 3px;
}

:deep(.el-checkbox .el-checkbox__inner) {
  height: 20px;
  width: 20px;
}
</style>