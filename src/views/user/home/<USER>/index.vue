<template>
  <div class="content-right-con" style="margin-left: -88px" v-if="isDetailShow">
    <el-breadcrumb :separator-icon="ArrowRight">
      <el-breadcrumb-item style="cursor: pointer" @click="goBack(detailData)"
        >我的消息</el-breadcrumb-item
      >
      <el-breadcrumb-item>消息详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="box_msg flex-row justify-between">
      <div class="image-wrapper_msg flex-col">
        <img
          class="label_msg"
          referrerpolicy="no-referrer"
          src="@/assets/icons/svg/small-bell.svg"
        />
      </div>
      <div class="text-wrapper_2_msg flex-col justify-between">
        <span class="text_4_msg">{{ detailData.title }}</span>
        <div class="paragraph_1_msg">
          <template
            v-for="(part, index) in detailData.parsedContent"
            :key="index"
          >
            <el-button
              v-if="part.type === 'book'"
              link
              type="primary"
              style="margin-top: -2px"
              @click="handleClick(detailData, part.name)"
            >
              《{{ part.name }}》
            </el-button>
            <el-button
              v-else-if="part.type === 'custom'"
              link
              type="primary"
              style="margin-top: -2px"
              @click="handleClick(detailData, part.name)"
            >
              【{{ part.name }}】
            </el-button>
            <el-button
              v-else-if="part.type === 'buy'"
              link
              type="primary"
              style="margin-top: -2px"
              @click="handleClick(detailData, part.name)"
            >
              {{ part.name }}
            </el-button>
            <el-button
              v-else-if="part.type === 'teacherCertification'"
              link
              type="primary"
              style="margin-top: -3px"
              @click="handleClick(detailData, part.name)"
            >
              {{ part.name }}
            </el-button>
            <span v-else>{{ part.text }}</span>
          </template>
        </div>
      </div>
    </div>
  </div>
  <div class="content-right-con" v-else>
    <div class="right-top-con">
      <div class="menu-con">
        <!-- 顶部菜单 -->
        <el-menu
          v-model="activeIndex"
          :default-active="activeIndex"
          mode="horizontal"
          class="el-menu-demo"
          @select="handleSelect"
        >
          <el-menu-item index="all">全部消息</el-menu-item>
          <el-menu-item index="read">已读消息</el-menu-item>
          <el-menu-item index="unread"
            >未读消息
            <el-badge
              v-if="unreadCount !== 0"
              :value="unreadCount"
              style="margin-bottom: 55px"
            ></el-badge>
          </el-menu-item>
        </el-menu>
      </div>
      <div class="filter-con">
        <img src="@/assets/icons/svg/mail.svg" />
        <el-button link style="color: black" @click="markAllAsRead"
          >全部已读</el-button
        >
        <img src="@/assets/icons/svg/delete.svg" />
        <el-button link style="color: black" v-if="showCheckboxes" @click="del"
          >确认删除</el-button
        >
        <el-button link style="color: black" v-else @click="toggleCheckboxes"
          >批量删除</el-button
        >
        <img src="@/assets/icons/svg/retentionAnalysis.svg" />
        <el-select
          v-model="filterType"
          size="small"
          style="width: 130px"
          placeholder="选择类型"
          clearable
          @change="filterMessages"
        >
          <el-option
            v-for="type in messageTypes"
            :key="type"
            :label="type"
            :value="type"
          ></el-option>
        </el-select>
      </div>
    </div>
    <div class="right-bottom-con">
      <el-row v-for="msg in messages" :key="msg.messageId">
        <el-col :span="20" class="checkbox-title-container">
          <el-checkbox-group
            v-model="selectedMessages"
            @change="handleCheckboxChange"
          >
            <el-checkbox
              v-if="showCheckboxes"
              :value="msg.messageId"
              style="margin: 0px 0px 0px 55px"
            >
              <span style="display: none">{{ msg.messageId }}</span>
            </el-checkbox>
          </el-checkbox-group>
          <!-- <el-checkbox v-if="showCheckboxes" v-model="selectedMessages" style="margin: 0px 0px 0px 55px;" @click="toggleCheckboxess(selectedMessages)"
                        :label="msg.messageId">
                        <span style="display: none;">{{ msg.messageId }}</span>
                    </el-checkbox> -->
          <span
            :class="!showCheckboxes ? spanText : ''"
            style="font-weight: bold"
            >{{ msg.title }}</span
          >
        </el-col>
        <el-col :span="4">
          <el-tag :type="msg.readFlag === 0 ? 'danger' : 'success'">
            {{ msg.readFlag === 0 ? "未读" : "已读" }}
          </el-tag>
        </el-col>
        <el-col :span="20" style="margin-top: 10px">
          <div style="margin-left: 78px; font-size: 14px">
            <template v-for="(part, index) in msg.parsedContent" :key="index">
              <el-button
                v-if="part.type === 'book'"
                link
                type="primary"
                style="margin-top: -2px"
                @click="handleClick(msg, part.name)"
              >
                《{{ part.name }}》
              </el-button>
              <el-button
                v-else-if="part.type === 'custom'"
                link
                type="primary"
                style="margin-top: -2px"
                @click="handleClick(msg, part.name)"
              >
                【{{ part.name }}】
              </el-button>
              <el-button
                v-else-if="part.type === 'buy'"
                link
                type="primary"
                style="margin-top: -2px"
                @click="handleClick(msg, part.name)"
              >
                {{ part.name }}
              </el-button>
              <el-button
                v-else-if="part.type === 'teacherCertification'"
                link
                type="primary"
                style="margin-top: -2px"
                @click="handleClick(msg, part.name)"
              >
                {{ part.name }}
              </el-button>
              <span
                style="cursor: pointer"
                v-else
                @click="handleRowClick(msg)"
                >{{ part.text }}</span
              >
            </template>
          </div>
        </el-col>
      <span style="font-size: 14px;margin-top: 50px;text-align: center">{{ msg.createTime }}</span>
        <el-divider />
      </el-row>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
// 分页组件
import Pagination from "@/components/Pagination";
import { getMessage, editBatch, removeBatch, getSelTitle } from "@/api/message/message";
// import { encryptData } from '@/utils/encrypt.js';
// import useSiteStore from '@/store/modules/site'
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox, ElNotification } from "element-plus";
const router = useRouter();
const total = ref(0);
const detailData = ref({});
// 当前选中的菜单项
const activeIndex = ref("all");
// 筛选条件
const filterType = ref("");
const spanText = ref("span-text");
const delIds = ref([]);
const deleLab = ref("批量删除");
const unreadCount = ref(0);

const queryParams = reactive({
  toUserType: 2,
  pageNum: 1,
  pageSize: 10,
});
// 消息数据
const messages = ref([]);
// 详情切换
const isDetailShow = ref(false);
function goBack() {
  isDetailShow.value = false;
}
function parseLinks(message) {
  const regex = /《(.*?)》|【(.*?)】|前往购买|教师认证/g;
  let match;
  const parts = [];
  let lastIndex = 0;

  while ((match = regex.exec(message)) !== null) {
    if (lastIndex < match.index) {
      parts.push({ type: "text", text: message.slice(lastIndex, match.index) });
    }
    if (match[1]) {
      parts.push({ type: "book", name: match[1] });
    } else if (match[2]) {
      parts.push({ type: "custom", name: match[2] });
    } else if (match[0] === "前往购买") {
      parts.push({ type: "buy", name: "前往购买" });
    } else if (match[0] === "教师认证") {
      parts.push({ type: "teacherCertification", name: "教师认证" });
    }
    lastIndex = regex.lastIndex;
  }

  if (lastIndex < message.length) {
    parts.push({ type: "text", text: message.slice(lastIndex) });
  }

  return parts;
}
const handleClick = async (msg, name) => {
  msg.readFlag = 1;
  editBatch([msg]);
  // // 获取后端的公钥
  // const publicKey = useSiteStore().publicKey;
  // if (!publicKey) {
  //     console.error('无法获取公钥');
  //     return;
  // }
  // const { encryptedData } = await encryptData(msg.businessId ? msg.businessId : -1, publicKey);

  // 根据消息类型执行不同的跳转
  switch (msg.title) {
    case "教材推送提醒":
      router.push({ path: "/book-detail", query: { key: msg.businessId } });
      break;
    case "教材召回提醒":
      router.push({ path: "/my-order", query: { key: msg.businessId } });
      break;
    case "教材更新提醒":
      router.push({
        path: "/purchase-history",
        query: { key: msg.businessId },
      });
      break;
    case "读者反馈提醒":
      router.push({
        path: "/myCorrection",
        query: { key: msg.businessId },
      });
      break;
    case "意见反馈提醒":
      router.push({ path: "/feedback", query: { key: msg.businessId } });
      break;
    case "购书码兑换提醒":
      router.push({
        path: "/purchase-history",
        query: { key: msg.businessId },
      });
      break;
    case "购书码到期提醒":
      if (name === "前往购买") {
        router.push({ path: "/book-detail", query: { key: msg.businessId } });
      } else {
        router.push({
          path: "/purchase-history",
          query: { key: msg.businessId },
        });
      }
      break;
    case "售后处理提醒":
      router.push({ path: "/my-order", query: { key: msg.businessId } });
      break;
    case "开票提醒":
      router.push({ path: "/my-order", query: { key: msg.businessId } });
      break;
    case "试用审核提醒":
      router.push({
        path: "/application-history",
        query: { key: msg.businessId },
      });
      break;
    case "试用到期提醒":
      if (name === "前往购买") {
        router.push({ path: "/book-detail", query: { key: msg.businessId } });
      } else {
        router.push({
          path: "/application-history",
          query: { key: msg.businessId },
        });
      }
      break;
    case "教师审核提醒":
      router.push({ path: "/identity" });
      break;
    default:
      console.warn("未知的消息类型:", msg.type);
      break;
  }
};
const filterMessages = (type) => {
  if (type === undefined) {
    queryParams.title = null;
  } else {
    queryParams.title = type;
  }
  getList();
};
const handleRowClick = (msg) => {
  detailData.value = msg;
  isDetailShow.value = true;
  detailData.value.readFlag = 1;
  editBatch([detailData.value]).then((res) => {
    if (res.code === 200) {
      getList();
    }
  });
};
const parsedMessages = computed(() => {
  return messages.value.map((message) => ({
    ...message,
    parsedContent: parseLinks(message.content),
  }));
});
// 查询表数据
function getList() {
  getMessage(queryParams).then((res) => {
    console.log("res", res.rows);
    res.rows.sort((a, b) => {
      // 先按 readFlag 排序，未读消息 (readFlag === 0) 在前
      if (a.readFlag !== b.readFlag) {
        return b.readFlag - a.readFlag;
      }
      // 如果 readFlag 相同，则按 createTime 排序
      return new Date(a.createTime) - new Date(b.createTime);
    });
    res.rows.forEach((message) => {
      message.parsedContent = parseLinks(message.content);
    });
    messages.value = res.rows;
    unreadCount.value = res.otherCount;

    console.log("messages", messages.value);

    total.value = res.total;
  });
}
const handleSelect = (key, path) => {
  queryParams.pageNum = 1;
  activeIndex.value = key;
  if (activeIndex.value === "all") {
    queryParams.readFlag = null;
    getList();
  } else if (activeIndex.value === "read") {
    queryParams.readFlag = 1;
    getList();
  } else {
    queryParams.readFlag = 0;
    getList();
  }
};

// 动态类型列表
const messageTypes = ref([]);

// 筛选后的消息
// const filteredMessages = computed(() => {
//     return messages.value.filter((msg) => {
//         const matchesType = filterType.value ? msg.title === filterType.value : true;
//         const matchesDate = filterDate.value
//             ? msg.date.startsWith(filterDate.value)
//             : true;
//         const matchesTab =
//             activeIndex.value === "all" ||
//             (activeIndex.value === "read" && msg.readFlag === 1) ||
//             (activeIndex.value === "unread" && msg.readFlag === 0);

//         return matchesType && matchesDate && matchesTab;
//     });
// });

// 存储选中的消息ID
const selectedMessages = ref([]);

// 标记所有消息为已读
const markAllAsRead = () => {
  messages.value.forEach((msg) => {
    if (msg.readFlag === 0) {
      msg.readFlag = 1;
    }
  });
  editBatch(messages.value).then((res) => {
    if (res.code === 200) {
      getList();
    }
  });
};

// 批量删除消息
const deleteSelectedMessages = () => {
  messages.value = messages.value.filter(
    (msg) => !selectedMessages.value.includes(msg.messageId)
  );
  selectedMessages.value = []; // 清空选中的消息
  showCheckboxes.value = false; // 隐藏复选框
};

// 计算未读消息数量
// const unreadCount = computed(() => {
//   return messages.value.filter((msg) => msg.readFlag === 0).length;
// });
// 控制复选框的显示
const showCheckboxes = ref(false);

// 切换复选框的显示
const toggleCheckboxes = () => {
  showCheckboxes.value = true;
};
const del = () => {
  ElMessageBox.confirm("确定删除所选消息吗?", "提醒", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      removeBatch(delIds.value).then((res) => {
        if (res.code === 200) {
          ElNotification({
            title: "操作提示",
            message: "删除消息成功",
            type: "success",
          });
          getList();
          showCheckboxes.value = false;
        } else {
          ElNotification({
            title: "操作提示",
            message: "删除消息失败",
            type: "error",
          });
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消删除",
      });
    });
};
const handleCheckboxChange = (value) => {
  delIds.value = value;
};
const getSelTitleList= () => {
  getSelTitle().then((res) => {
    messageTypes.value = res.data;
  });
}
// 初始化时触发一次过滤
onMounted(() => {
  getList();
  getSelTitleList();
});
</script>

<style scoped lang="scss">
@import "@/assets/styles/index.scss";

.message-item {
  /* 使用grid布局，第一列宽度固定为1000px，第二列自适应 */
  display: grid;
  grid-template-columns: 80%;
  /* 第一列固定1000px，第二列自适应 */
  grid-gap: 10px;
  /* 控制每个item之间的间隙 */
  align-items: center;
  /* 确保每个item垂直居中对齐 */
}

.item {
  /* 如果需要进一步的样式调整，可以在这里添加 */
  display: flex;
  align-items: center;
  width: 100px;
  /* 确保内容垂直居中 */
}

.el-tag {
  margin-left: 85px;
}

.filter-bar,
.action-bar {
  margin-bottom: 20px;
}

.message-item {
  margin: -27px 0px 0px 75px;
  /* Add spacing between messages */
}

.message-item .item {
  width: 50%;
  /* 确保每个项目占一半的宽度 */
}

.no-messages {
  margin-top: 20px;
}

.content-right-con {
  width: 1239px;
  min-height: 780px;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(182, 182, 182, 1);
  margin-left: -70px;

  .right-top-con {
    width: 100%;
    border-bottom: 1px solid #e5e6e7;
    // margin-top: -20px;
    margin-bottom: 40px;
    @extend .base-flex-row;
    justify-content: space-between;
    align-items: center;

    .menu-con {
      margin-left: 57px;

      .el-menu-demo {
        width: 600px;
        border-bottom: none !important;
      }
    }

    .filter-con {
      width: 350px;
      margin-left: 180px;
      display: flex;
      align-items: center;
      gap: 10px;
      margin-right: 30px;
      /* 可选：设置组件之间的间距 */
    }
  }
}

.right-bottom-con {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  flex-direction: column-reverse;
}

.el-menu--horizontal > .el-menu-item {
  align-items: center;
  border-bottom: 2px solid transparent;
  color: var(--el-menu-text-color);
  display: inline-flex;
  height: 100%;
  justify-content: center;
  // margin-top: 20px;
}

.retention-analysis {
  margin-top: 20px;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: row;
}

.span-text {
  margin: 0px 0px 0px 77px;
}

.box_msg {
  width: 1062px;
  height: 302px;
  margin: 39px 0 0 44px;

  .image-wrapper_msg {
    background-color: rgba(221, 240, 255, 1);
    border-radius: 50%;
    height: 58px;
    width: 58px;

    .label_msg {
      width: 26px;
      height: 26px;
      margin: 16px 0 0 16px;
    }
  }

  .text-wrapper_2_msg {
    width: 985px;
    height: 301px;
    margin-top: 1px;

    .text_4_msg {
      width: 469px;
      height: 28px;
      overflow-wrap: break-word;
      color: rgba(51, 51, 51, 1);
      font-size: 20px;
      font-family: PingFang SC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 28px;
    }

    .paragraph_1_msg {
      width: 985px;
      height: 252px;
      overflow-wrap: break-word;
      color: rgba(102, 102, 102, 1);
      font-size: 14px;
      font-family: PingFang SC-Regular;
      font-weight: normal;
      text-align: left;
      line-height: 28px;
      margin-top: 21px;
    }
  }
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

/* 自定义激活样式 */
.el-menu-demo .el-menu-item.is-active {
  color: #0966b4;
  border-bottom: 2px solid #0966b4;
}

/* 调整 badge 样式 */
.el-badge__content {
  margin-bottom: 0;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.checkbox-title-container {
  display: flex;
  align-items: center;
}
</style>
