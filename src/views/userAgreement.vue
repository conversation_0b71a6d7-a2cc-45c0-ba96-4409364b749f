<template>
  <div>
    <div v-html="articleContent" style="width: 60%;margin-left: 20%;"></div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { listArticle } from "@/api/basic/article";

const articleContent = ref('');

onMounted(async () => {
  try {
    const parm = { articleId: 3, pageNum:1, pageSize:10 };
    const response = await listArticle(parm);
    articleContent.value = response.rows[0].content;
  } catch (error) {
    console.error('获取文章内容失败', error);
  }
});
</script>