<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试Token错误处理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #66b1ff;
        }
        .code {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Token错误处理测试页面</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>这个页面用于测试简化阅读器的token错误处理功能。</p>
        <p>当token无效时，系统会：</p>
        <ul>
            <li>显示错误消息</li>
            <li>尝试关闭窗口</li>
            <li>如果无法关闭，显示确认对话框</li>
            <li>最终显示友好的错误页面</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>测试链接</h2>
        <p>点击以下链接测试不同的错误场景：</p>
        
        <div class="code">
            <!-- 无效token测试 -->
            /reader/simplified?token=invalid_token&operationFlag=true
        </div>
        <button onclick="testInvalidToken()">测试无效Token（新窗口）</button>
        <button onclick="testInvalidTokenSameWindow()">测试无效Token（当前窗口）</button>
        
        <div class="code">
            <!-- 空token测试 -->
            /reader/simplified?token=&operationFlag=true
        </div>
        <button onclick="testEmptyToken()">测试空Token（新窗口）</button>
        
        <div class="code">
            <!-- 缺少token测试 -->
            /reader/simplified?operationFlag=true
        </div>
        <button onclick="testMissingToken()">测试缺少Token（新窗口）</button>
    </div>

    <div class="test-section">
        <h2>预期行为</h2>
        <ol>
            <li><strong>所有测试</strong>：都会跳转到错误提示页面</li>
            <li><strong>错误页面</strong>：显示友好的提示信息</li>
            <li><strong>关闭功能</strong>：提供关闭页面的按钮</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>iframe测试</h2>
        <p>在iframe中测试错误处理：</p>
        <iframe 
            id="testFrame" 
            src="about:blank" 
            width="100%" 
            height="400" 
            style="border: 1px solid #ccc; border-radius: 4px;">
        </iframe>
        <br>
        <button onclick="loadInvalidTokenInFrame()">在iframe中加载无效Token</button>
        <button onclick="clearFrame()">清空iframe</button>
    </div>

    <script>
        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            console.log('收到iframe消息:', event.data);
            
            if (event.data.type === 'request_close') {
                alert('iframe请求关闭: ' + event.data.message);
                document.getElementById('testFrame').src = 'about:blank';
            }
            
            if (event.data.type === 'iframe_error') {
                console.error('iframe错误:', event.data.error);
                alert('iframe发生错误: ' + event.data.error);
            }
        });

        function testInvalidToken() {
            window.open('/reader/simplified?token=invalid_token_123&operationFlag=true', '_blank');
        }

        function testInvalidTokenSameWindow() {
            if (confirm('这将在当前窗口测试，可能无法自动关闭。继续吗？')) {
                window.location.href = '/reader/simplified?token=invalid_token_123&operationFlag=true';
            }
        }

        function testEmptyToken() {
            window.open('/reader/simplified?token=&operationFlag=true', '_blank');
        }

        function testMissingToken() {
            window.open('/reader/simplified?operationFlag=true', '_blank');
        }

        function loadInvalidTokenInFrame() {
            document.getElementById('testFrame').src = '/reader/simplified?token=invalid_token_frame&operationFlag=true';
        }

        function clearFrame() {
            document.getElementById('testFrame').src = 'about:blank';
        }

        // 页面加载完成后的提示
        window.onload = function() {
            console.log('测试页面加载完成');
            console.log('请确保简化阅读器页面已经部署并可访问');
        };
    </script>
</body>
</html>
